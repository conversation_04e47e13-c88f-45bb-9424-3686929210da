# AWS Image Builder Component: Configure Security and Outbound Firewall Rules
# This component configures Windows Firewall security rules and outbound traffic management

name: configure-firewall-security
description: Configure Windows Firewall security rules, attack prevention, and outbound traffic management
schemaVersion: 1.0
phases:
- name: build
  steps:
  - name: ConfigureSecurityRules
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Configuring security firewall rules..."

        # Block common attack ports
        Write-Host "Blocking common attack ports..."
        
        # Block Telnet (insecure protocol)
        New-NetFirewallRule -DisplayName "Block Telnet Public" -Direction Inbound -Action Block -Protocol TCP -LocalPort 23 -Profile Public -Description "Block insecure Telnet from public networks"
        
        # Block FTP (insecure protocol) from public networks
        New-NetFirewallRule -DisplayName "Block FTP Public" -Direction Inbound -Action Block -Protocol TCP -LocalPort 21 -Profile Public -Description "Block insecure FTP from public networks"
        
        # Block SNMP from public networks (information disclosure)
        New-NetFirewallRule -DisplayName "Block SNMP Public" -Direction Inbound -Action Block -Protocol UDP -LocalPort 161 -Profile Public -Description "Block SNMP from public networks"
        
        # Block NetBIOS from public networks
        New-NetFirewallRule -DisplayName "Block NetBIOS Public" -Direction Inbound -Action Block -Protocol TCP -LocalPort 139 -Profile Public -Description "Block NetBIOS from public networks"
        New-NetFirewallRule -DisplayName "Block NetBIOS Name Public" -Direction Inbound -Action Block -Protocol UDP -LocalPort 137 -Profile Public -Description "Block NetBIOS Name Service from public networks"
        New-NetFirewallRule -DisplayName "Block NetBIOS Datagram Public" -Direction Inbound -Action Block -Protocol UDP -LocalPort 138 -Profile Public -Description "Block NetBIOS Datagram from public networks"

        # Block SMB from public networks
        New-NetFirewallRule -DisplayName "Block SMB Public" -Direction Inbound -Action Block -Protocol TCP -LocalPort 445 -Profile Public -Description "Block SMB from public networks"

        # Block RDP from public networks (allow only from internal)
        New-NetFirewallRule -DisplayName "Block RDP Public" -Direction Inbound -Action Block -Protocol TCP -LocalPort 3389 -Profile Public -Description "Block RDP from public networks"

        # Block common database ports from public networks
        Write-Host "Blocking database ports from public networks..."
        $databasePorts = @(1433, 1434, 3306, 5432, 1521, 27017, 6379)
        foreach ($port in $databasePorts) {
            New-NetFirewallRule -DisplayName "Block Database Port $port Public" -Direction Inbound -Action Block -Protocol TCP -LocalPort $port -Profile Public -Description "Block database port $port from public networks"
        }

  - name: ConfigureOutboundSecurity
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Configuring outbound security rules..."

        # Allow essential outbound traffic
        Write-Host "Allowing essential outbound traffic..."
        
        # Allow DNS outbound
        New-NetFirewallRule -DisplayName "Allow DNS Outbound" -Direction Outbound -Action Allow -Protocol UDP -RemotePort 53 -Profile Any -Description "Allow outbound DNS queries"
        
        # Allow NTP outbound for time synchronization
        New-NetFirewallRule -DisplayName "Allow NTP Outbound" -Direction Outbound -Action Allow -Protocol UDP -RemotePort 123 -Profile Any -Description "Allow outbound NTP for time sync"
        
        # Allow DHCP outbound
        New-NetFirewallRule -DisplayName "Allow DHCP Outbound" -Direction Outbound -Action Allow -Protocol UDP -RemotePort 67 -Profile Any -Description "Allow outbound DHCP requests"
        
        # Allow Windows Update outbound
        New-NetFirewallRule -DisplayName "Allow Windows Update HTTP" -Direction Outbound -Action Allow -Protocol TCP -RemotePort 80 -Profile Any -Description "Allow Windows Update HTTP"
        New-NetFirewallRule -DisplayName "Allow Windows Update HTTPS" -Direction Outbound -Action Allow -Protocol TCP -RemotePort 443 -Profile Any -Description "Allow Windows Update HTTPS"
        
        # Allow LDAP outbound for domain authentication
        New-NetFirewallRule -DisplayName "Allow LDAP Outbound" -Direction Outbound -Action Allow -Protocol TCP -RemotePort 389 -Profile Domain -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow outbound LDAP to domain controllers"
        New-NetFirewallRule -DisplayName "Allow LDAPS Outbound" -Direction Outbound -Action Allow -Protocol TCP -RemotePort 636 -Profile Domain -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow outbound LDAPS to domain controllers"
        
        # Allow Kerberos outbound
        New-NetFirewallRule -DisplayName "Allow Kerberos Outbound" -Direction Outbound -Action Allow -Protocol TCP -RemotePort 88 -Profile Domain -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow outbound Kerberos to domain controllers"

  - name: ConfigureAdvancedSecurity
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Configuring advanced security settings..."

        # Configure connection security rules (IPSec)
        Write-Host "Configuring IPSec connection security..."
        try {
            # Require authentication for domain traffic (if domain joined)
            New-NetIPsecRule -DisplayName "Require Auth Domain Traffic" -InboundSecurity Require -OutboundSecurity Request -Profile Domain -Description "Require authentication for domain network traffic" -ErrorAction SilentlyContinue
        }
        catch {
            Write-Host "Could not configure IPSec rules (may not be domain joined): $($_.Exception.Message)" -ForegroundColor Yellow
        }

        # Configure stealth mode settings
        Write-Host "Configuring stealth mode settings..."
        Set-NetFirewallProfile -All -NotifyOnListen False
        Set-NetFirewallProfile -All -AllowUnicastResponseToMulticast False
        Set-NetFirewallProfile -All -EnableStealthModeForIPsec True

        # Configure additional security settings
        Write-Host "Configuring additional security settings..."
        Set-NetFirewallProfile -All -AllowLocalFirewallRules False
        Set-NetFirewallProfile -All -AllowLocalIPsecRules False

        Write-Host "Security firewall rules configured successfully"

- name: validate
  steps:
  - name: ValidateSecurityFirewallConfiguration
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Validating security firewall configuration..."

        $validationErrors = @()

        # Check if security block rules exist
        $existingRules = Get-NetFirewallRule | Where-Object { $_.Enabled -eq "True" }

        $requiredBlockRules = @(
            "Block Telnet Public",
            "Block FTP Public",
            "Block SNMP Public",
            "Block NetBIOS Public",
            "Block SMB Public",
            "Block RDP Public"
        )

        $missingBlockRules = @()
        foreach ($rule in $requiredBlockRules) {
            $foundRule = $existingRules | Where-Object { $_.DisplayName -like "*$rule*" }
            if (-not $foundRule) {
                $missingBlockRules += $rule
            }
        }

        if ($missingBlockRules.Count -gt 0) {
            $validationErrors += "Missing security block firewall rules: $($missingBlockRules -join ', ')"
        }

        # Check if essential outbound rules exist
        $requiredOutboundRules = @(
            "Allow DNS Outbound",
            "Allow NTP Outbound",
            "Allow DHCP Outbound",
            "Allow Windows Update HTTP",
            "Allow Windows Update HTTPS"
        )

        $missingOutboundRules = @()
        foreach ($rule in $requiredOutboundRules) {
            $foundRule = $existingRules | Where-Object { $_.DisplayName -like "*$rule*" }
            if (-not $foundRule) {
                $missingOutboundRules += $rule
            }
        }

        if ($missingOutboundRules.Count -gt 0) {
            $validationErrors += "Missing essential outbound firewall rules: $($missingOutboundRules -join ', ')"
        }

        # Validate firewall profile security settings
        try {
            $profiles = Get-NetFirewallProfile
            foreach ($profile in $profiles) {
                if ($profile.NotifyOnListen -eq $true) {
                    $validationErrors += "Profile $($profile.Name) should have NotifyOnListen disabled for stealth mode"
                }
                if ($profile.AllowUnicastResponseToMulticast -eq $true) {
                    $validationErrors += "Profile $($profile.Name) should have AllowUnicastResponseToMulticast disabled for security"
                }
                if ($profile.AllowLocalFirewallRules -eq $true) {
                    $validationErrors += "Profile $($profile.Name) should have AllowLocalFirewallRules disabled for centralized management"
                }
            }
        }
        catch {
            $validationErrors += "Failed to validate firewall profile security settings: $($_.Exception.Message)"
        }

        # Validate specific security rule configurations
        try {
            $blockTelnetRule = Get-NetFirewallRule -DisplayName "Block Telnet Public" -ErrorAction SilentlyContinue
            if ($blockTelnetRule) {
                if ($blockTelnetRule.Action -ne "Block") {
                    $validationErrors += "Block Telnet Public rule should have Action=Block, found: $($blockTelnetRule.Action)"
                }
                $telnetPortFilter = Get-NetFirewallPortFilter -AssociatedNetFirewallRule $blockTelnetRule
                if ($telnetPortFilter.LocalPort -ne "23") {
                    $validationErrors += "Block Telnet Public rule should be on port 23, found: $($telnetPortFilter.LocalPort)"
                }
            }

            $blockSMBRule = Get-NetFirewallRule -DisplayName "Block SMB Public" -ErrorAction SilentlyContinue
            if ($blockSMBRule) {
                if ($blockSMBRule.Action -ne "Block") {
                    $validationErrors += "Block SMB Public rule should have Action=Block, found: $($blockSMBRule.Action)"
                }
                $smbPortFilter = Get-NetFirewallPortFilter -AssociatedNetFirewallRule $blockSMBRule
                if ($smbPortFilter.LocalPort -ne "445") {
                    $validationErrors += "Block SMB Public rule should be on port 445, found: $($smbPortFilter.LocalPort)"
                }
            }
        }
        catch {
            $validationErrors += "Failed to validate specific security rule configurations: $($_.Exception.Message)"
        }

        # Report validation results
        if ($validationErrors.Count -eq 0) {
            Write-Host "VALIDATION SUCCESS: Security firewall rules configured correctly"
            Write-Host "- Attack Prevention: Common attack ports blocked from public networks"
            Write-Host "- Protocol Security: Insecure protocols (Telnet, FTP, SNMP) blocked from public"
            Write-Host "- Network Security: NetBIOS, SMB, RDP blocked from public networks"
            Write-Host "- Database Security: Database ports blocked from public networks"
            Write-Host "- Outbound Security: Essential services allowed, others controlled"
            Write-Host "- Stealth Mode: Enabled for all profiles"
            Write-Host "- Centralized Management: Local rule creation disabled"
            Write-Host "- Total security rules: $($existingRules | Where-Object { $_.DisplayName -like "*Block*" -or $_.DisplayName -like "*Allow*Outbound*" } | Measure-Object | Select-Object -ExpandProperty Count)"
            exit 0
        } else {
            Write-Error "VALIDATION FAILED: Security firewall configuration errors found:"
            foreach ($error in $validationErrors) {
                Write-Error "  - $error"
            }
            exit 1
        }
