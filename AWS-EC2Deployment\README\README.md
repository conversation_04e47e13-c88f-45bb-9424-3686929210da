# AWS EC2 Deployment Automation

This repository contains a comprehensive AWS Systems Manager Automation solution for deploying EC2 instances from ImageBuilder recipes with complete configuration management, Active Directory integration, application deployment, and system customizations.

## Overview

The EC2 Deployment Automation orchestrates the following workflow:

1. **Configuration Loading**: Loads and validates deployment configurations from S3 buckets
2. **AD Object Creation**: Creates Active Directory computer objects in the correct OU
3. **EC2 Instance Deployment**: Deploys EC2 instances from ImageBuilder recipes
4. **Domain Join**: Joins instances to the domain and configures local administrators
5. **Application Deployment**: Installs applications specific to the instance configuration
6. **System Customizations**: Applies firewall rules, registry settings, and deploys customization software

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   S3 Bucket     │    │ Secrets Manager │    │ ImageBuilder    │
│  (Configs)      │    │ (Domain Creds)  │    │   (AMIs)        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │ SSM Automation  │
                    │   Runbook       │
                    └─────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Active Directory│    │   EC2 Instance  │    │   CloudWatch    │
│   (Computer     │    │   (Deployed)    │    │    (Logs)       │
│    Objects)     │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Directory Structure

```
AWS-EC2Deployment/
├── CloudFormation/                 # CloudFormation templates
│   ├── EC2-Deployment-IAM-Roles.yaml
│   └── EC2-Deployment-SSM-Document.yaml
├── EC2/                           # EC2 deployment scripts
│   └── Deploy-EC2FromImageBuilder.ps1
├── ImageBuilder/                  # ImageBuilder configurations
├── README/                        # Documentation
│   ├── README.md
│   ├── Configuration-Guide.md
│   ├── Deployment-Guide.md
│   └── Troubleshooting-Guide.md
├── Scripts/
│   ├── Applications/              # Application deployment scripts
│   │   └── Deploy-Applications.ps1
│   ├── Configuration/             # Configuration management scripts
│   │   ├── Get-DeploymentConfiguration.ps1
│   │   ├── Test-DeploymentConfiguration.ps1
│   │   ├── New-ADComputerObject.ps1
│   │   └── Join-DomainWithLocalAdmins.ps1
│   └── Customizations/            # System customizations scripts
│       └── Apply-Customizations.ps1
├── SecretsManager/                # Secrets Manager configurations
└── SystemsManager/
    ├── Automation/                # SSM Automation runbooks
    │   ├── EC2-Deployment-Automation.yaml
    │   └── EC2-Deployment-Automation-Corrected.yaml
    └── Runbooks/                  # Additional runbooks
```

## Prerequisites

### AWS Resources Required

1. **S3 Bucket**: For storing configuration files, scripts, and applications
2. **Secrets Manager Secret**: Containing domain join credentials
3. **ImageBuilder Images**: Pre-built AMIs for deployment
4. **IAM Roles**: Automation and instance roles with appropriate permissions
5. **VPC/Subnets**: Network infrastructure for instance deployment
6. **Security Groups**: Network security configurations

### Configuration Files Required

1. **base_config.json**: Defines valid combinations of Asset Owners, App Types, Clients, etc.
2. **{AssetOwner}_{AppType}_Config.json**: Specific configurations for each combination
3. **Security configurations**: Firewall rules, registry settings, software packages

### Domain Integration

1. **Active Directory**: Domain controllers accessible from AWS
2. **Domain Service Account**: With permissions to create/manage computer objects
3. **DNS Resolution**: Proper DNS configuration for domain resolution

## Quick Start

### 1. Deploy Infrastructure

```bash
# Deploy IAM roles and policies
aws cloudformation deploy \
  --template-file CloudFormation/EC2-Deployment-IAM-Roles.yaml \
  --stack-name ec2-deployment-iam \
  --parameter-overrides \
    S3ConfigBucketName=your-config-bucket \
    SecretsManagerSecretArn=arn:aws:secretsmanager:af-south-1:account:secret:domain-creds \
  --capabilities CAPABILITY_NAMED_IAM \
  --region af-south-1

# Deploy SSM Automation Document
aws cloudformation deploy \
  --template-file CloudFormation/EC2-Deployment-SSM-Document.yaml \
  --stack-name ec2-deployment-ssm \
  --parameter-overrides \
    AutomationRoleArn=arn:aws:iam::account:role/EC2-Deployment-Automation-Role-af-south-1 \
    S3ConfigBucket=your-config-bucket \
  --region af-south-1
```

### 2. Upload Configuration Files

```bash
# Upload base configuration
aws s3 cp configs/base_config.json s3://your-config-bucket/configs/ --region af-south-1

# Upload specific configurations
aws s3 cp configs/Sanlam_Shared_Config.json s3://your-config-bucket/configs/ --region af-south-1

# Upload scripts
aws s3 sync Scripts/ s3://your-config-bucket/scripts/ --region af-south-1
```

### 3. Validate Configuration (Recommended First Step)

Before running full deployments, validate your configuration and AD integration:

```bash
# Execute Configuration and AD Validation
aws ssm start-automation-execution \
  --document-name "Config-and-AD-Validation" \
  --parameters \
    AssetOwner=Sanlam \
    AppType=Shared \
    Client=SGT \
    Environment=DEV \
    OSVersion="Windows Server 2022" \
    S3ConfigBucket=your-config-bucket \
    SecretsManagerSecretArn=arn:aws:secretsmanager:af-south-1:account:secret:domain-creds \
    TestMode=true \
  --region af-south-1
```

### 4. Execute Full Deployment

After validation passes, run the full deployment:

```bash
# Execute SSM Automation
aws ssm start-automation-execution \
  --document-name "EC2-Deployment-Automation" \
  --parameters \
    AssetOwner=Sanlam \
    AppType=Shared \
    Client=SGT \
    Environment=DEV \
    OSVersion="Windows Server 2022" \
    ImageBuilderImageArn=arn:aws:imagebuilder:af-south-1:account:image/your-image \
    InstanceType=t3.medium \
    SubnetId=subnet-******** \
    SecurityGroupIds=sg-******** \
    KeyName=your-key-pair \
    S3ConfigBucket=your-config-bucket \
    SecretsManagerSecretArn=arn:aws:secretsmanager:af-south-1:account:secret:domain-creds \
    IamInstanceProfile=EC2-Deployment-Instance-Profile-af-south-1 \
  --region af-south-1
```

## Configuration Structure

### Base Configuration (base_config.json)

Defines the valid combinations of:
- Asset Owners (Sanlam, Santam, Retail Mass)
- Application Types (Shared, MSSQL)
- Environments (PRD, PPE, DEV)
- OS Versions (Windows Server 2022, 2019, 2025)
- Clients (SGT, SPF, SC, etc.)

### Specific Configuration ({AssetOwner}_{AppType}_Config.json)

Contains detailed settings for each client/environment combination:
- Domain information
- Target OUs for different OS versions
- Local administrator groups
- Application lists
- Security configurations

## Security Considerations

1. **Least Privilege**: IAM roles follow least privilege principles
2. **Secrets Management**: Domain credentials stored securely in Secrets Manager
3. **Network Security**: Instances deployed in private subnets with appropriate security groups
4. **Encryption**: All data in transit and at rest should be encrypted
5. **Audit Logging**: All actions logged to CloudWatch for audit purposes

## Monitoring and Logging

- **CloudWatch Logs**: All script execution logs
- **SSM Automation**: Execution history and status
- **EC2 Tags**: Comprehensive tagging for tracking and management
- **CloudTrail**: API call auditing

## Support and Troubleshooting

See the following documentation for detailed guidance:
- [Configuration Guide](Configuration-Guide.md)
- [Deployment Guide](Deployment-Guide.md)
- [Troubleshooting Guide](Troubleshooting-Guide.md)

## Contributing

1. Follow the established PowerShell and YAML coding standards
2. Test all changes in a development environment
3. Update documentation for any new features or changes
4. Ensure all scripts include proper error handling and logging

## License

This project is licensed under the MIT License - see the LICENSE file for details.
