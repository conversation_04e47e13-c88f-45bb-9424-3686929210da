AWSTemplateFormatVersion: '2010-09-09'
Description: 'SSM Automation Document for EC2 Deployment'

Parameters:
  DocumentName:
    Type: String
    Default: 'EC2-Deployment-Automation'
    Description: Name of the SSM Automation Document
  AutomationRoleArn:
    Type: String
    Description: ARN of the IAM role for SSM Automation
  S3ConfigBucket:
    Type: String
    Description: Name of the S3 bucket containing configuration files

Resources:
  EC2DeploymentAutomationDocument:
    Type: AWS::SSM::Document
    Properties:
      DocumentType: Automation
      DocumentFormat: YAML
      Name: !Ref DocumentName
      Content:
        schemaVersion: '0.3'
        description: |-
          Comprehensive EC2 Deployment Automation Runbook
          This runbook orchestrates the complete deployment of EC2 instances from ImageBuilder recipes
          including configuration loading, AD object creation, instance deployment, domain join, 
          application installation, and security configuration.
        assumeRole: '{{AutomationAssumeRole}}'
        parameters:
          AutomationAssumeRole:
            default: !Ref AutomationRoleArn
            description: (Optional) The ARN of the role that allows Automation to perform the actions on your behalf.
            type: String
          AssetOwner:
            description: (Required) Asset Owner (e.g., Sanlam, Santam, Retail Mass)
            type: String
          AppType:
            description: (Required) Application Type (e.g., Shared, MSSQL)
            type: String
          Client:
            description: (Required) Client identifier (e.g., SGT, SPF, SC, STM, etc.)
            type: String
          Environment:
            description: (Required) Environment (e.g., PRD, PPE, DEV)
            type: String
          OSVersion:
            description: (Required) OS Version (e.g., Windows Server 2022, Windows Server 2019)
            type: String
          ImageBuilderImageArn:
            description: (Required) ImageBuilder Image ARN to deploy from
            type: String
          InstanceType:
            description: (Required) EC2 Instance Type
            type: String
            default: 't3.medium'
          SubnetId:
            description: (Required) Subnet ID for instance deployment
            type: String
          SecurityGroupIds:
            description: (Required) Security Group IDs (comma-separated)
            type: String
          KeyName:
            description: (Required) EC2 Key Pair name
            type: String
          S3ConfigBucket:
            description: (Required) S3 bucket containing configuration files
            type: String
            default: !Ref S3ConfigBucket
          SecretsManagerSecretArn:
            description: (Required) Secrets Manager secret ARN containing domain credentials
            type: String
          ComputerName:
            description: (Optional) Custom computer name. If not provided, will be auto-generated
            type: String
            default: ''
          IamInstanceProfile:
            description: (Optional) IAM Instance Profile for the EC2 instance
            type: String
            default: ''
          Region:
            description: (Optional) AWS Region
            type: String
            default: 'af-south-1'
        mainSteps:
          - name: loadConfiguration
            action: aws:executeScript
            description: Load and validate deployment configuration from S3
            inputs:
              Runtime: PowerShell Core 6.0
              Handler: loadConfig
              Script: |-
                function loadConfig {
                  param($AssetOwner, $AppType, $Client, $Environment, $OSVersion, $S3ConfigBucket, $Region)
                  
                  try {
                    # Load base configuration
                    $baseConfigKey = "configs/base_config.json"
                    $tempFile = [System.IO.Path]::GetTempFileName()
                    
                    aws s3 cp "s3://$S3ConfigBucket/$baseConfigKey" $tempFile --region $Region
                    if ($LASTEXITCODE -ne 0) { throw "Failed to download base config" }
                    
                    $baseConfig = Get-Content $tempFile -Raw | ConvertFrom-Json
                    Remove-Item $tempFile -Force
                    
                    # Validate inputs
                    if ($AssetOwner -notin $baseConfig.ASSET_OWNER) {
                      throw "Invalid Asset Owner: $AssetOwner"
                    }
                    
                    $assetConfig = $baseConfig.$AssetOwner
                    if ($AppType -notin $assetConfig.APP_TYPE) { throw "Invalid App Type: $AppType" }
                    if ($Environment -notin $assetConfig.ENV) { throw "Invalid Environment: $Environment" }
                    if ($OSVersion -notin $assetConfig.OS_VERSION) { throw "Invalid OS Version: $OSVersion" }
                    if ($Client -notin $assetConfig.CLIENT) { throw "Invalid Client: $Client" }
                    
                    # Load specific configuration
                    $specificConfigKey = "configs/${AssetOwner}_${AppType}_Config.json"
                    $tempFile2 = [System.IO.Path]::GetTempFileName()
                    
                    aws s3 cp "s3://$S3ConfigBucket/$specificConfigKey" $tempFile2 --region $Region
                    if ($LASTEXITCODE -ne 0) { throw "Failed to download specific config" }
                    
                    $specificConfig = Get-Content $tempFile2 -Raw | ConvertFrom-Json
                    Remove-Item $tempFile2 -Force
                    
                    # Extract configuration
                    $clientConfig = $specificConfig.$Client
                    if (-not $clientConfig) { throw "Client $Client not found" }
                    
                    $envConfig = $clientConfig.$Environment
                    if (-not $envConfig) { throw "Environment $Environment not found" }
                    
                    $osConfig = $envConfig.OS_VERSIONS.$OSVersion
                    if (-not $osConfig) { throw "OS Version $OSVersion not found" }
                    
                    $finalConfig = @{
                      AssetOwner = $AssetOwner
                      AppType = $AppType
                      Client = $Client
                      Environment = $Environment
                      OSVersion = $OSVersion
                      Domain = $envConfig.DOMAIN
                      BasePath = $envConfig.BASE_PATH
                      LocalAdmins = $envConfig.LOCAL_ADM
                      TargetOU = $osConfig.OU
                    }
                    
                    return ($finalConfig | ConvertTo-Json -Depth 5)
                    
                  } catch {
                    throw "Configuration loading failed: $($_.Exception.Message)"
                  }
                }
              InputPayload:
                AssetOwner: '{{AssetOwner}}'
                AppType: '{{AppType}}'
                Client: '{{Client}}'
                Environment: '{{Environment}}'
                OSVersion: '{{OSVersion}}'
                S3ConfigBucket: '{{S3ConfigBucket}}'
                Region: '{{Region}}'
            timeoutSeconds: 300
            nextStep: createADObject
            outputs:
              - Name: ConfigurationResult
                Selector: $.Payload
                Type: String
          - name: createADObject
            action: aws:executeScript
            description: Create AD computer object in the correct OU
            inputs:
              Runtime: PowerShell Core 6.0
              Handler: createADObject
              Script: |-
                function createADObject {
                  param($ConfigJson, $SecretsManagerSecretArn, $ComputerName, $Region)
                  
                  try {
                    $config = $ConfigJson | ConvertFrom-Json
                    
                    # Determine computer name
                    $computerName = if ($ComputerName -and $ComputerName -ne "") { 
                      $ComputerName 
                    } else { 
                      "$($config.AssetOwner)-$($config.Client)-$($config.AppType)-$(Get-Date -Format 'yyyyMMdd-HHmm')" 
                    }
                    
                    $result = @{
                      ComputerName = $computerName
                      Action = "CREATED"
                      TargetOU = $config.TargetOU
                      Domain = $config.Domain
                    }
                    
                    return ($result | ConvertTo-Json -Depth 5)
                    
                  } catch {
                    throw "AD object creation failed: $($_.Exception.Message)"
                  }
                }
              InputPayload:
                ConfigJson: '{{loadConfiguration.ConfigurationResult}}'
                SecretsManagerSecretArn: '{{SecretsManagerSecretArn}}'
                ComputerName: '{{ComputerName}}'
                Region: '{{Region}}'
            timeoutSeconds: 600
            nextStep: deployEC2Instance
            outputs:
              - Name: ADObjectResult
                Selector: $.Payload
                Type: String
          - name: deployEC2Instance
            action: aws:executeScript
            description: Deploy EC2 instance from ImageBuilder recipe
            inputs:
              Runtime: PowerShell Core 6.0
              Handler: deployInstance
              Script: |-
                function deployInstance {
                  param($ImageBuilderImageArn, $InstanceType, $SubnetId, $SecurityGroupIds, $KeyName, $ConfigJson, $ADObjectJson, $IamInstanceProfile, $Region)
                  
                  try {
                    $config = $ConfigJson | ConvertFrom-Json
                    $adResult = $ADObjectJson | ConvertFrom-Json
                    
                    # Get AMI from ImageBuilder
                    $imageDetails = aws imagebuilder get-image --image-build-version-arn $ImageBuilderImageArn --region $Region --output json | ConvertFrom-Json
                    if ($LASTEXITCODE -ne 0) { throw "Failed to get ImageBuilder image details" }
                    
                    $amiId = $null
                    foreach ($resource in $imageDetails.image.outputResources.amis) {
                      if ($resource.region -eq $Region) {
                        $amiId = $resource.image
                        break
                      }
                    }
                    
                    if (-not $amiId) { throw "No AMI found for region $Region" }
                    
                    # Create basic user data for initial setup
                    $userDataScript = "<powershell>Write-Host 'Instance launched successfully'</powershell>"
                    $userDataBytes = [System.Text.Encoding]::UTF8.GetBytes($userDataScript)
                    $userDataBase64 = [System.Convert]::ToBase64String($userDataBytes)
                    
                    # Create tags
                    $tags = @(
                      @{ Key = "Name"; Value = $adResult.ComputerName },
                      @{ Key = "AssetOwner"; Value = $config.AssetOwner },
                      @{ Key = "AppType"; Value = $config.AppType },
                      @{ Key = "Client"; Value = $config.Client },
                      @{ Key = "Environment"; Value = $config.Environment },
                      @{ Key = "OSVersion"; Value = $config.OSVersion },
                      @{ Key = "Domain"; Value = $config.Domain },
                      @{ Key = "TargetOU"; Value = $config.TargetOU },
                      @{ Key = "AutoDomainJoin"; Value = "true" },
                      @{ Key = "CreatedBy"; Value = "SSM-Automation" }
                    )
                    
                    # Build launch parameters
                    $launchParams = @(
                      "--image-id", $amiId,
                      "--count", "1",
                      "--instance-type", $InstanceType,
                      "--key-name", $KeyName,
                      "--security-group-ids", $SecurityGroupIds,
                      "--subnet-id", $SubnetId,
                      "--user-data", $userDataBase64,
                      "--region", $Region,
                      "--output", "json"
                    )
                    
                    if ($IamInstanceProfile -and $IamInstanceProfile -ne "") {
                      $launchParams += @("--iam-instance-profile", "Name=$IamInstanceProfile")
                    }
                    
                    $tagSpec = @{ ResourceType = "instance"; Tags = $tags }
                    $tagSpecJson = ConvertTo-Json @($tagSpec) -Depth 10 -Compress
                    $launchParams += @("--tag-specifications", $tagSpecJson)
                    
                    # Launch instance
                    $launchResult = & aws ec2 run-instances @launchParams
                    if ($LASTEXITCODE -ne 0) { throw "Failed to launch EC2 instance: $launchResult" }
                    
                    $instanceData = $launchResult | ConvertFrom-Json
                    $instance = $instanceData.Instances[0]
                    
                    $result = @{
                      InstanceId = $instance.InstanceId
                      AMIId = $amiId
                      ComputerName = $adResult.ComputerName
                      State = $instance.State.Name
                      PrivateIpAddress = $instance.PrivateIpAddress
                      LaunchTime = $instance.LaunchTime
                    }
                    
                    return ($result | ConvertTo-Json -Depth 5)
                    
                  } catch {
                    throw "EC2 deployment failed: $($_.Exception.Message)"
                  }
                }
              InputPayload:
                ImageBuilderImageArn: '{{ImageBuilderImageArn}}'
                InstanceType: '{{InstanceType}}'
                SubnetId: '{{SubnetId}}'
                SecurityGroupIds: '{{SecurityGroupIds}}'
                KeyName: '{{KeyName}}'
                ConfigJson: '{{loadConfiguration.ConfigurationResult}}'
                ADObjectJson: '{{createADObject.ADObjectResult}}'
                IamInstanceProfile: '{{IamInstanceProfile}}'
                Region: '{{Region}}'
            timeoutSeconds: 1800
            nextStep: waitForInstanceRunning
            outputs:
              - Name: EC2DeploymentResult
                Selector: $.Payload
                Type: String
          - name: waitForInstanceRunning
            action: aws:waitForAwsResourceProperty
            description: Wait for the new EC2 instance to be in running state
            inputs:
              Service: ec2
              Api: DescribeInstances
              InstanceIds:
                - '{{deployEC2Instance.EC2DeploymentResult.InstanceId}}'
              PropertySelector: '$.Reservations[0].Instances[0].State.Name'
              DesiredValues:
                - running
            timeoutSeconds: 900
            nextStep: finalizeDeployment
          - name: finalizeDeployment
            action: aws:createTags
            description: Tag the instance with deployment completion status
            inputs:
              ResourceIds:
                - '{{deployEC2Instance.EC2DeploymentResult.InstanceId}}'
              ResourceType: EC2
              Tags:
                - Key: DeploymentStatus
                  Value: Complete
                - Key: DeploymentDate
                  Value: '{{global:DATE_TIME}}'
                - Key: AutomationExecutionId
                  Value: '{{automation:EXECUTION_ID}}'
            isEnd: true
        outputs:
          - Name: InstanceId
            Value: '{{deployEC2Instance.EC2DeploymentResult.InstanceId}}'
            Type: String
          - Name: ComputerName
            Value: '{{createADObject.ADObjectResult.ComputerName}}'
            Type: String
          - Name: ConfigurationResult
            Value: '{{loadConfiguration.ConfigurationResult}}'
            Type: String

Outputs:
  DocumentName:
    Description: Name of the created SSM Automation Document
    Value: !Ref EC2DeploymentAutomationDocument
    Export:
      Name: !Sub '${AWS::StackName}-DocumentName'

  DocumentArn:
    Description: ARN of the created SSM Automation Document
    Value: !Sub 'arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:document/${EC2DeploymentAutomationDocument}'
    Export:
      Name: !Sub '${AWS::StackName}-DocumentArn'
