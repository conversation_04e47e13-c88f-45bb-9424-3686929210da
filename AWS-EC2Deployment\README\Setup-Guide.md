# AWS EC2 Deployment Automation - Complete Setup Guide

This guide provides step-by-step instructions for setting up the AWS EC2 Deployment Automation system using the AWS web console. No AWS CLI access required.

## 📋 Prerequisites

Before starting, ensure you have:
- AWS account with administrative access
- Active Directory domain (on-premises or AWS Managed Microsoft AD)
- Domain service account with computer object creation permissions
- Basic understanding of AWS services (S3, IAM, Systems Manager, Secrets Manager)

## 🎯 Overview of Components to Setup

1. **S3 Bucket** - Store configuration files, scripts, and applications
2. **Secrets Manager** - Store domain credentials securely
3. **IAM Roles** - Permissions for automation and EC2 instances
4. **Systems Manager Document** - Automation runbook
5. **ImageBuilder** - Base AMI images (optional but recommended)
6. **Configuration Files** - Upload all configs and scripts

---

## Step 1: Create S3 Bucket for Configuration Storage

### 1.1 Create the S3 Bucket

1. **Navigate to S3 Console**
   - Go to AWS Console → Services → S3
   - Click **"Create bucket"**

2. **Configure Bucket Settings**
   - **Bucket name**: `sgt-imagebuilder` (or your preferred name)
   - **Region**: `af-south-1` (Africa - Cape Town)
   - **Block Public Access**: Keep all boxes checked (recommended)
   - **Bucket Versioning**: Enable (recommended for config management)
   - **Default encryption**: Enable with Amazon S3 managed keys (SSE-S3)

3. **Create the Bucket**
   - Click **"Create bucket"**

### 1.2 Create Folder Structure

1. **Open your new bucket**
2. **Create the following folder structure** by clicking "Create folder":
   ```
   windows/
   ├── config/
   ├── scripts/
   ├── applications/
   └── customizations/
   ```

3. **Create each folder**:
   - Click **"Create folder"** → Enter `windows` → **"Create folder"**
   - Navigate into `windows/` folder
   - Create `config`, `scripts`, `applications`, and `customizations` folders

---

## Step 2: Setup Secrets Manager for Domain Credentials

### 2.1 Create Domain Credentials Secret

1. **Navigate to Secrets Manager**
   - Go to AWS Console → Services → Secrets Manager
   - Click **"Store a new secret"**

2. **Configure Secret Type**
   - Select **"Other type of secret"**
   - Choose **"Plaintext"** tab

3. **Enter Domain Credentials and Webhook API Key** (replace with your actual values):
   ```json
   {
     "domainJoinUserName": "YOURDOMAIN\\ServiceAccount",
     "domainJoinPassword": "YourSecurePassword123!",
     "domainName": "yourdomain.com",
     "defaultTargetOU": "OU=Servers,OU=Default,DC=yourdomain,DC=com",
     "webhookApiKey": "18adecad-df09-47e2-ae05-de7686ae3156",
     "webhookApiUrl": "http://srv009484.mud.internal.co.za:8080/webhook/v1"
   }
   ```

4. **Configure Secret Details**
   - **Secret name**: `ec2-deployment-domain-credentials`
   - **Description**: `Domain join credentials for EC2 deployment automation`
   - **Encryption key**: Use default AWS managed key

5. **Configure Rotation** (Optional)
   - For production, consider enabling automatic rotation
   - For testing, you can disable rotation

6. **Review and Create**
   - Click **"Store"**
   - **Copy the Secret ARN** - you'll need this later

---

## Step 3: Create IAM Roles and Policies

### 3.1 Create EC2 Instance Role

1. **Navigate to IAM Console**
   - Go to AWS Console → Services → IAM → Roles
   - Click **"Create role"**

2. **Select Trusted Entity**
   - Choose **"AWS service"**
   - Select **"EC2"**
   - Click **"Next"**

3. **Attach Policies**
   - Search and select these AWS managed policies:
     - `AmazonSSMManagedInstanceCore`
     - `CloudWatchAgentServerPolicy`
   - Click **"Next"**

4. **Configure Role**
   - **Role name**: `EC2-Deployment-Instance-Role-af-south-1`
   - **Description**: `IAM role for EC2 instances in deployment automation`
   - Click **"Create role"**

5. **Add Custom Policy to Role**
   - Click on the newly created role
   - Click **"Add permissions"** → **"Create inline policy"**
   - Click **"JSON"** tab and paste:

   ```json
   {
     "Version": "2012-10-17",
     "Statement": [
       {
         "Effect": "Allow",
         "Action": [
           "s3:GetObject",
           "s3:ListBucket"
         ],
         "Resource": [
           "arn:aws:s3:::sgt-imagebuilder",
           "arn:aws:s3:::sgt-imagebuilder/*"
         ]
       },
       {
         "Effect": "Allow",
         "Action": [
           "secretsmanager:GetSecretValue"
         ],
         "Resource": "arn:aws:secretsmanager:af-south-1:YOUR-ACCOUNT-ID:secret:ec2-deployment-domain-credentials-*"
       },
       {
         "Effect": "Allow",
         "Action": [
           "ec2:DescribeInstances",
           "ec2:DescribeTags",
           "ec2:CreateTags"
         ],
         "Resource": "*"
       }
     ]
   }
   ```

   - **Replace** `YOUR-ACCOUNT-ID` with your actual AWS account ID
   - **Policy name**: `EC2DeploymentInstancePolicy`
   - Click **"Create policy"**

   > **Note**: The domain service account referenced in Secrets Manager should have the appropriate Active Directory roles/permissions assigned for computer object creation and domain join operations. These roles may have different names in your environment but should provide equivalent functionality to standard AD delegation permissions.

### 3.2 Create Instance Profile

1. **Still in the EC2 Instance Role**
   - Note the **Role ARN** - you'll need this later
   - The instance profile is automatically created with the same name

### 3.3 Create SSM Automation Role

1. **Create New Role**
   - Go back to IAM → Roles → **"Create role"**

2. **Select Trusted Entity**
   - Choose **"AWS service"**
   - Select **"Systems Manager"**
   - Click **"Next"**

3. **Attach Policies**
   - Search and select: `AmazonSSMAutomationRole`
   - Click **"Next"**

4. **Configure Role**
   - **Role name**: `EC2-Deployment-Automation-Role-af-south-1`
   - **Description**: `IAM role for SSM Automation in EC2 deployment`
   - Click **"Create role"**

5. **Add Custom Policy**
   - Click on the newly created role
   - Click **"Add permissions"** → **"Create inline policy"**
   - Use **JSON** tab and paste:

   ```json
   {
     "Version": "2012-10-17",
     "Statement": [
       {
         "Effect": "Allow",
         "Action": [
           "ec2:RunInstances",
           "ec2:DescribeInstances",
           "ec2:DescribeImages",
           "ec2:CreateTags",
           "ec2:DescribeInstanceStatus",
           "ec2:DescribeSecurityGroups",
           "ec2:DescribeSubnets",
           "ec2:DescribeKeyPairs"
         ],
         "Resource": "*"
       },
       {
         "Effect": "Allow",
         "Action": [
           "imagebuilder:GetImage",
           "imagebuilder:ListImages"
         ],
         "Resource": "*"
       },
       {
         "Effect": "Allow",
         "Action": [
           "s3:GetObject",
           "s3:ListBucket"
         ],
         "Resource": [
           "arn:aws:s3:::sgt-imagebuilder",
           "arn:aws:s3:::sgt-imagebuilder/*"
         ]
       },
       {
         "Effect": "Allow",
         "Action": [
           "secretsmanager:GetSecretValue"
         ],
         "Resource": "arn:aws:secretsmanager:af-south-1:YOUR-ACCOUNT-ID:secret:ec2-deployment-domain-credentials-*"
       },
       {
         "Effect": "Allow",
         "Action": [
           "iam:PassRole"
         ],
         "Resource": "arn:aws:iam::YOUR-ACCOUNT-ID:role/EC2-Deployment-Instance-Role-af-south-1"
       }
     ]
   }
   ```

   - **Replace** `YOUR-ACCOUNT-ID` with your actual AWS account ID
   - **Policy name**: `EC2DeploymentAutomationPolicy`
   - Click **"Create policy"**

---

## Step 4: Upload Configuration Files to S3

### 4.1 Prepare Configuration Files

First, you need to create your configuration files based on your environment. Use the examples provided in the project.

### 4.2 Upload Base Configuration

1. **Navigate to your S3 bucket** → `windows/config/` folder
2. **Upload base_config.json**
   - Click **"Upload"** → **"Add files"**
   - Select your `base_config.json` file
   - Click **"Upload"**

### 4.3 Upload Specific Configurations

1. **Upload business-specific configs** to `windows/config/`:
   - `Sanlam_Shared_Config.json` (provided example)
   - Create additional configs as needed following the pattern: `{AssetOwner}_{AppType}_Config.json`
   - Examples you might create:
     - `Sanlam_MSSQL_Config.json` (for SQL Server deployments)
     - `Santam_Shared_Config.json` (for Santam shared servers)
     - `RetailMass_Shared_Config.json` (for Retail Mass deployments)

   > **Note**: Only `Sanlam_Shared_Config.json` is provided as an example. You'll need to create additional configuration files based on your specific requirements using the same structure.

### 4.4 Upload PowerShell Scripts

1. **Navigate to** `windows/scripts/` folder
2. **Upload all PowerShell scripts**:
   - `Get-DeploymentConfiguration.ps1`
   - `New-ADComputerObject.ps1`
   - `Join-DomainWithLocalAdmins.ps1`
   - `Deploy-Applications.ps1`
   - `Apply-Customizations.ps1`
   - `Test-DeploymentConfiguration.ps1`

### 4.5 Upload Customizations

1. **Navigate to** `windows/customizations/` folder
2. **Upload customization files**:
   - `Sanlam_Shared_DEV_customizations.json`
   - `Sanlam_MSSQL_PRD_customizations.json`
   - Any other customization files

### 4.6 Upload Applications (Optional)

1. **Navigate to** `windows/applications/` folder
2. **Upload application installers**:
   - MSI files, EXE files, or ZIP packages
   - Organize in subfolders if needed

---

## Step 5: Create Systems Manager Automation Document

### 5.1 Navigate to Systems Manager

1. **Go to AWS Console** → Services → Systems Manager
2. **Click on "Documents"** in the left sidebar
3. **Click "Create document"**

### 5.2 Configure Document

1. **Document details**:
   - **Name**: `EC2-Deployment-Automation` (for on-premises AD webhook integration)
   - **Alternative Name**: `EC2-Deployment-Automation-AWS-AD` (for AWS Managed Microsoft AD integration)
   - **Document type**: `Automation`
   - **Document format**: `YAML`

2. **Content**:
   - **For on-premises AD**: Copy content from `EC2-Deployment-Automation.yaml`
   - **For AWS Managed Microsoft AD**: Copy content from `EC2-Deployment-Automation-AWS-AD.yaml`
   - Paste the selected content into the **Content** section

3. **Update Parameters** in the document:
   - Find the `AutomationAssumeRole` default value
   - Replace with: `arn:aws:iam::YOUR-ACCOUNT-ID:role/EC2-Deployment-Automation-Role-af-south-1`
   - Find the `S3ConfigBucket` default value
   - Replace with: `sgt-imagebuilder`

4. **Create Document**:
   - Click **"Create document"**

---

## Step 6: Setup ImageBuilder (Optional but Recommended)

### 6.1 Create Base Windows Image

1. **Navigate to EC2 Image Builder**
   - Go to AWS Console → Services → EC2 Image Builder

2. **Create Image Recipe**
   - Click **"Create image recipe"**
   - **Name**: `windows-server-2022-base`
   - **Base image**: Select latest Windows Server 2022
   - **Build components**: Add any standard components you need
   - **Create recipe**

3. **Create Infrastructure Configuration**
   - Click **"Create infrastructure configuration"**
   - **Name**: `windows-deployment-infrastructure`
   - **Instance type**: `t3.medium` (or larger)
   - **Instance profile**: `EC2-Deployment-Instance-Role-af-south-1`
   - **VPC and subnet**: Select appropriate values
   - **Security groups**: Ensure RDP and domain communication
   - **Create configuration**

4. **Create Image Pipeline**
   - Click **"Create image pipeline"**
   - **Name**: `windows-server-2022-pipeline`
   - **Image recipe**: Select your created recipe
   - **Infrastructure configuration**: Select your created config
   - **Distribution settings**: Configure as needed
   - **Create pipeline**

5. **Run Pipeline**
   - Select your pipeline and click **"Run pipeline"**
   - Wait for completion (this can take 30-60 minutes)
   - Note the **Image ARN** for use in automation

---

## Step 7: Test the Setup

### 7.1 Validate Configuration Files

1. **Test S3 Access**
   - Ensure all files are uploaded correctly
   - Check folder structure matches requirements

2. **Test Secrets Manager**
   - Verify secret contains correct domain credentials
   - Test domain connectivity from AWS

### 7.2 Run Test Automation

1. **Navigate to Systems Manager** → **Automation**
2. **Click "Execute automation"**
3. **Select your document**: `EC2-Deployment-Automation`
4. **Fill in parameters**:
   - **AssetOwner**: `Sanlam`
   - **AppType**: `Shared`
   - **Client**: `SGT`
   - **Environment**: `DEV`
   - **OSVersion**: `Windows Server 2022`
   - **ImageBuilderImageArn**: Your ImageBuilder image ARN
   - **InstanceType**: `t3.medium`
   - **SubnetId**: Your subnet ID
   - **SecurityGroupIds**: Your security group ID
   - **KeyName**: Your EC2 key pair name
   - **IamInstanceProfile**: `EC2-Deployment-Instance-Role-af-south-1`

5. **Execute automation**
   - Click **"Execute"**
   - Monitor progress in the execution details

---

## 🔧 Troubleshooting Common Issues

### S3 Access Issues
- Verify bucket name and region
- Check IAM permissions for S3 access
- Ensure files are in correct folder structure

### Secrets Manager Issues
- Verify secret ARN in IAM policies
- Check JSON format in secret value
- Ensure domain credentials are correct

### Domain Join Issues
- Verify domain connectivity from AWS
- Check DNS resolution
- Validate service account permissions

### ImageBuilder Issues
- Ensure instance profile has required permissions
- Check VPC and security group settings
- Verify base image availability in region

---

## 🧪 Testing and Validation

Before running full deployments, it's recommended to test your configuration and AD integration using the validation runbook.

### Configuration and AD Validation Runbook

The `Config-and-AD-Validation.yaml` runbook provides comprehensive testing capabilities:

1. **Navigate to Systems Manager > Automation**
2. **Search for**: `Config-and-AD-Validation`
3. **Execute with parameters**:
   - **AssetOwner**: `Sanlam`
   - **AppType**: `Shared`
   - **Client**: `SGT`
   - **Environment**: `DEV`
   - **OSVersion**: `Windows Server 2022`
   - **S3ConfigBucket**: `your-config-bucket`
   - **SecretsManagerSecretArn**: `your-secret-arn`
   - **TestMode**: `true` (for safe testing without creating actual AD objects)

### What the Validation Tests

1. **Configuration Loading**:
   - Downloads and validates base_config.json
   - Validates input parameters against allowed values
   - Loads specific configuration files
   - Extracts client/environment/OS specific settings

2. **AD Object Creation**:
   - Tests webhook API connectivity
   - Validates Secrets Manager access
   - Simulates AD object creation (in test mode)
   - Provides detailed logging and error handling

3. **Results Validation**:
   - Comprehensive validation of all configuration elements
   - AD object creation status verification
   - Overall system readiness assessment

### Test Mode vs Production Mode

- **Test Mode** (`TestMode: true`): Simulates API calls without creating actual AD objects
- **Production Mode** (`TestMode: false`): Creates actual AD objects for testing

> **Recommendation**: Always run in test mode first to validate configuration and connectivity before attempting actual AD object creation.

---

## 📚 Next Steps

After successful setup and validation:

1. **Run validation tests** using the Config-and-AD-Validation runbook
2. **Create additional configurations** for different environments
3. **Set up monitoring and alerting** for automation executions
4. **Implement change management** processes for configuration updates
5. **Train team members** on using the automation system
6. **Plan for disaster recovery** scenarios

---

## 🏗️ Architecture Options

This automation system provides two different approaches for Active Directory integration:

### **Option 1: On-Premises AD Webhook Integration**
- **File**: `EC2-Deployment-Automation.yaml`
- **Use Case**: When you have on-premises Active Directory with webhook API access
- **AD Integration**: Calls your on-premises webhook API to create computer objects
- **Benefits**: Leverages existing on-premises AD infrastructure and scripts
- **Requirements**: Network connectivity to on-premises webhook server

### **Option 2: AWS Managed Microsoft AD Integration**
- **File**: `EC2-Deployment-Automation-AWS-AD.yaml`
- **Use Case**: When using AWS Managed Microsoft AD or AWS AD Connector
- **AD Integration**: Uses AWS Directory Service APIs for computer object management
- **Benefits**: Native AWS integration, no on-premises dependencies
- **Requirements**: AWS Managed Microsoft AD or AD Connector configured

### **Choosing the Right Option**

| Factor | On-Premises Webhook | AWS Managed AD |
|--------|-------------------|----------------|
| **AD Location** | On-premises | AWS Managed or Hybrid |
| **Network Dependencies** | Requires VPN/Direct Connect | AWS native |
| **Complexity** | Medium (webhook setup) | Low (AWS native) |
| **Flexibility** | High (custom scripts) | Medium (AWS APIs) |
| **Cost** | Lower (existing infrastructure) | Higher (AWS AD service) |
| **Maintenance** | Higher (webhook server) | Lower (AWS managed) |

---

## 🔗 On-Premises Webhook Integration

The automation uses an on-premises webhook API to create Active Directory computer objects. This integration is required because AWS Systems Manager automation cannot directly access your on-premises Active Directory.

### Webhook Configuration

The webhook API credentials are securely stored in AWS Secrets Manager along with the domain credentials:

**API Endpoint**: Retrieved from `webhookApiUrl` in Secrets Manager
**API Key**: Retrieved from `webhookApiKey` in Secrets Manager
**Script**: `Create-ADObject.ps1`

> **Security Note**: The API key and URL are stored in Secrets Manager for security and can be rotated without updating the automation code.

### Required Parameters

The automation will call your webhook with these parameters:
- `jobId`: Unique identifier for tracking (format: AWS-SSM-yyyyMMdd-HHmmss)
- `objectName`: Computer name for the AD object
- `objectDescription`: Description including asset owner, client, app type, environment
- `vmOS`: Always "Windows"
- `domain`: Domain from configuration (e.g., mud.internal.co.za)
- `ouPath`: Target OU path from configuration
- `appType`: Application type from configuration

### Example Webhook Call

```
http://srv009484.mud.internal.co.za:8080/webhook/v1?key=18adecad-df09-47e2-ae05-de7686ae3156&script=Create-ADObject.ps1&param=-jobId%20%27AWS-SSM-20250903-143022%27%20-objectName%20%27Sanlam-SGT-Shared-20250903-1430%27%20-objectDescription%20%27AWS%20EC2%20-%20Sanlam%20SGT%20Shared%20DEV%27%20-vmOS%20%27Windows%27%20-domain%20%27mud.internal.co.za%27%20-ouPath%20%27OU=Server%202022,OU=Windows%20Server,OU=Servers,OU=SGT,OU=Businesses,DC=mud,DC=internal,DC=co,DC=za%27%20-appType%20%27Shared%27
```

### Expected API Response Format

The automation expects your webhook API to return a JSON response with this structure:

```json
{
  "output": {
    "status": "COMPLETED",
    "message": "AD Object TST041122 created successfully in mud.internal.co.za",
    "data": {
      "objectName": "TST041122",
      "timeStamp": "2025-08-22 11:48:12",
      "Domain": "mud.internal.co.za",
      "Comment": "AD Object TST041122 created successfully in mud.internal.co.za",
      "ouCreatedIn": "OU=Server 2022,OU=Windows Server,OU=Servers,OU=SanlamLife,OU=Businesses,DC=mud,DC=internal,DC=co,DC=za"
    },
    "success": true
  }
}
```

**Required Fields for Success:**
- `output.success`: Must be `true` for successful creation
- `output.status`: Must be `"COMPLETED"` for successful creation
- `output.data.objectName`: The actual computer name created
- `output.data.ouCreatedIn`: The actual OU where the object was created
- `output.message`: Human-readable success/error message

**Response Handling:**
- ✅ **Success**: `success: true` and `status: "COMPLETED"`
- ❌ **Failure**: `success: false` or `status` not "COMPLETED"
- 📝 **Logging**: All response details are logged for troubleshooting

### Network Requirements

Ensure your AWS VPC can reach the webhook server:
1. **Security Groups**: Allow outbound HTTP traffic on port 8080
2. **Route Tables**: Configure routing to on-premises network
3. **DNS Resolution**: Ensure srv009484.mud.internal.co.za resolves correctly
4. **Firewall Rules**: Allow traffic from AWS subnets to webhook server

### Error Handling

If the webhook API call fails:
- The automation will log a warning but **continue** with EC2 deployment
- **Default OU Fallback**: Uses `defaultTargetOU` from Secrets Manager or the configured target OU
- **Automatic Creation**: The domain join process will create the AD object automatically in the default OU
- **No Manual Intervention**: The automation continues seamlessly even without pre-created AD objects

**Fallback OU Priority:**
1. `defaultTargetOU` from Secrets Manager (if configured)
2. Target OU from the specific configuration file
3. Domain join will create the computer object in the appropriate OU during the join process

---

## 🏢 AWS Managed Microsoft AD Integration

For environments using AWS Managed Microsoft AD, the alternative runbook (`EC2-Deployment-Automation-AWS-AD.yaml`) provides native AWS integration.

### AWS Directory Service Configuration

**Directory Setup Requirements:**
- AWS Managed Microsoft AD directory created and configured
- Directory ID available for automation parameter
- Proper DNS configuration for domain resolution
- Security groups allowing domain traffic

### Required Parameters (Additional for AWS AD)

When using the AWS AD version, you'll need this additional parameter:

```yaml
DirectoryId: d-**********  # Your AWS Managed Microsoft AD Directory ID
```

### Secrets Manager Structure (AWS AD Version)

The AWS AD version uses the same Secrets Manager structure but doesn't require webhook credentials:

```json
{
  "domainJoinUserName": "YOURDOMAIN\\ServiceAccount",
  "domainJoinPassword": "YourSecurePassword123!",
  "domainName": "yourdomain.com",
  "defaultTargetOU": "OU=Servers,OU=Default,DC=yourdomain,DC=com"
}
```

### AWS AD Integration Features

**Native AWS Integration:**
- Uses AWS Directory Service APIs where available
- Seamless integration with AWS services
- No external dependencies or webhook servers
- Built-in AWS security and compliance

**Fallback Mechanism:**
- If AWS Directory Service APIs are limited, falls back to standard domain join
- Computer objects created automatically during domain join process
- Same OU targeting as on-premises version

### Network Requirements (AWS AD)

Ensure your AWS VPC is properly configured:
1. **Directory Service**: AWS Managed Microsoft AD deployed in your VPC
2. **DNS Resolution**: Proper DNS configuration for domain resolution
3. **Security Groups**: Allow domain traffic between subnets
4. **Subnets**: Directory and EC2 instances in appropriate subnets

### Limitations and Considerations

**AWS Directory Service API Limitations:**
- Limited computer object management APIs compared to full AD PowerShell
- Some advanced AD operations may require custom Lambda functions
- Fallback to standard domain join for complex scenarios

**Recommended Use Cases:**
- New AWS-native deployments
- Hybrid environments with AWS AD Connector
- Simplified AD management requirements
- Reduced on-premises dependencies

---

## 📋 Pre-Deployment Checklist

Before running your first automation, verify:

### AWS Infrastructure
- [ ] S3 bucket created with correct folder structure
- [ ] All configuration files uploaded to S3
- [ ] All PowerShell scripts uploaded to S3
- [ ] Secrets Manager secret created with domain credentials
- [ ] IAM roles created with proper permissions
- [ ] Systems Manager automation document created
- [ ] ImageBuilder pipeline completed (if using custom images)

### Network Configuration
- [ ] VPC and subnets configured for domain communication
- [ ] Security groups allow domain traffic (ports 53, 88, 135, 389, 445, 464, 636, 3268, 3269)
- [ ] Security groups allow outbound HTTP access to webhook API (port 8080)
- [ ] DNS resolution working for domain controllers
- [ ] DNS resolution working for webhook server (srv009484.mud.internal.co.za)
- [ ] Route tables configured for domain connectivity
- [ ] Route tables configured for webhook API connectivity

### Active Directory
- [ ] Service account created with computer object creation permissions
- [ ] Target OUs exist and are accessible
- [ ] Domain controllers accessible from AWS subnets
- [ ] DNS properly configured for domain resolution
- [ ] On-premises webhook API server accessible (srv009484.mud.internal.co.za:8080)
- [ ] Webhook API key and URL stored in Secrets Manager
- [ ] Create-ADObject.ps1 script deployed and functional on webhook server

### Configuration Files
- [ ] base_config.json contains correct asset owners and app types
- [ ] Specific configuration files match your naming convention
- [ ] Customization files contain appropriate settings
- [ ] All file paths and S3 references are correct

---

## 🔐 Security Best Practices

### IAM Security
1. **Principle of Least Privilege**
   - Only grant minimum required permissions
   - Regularly review and audit IAM policies
   - Use condition statements where possible

2. **Role Separation**
   - Separate roles for automation vs. instance access
   - Different roles for different environments (DEV/TEST/PROD)

3. **Cross-Account Access**
   - If using multiple AWS accounts, configure cross-account roles
   - Use external ID for additional security

### Secrets Management
1. **Rotation Strategy**
   - Enable automatic rotation for production secrets
   - Use different service accounts for different environments
   - Monitor secret access through CloudTrail

2. **Access Control**
   - Limit secret access to specific roles only
   - Use resource-based policies for fine-grained control
   - Enable secret versioning

### Network Security
1. **VPC Configuration**
   - Use private subnets for domain-joined instances
   - Implement NACLs for additional security
   - Enable VPC Flow Logs for monitoring

2. **Security Groups**
   - Follow principle of least privilege for port access
   - Use specific source/destination rules
   - Document all security group rules

---

## 📊 Monitoring and Logging

### CloudWatch Integration
1. **Automation Monitoring**
   - Set up CloudWatch alarms for automation failures
   - Monitor execution duration and success rates
   - Create dashboards for automation metrics

2. **Instance Monitoring**
   - Enable detailed monitoring for deployed instances
   - Set up custom metrics for application health
   - Configure log aggregation

### CloudTrail Logging
1. **API Monitoring**
   - Enable CloudTrail for all automation-related API calls
   - Monitor Secrets Manager access
   - Track S3 configuration file access

2. **Compliance**
   - Maintain audit trails for compliance requirements
   - Set up log retention policies
   - Configure log analysis tools

---

## 🔄 Maintenance and Updates

### Regular Maintenance Tasks
1. **Monthly Reviews**
   - Review automation execution logs
   - Update configuration files as needed
   - Check for AWS service updates

2. **Quarterly Tasks**
   - Review and update IAM policies
   - Test disaster recovery procedures
   - Update documentation

3. **Annual Tasks**
   - Comprehensive security review
   - Update service account passwords
   - Review and optimize costs

### Configuration Management
1. **Version Control**
   - Use S3 versioning for configuration files
   - Maintain change logs for all modifications
   - Test changes in development environment first

2. **Backup Strategy**
   - Regular backups of S3 configuration bucket
   - Export and backup IAM policies
   - Document all custom configurations

---

## 🚀 Advanced Configuration Options

### Multi-Environment Setup
1. **Environment Separation**
   - Use separate S3 prefixes for DEV/TEST/PROD
   - Different IAM roles per environment
   - Separate Secrets Manager secrets

2. **Cross-Region Deployment**
   - Replicate S3 bucket to multiple regions
   - Create region-specific automation documents
   - Configure cross-region ImageBuilder

### Integration with Other Services
1. **Service Catalog Integration**
   - Create Service Catalog products for common deployments
   - Use automation as backend for self-service portal

2. **Config Rules**
   - Set up AWS Config rules for compliance checking
   - Automatic remediation for configuration drift

3. **Systems Manager Patch Manager**
   - Integrate with patch management workflows
   - Automated patching schedules

---

## 📋 Quick Reference - Validation Runbook Parameters

### Config-and-AD-Validation Parameters

| Parameter | Required | Description | Example |
|-----------|----------|-------------|---------|
| `AssetOwner` | ✅ | Asset Owner | `Sanlam`, `Santam`, `Retail Mass` |
| `AppType` | ✅ | Application Type | `Shared`, `MSSQL` |
| `Client` | ✅ | Client identifier | `SGT`, `SPF`, `SC`, `STM` |
| `Environment` | ✅ | Environment | `PRD`, `PPE`, `DEV` |
| `OSVersion` | ✅ | OS Version | `Windows Server 2022`, `Windows Server 2019` |
| `S3ConfigBucket` | ✅ | S3 bucket with configs | `your-config-bucket` |
| `SecretsManagerSecretArn` | ✅ | Secret ARN | `arn:aws:secretsmanager:af-south-1:account:secret:name` |
| `ComputerName` | ❌ | Custom computer name | Auto-generated if empty |
| `Region` | ❌ | AWS Region | `af-south-1` (default) |
| `TestMode` | ❌ | Test mode flag | `true` (safe testing), `false` (production) |

### Example Validation Command

```bash
aws ssm start-automation-execution \
  --document-name "Config-and-AD-Validation" \
  --parameters \
    AssetOwner=Sanlam \
    AppType=Shared \
    Client=SGT \
    Environment=DEV \
    OSVersion="Windows Server 2022" \
    S3ConfigBucket=your-config-bucket \
    SecretsManagerSecretArn=arn:aws:secretsmanager:af-south-1:account:secret:domain-creds \
    TestMode=true \
  --region af-south-1
```

---

## 📞 Support and Resources

### Internal Documentation
- [Configuration-Guide.md](Configuration-Guide.md) - Detailed configuration reference
- [Deployment-Guide.md](Deployment-Guide.md) - Step-by-step deployment procedures
- [Troubleshooting-Guide.md](Troubleshooting-Guide.md) - Common issues and solutions

### AWS Resources
- [AWS Systems Manager Documentation](https://docs.aws.amazon.com/systems-manager/)
- [AWS ImageBuilder Documentation](https://docs.aws.amazon.com/imagebuilder/)
- [AWS Secrets Manager Documentation](https://docs.aws.amazon.com/secretsmanager/)

### Community Resources
- AWS re:Post for community support
- AWS Architecture Center for best practices
- AWS Well-Architected Framework

---

## 📝 Change Log

| Version | Date | Changes |
|---------|------|---------|
| 1.0.0 | 03/09/2025 | Initial setup guide creation |

---

**Author**: Rudi van Zyl
**Last Updated**: 03/09/2025
**Version**: 1.0.0
