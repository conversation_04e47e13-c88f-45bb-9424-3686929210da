# AWS Image Builder Component: Configure System Services Firewall Rules
# This component configures Windows Firewall rules for system services and protocols

name: configure-firewall-system
description: Configure Windows Firewall rules for system services, RPC, SMB, and monitoring
schemaVersion: 1.0
phases:
  - name: build
    steps:
      - name: ConfigureSystemServiceRules
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "Configuring system services firewall rules..."

                # DHCP Server (Ports 67, 68)
                Write-Host "Allowing DHCP Server (ports 67, 68)..."
                New-NetFirewallRule -DisplayName "DHCP Server" -Direction Inbound -Action Allow -Protocol UDP -LocalPort 67 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow DHCP server from internal networks"
                New-NetFirewallRule -DisplayName "DHCP Client" -Direction Inbound -Action Allow -Protocol UDP -LocalPort 68 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow DHCP client from internal networks"

                # RADIUS (Ports 1812, 1813)
                Write-Host "Allowing RADIUS (ports 1812, 1813)..."
                New-NetFirewallRule -DisplayName "RADIUS Authentication" -Direction Inbound -Action Allow -Protocol UDP -LocalPort 1812 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow RADIUS authentication from internal networks"
                New-NetFirewallRule -DisplayName "RADIUS Accounting" -Direction Inbound -Action Allow -Protocol UDP -LocalPort 1813 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow RADIUS accounting from internal networks"

                # LDAP over UDP (Port 389)
                Write-Host "Allowing LDAP over UDP (port 389)..."
                New-NetFirewallRule -DisplayName "LDAP UDP" -Direction Inbound -Action Allow -Protocol UDP -LocalPort 389 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow LDAP UDP from internal networks"

                # Print Spooler (Port 515)
                Write-Host "Allowing Print Spooler (port 515)..."
                New-NetFirewallRule -DisplayName "Print Spooler" -Direction Inbound -Action Allow -Protocol TCP -LocalPort 515 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow print spooler from internal networks"

                # Enable built-in system service firewall rules
                Write-Host "Enabling built-in system service firewall rules..."
                $systemRuleGroups = @(
                    "Network Discovery",
                    "Performance Logs and Alerts",
                    "Remote Event Log Management",
                    "Remote Scheduled Tasks Management",
                    "Remote Volume Management",
                    "Windows Firewall Remote Management",
                    "Windows Management Instrumentation (WMI)"
                )

                foreach ($ruleGroup in $systemRuleGroups) {
                    try {
                        Enable-NetFirewallRule -DisplayGroup $ruleGroup -ErrorAction SilentlyContinue
                        Write-Host "Enabled firewall rule group: $ruleGroup"
                    }
                    catch {
                        Write-Host "Could not enable firewall rule group $ruleGroup`: $($_.Exception.Message)" -ForegroundColor Yellow
                    }
                }

                Write-Host "System services firewall rules configured successfully"

  - name: validate
    steps:
      - name: ValidateSystemFirewallConfiguration
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "Validating system services firewall configuration..."

                $validationErrors = @()

                # Check if key system service rules exist
                $existingRules = Get-NetFirewallRule | Where-Object { $_.Enabled -eq "True" }

                $requiredSystemRules = @(
                    "DHCP Server",
                    "RADIUS Authentication"
                )

                $missingRules = @()
                foreach ($rule in $requiredSystemRules) {
                    $foundRule = $existingRules | Where-Object { $_.DisplayName -like "*$rule*" }
                    if (-not $foundRule) {
                        $missingRules += $rule
                    }
                }

                if ($missingRules.Count -gt 0) {
                    $validationErrors += "Missing system service firewall rules: $($missingRules -join ', ')"
                }

                # Validate specific system service port rules
                try {
                    $snmpRule = Get-NetFirewallRule -DisplayName "SNMP" -ErrorAction SilentlyContinue
                    if ($snmpRule) {
                        $snmpPortFilter = Get-NetFirewallPortFilter -AssociatedNetFirewallRule $snmpRule
                        if ($snmpPortFilter.LocalPort -ne "161") {
                            $validationErrors += "SNMP rule should be on port 161, found: $($snmpPortFilter.LocalPort)"
                        }
                    }



                    $tftpRule = Get-NetFirewallRule -DisplayName "TFTP" -ErrorAction SilentlyContinue
                    if ($tftpRule) {
                        $tftpPortFilter = Get-NetFirewallPortFilter -AssociatedNetFirewallRule $tftpRule
                        if ($tftpPortFilter.LocalPort -ne "69") {
                            $validationErrors += "TFTP rule should be on port 69, found: $($tftpPortFilter.LocalPort)"
                        }
                    }

                    $dhcpRule = Get-NetFirewallRule -DisplayName "DHCP Server" -ErrorAction SilentlyContinue
                    if ($dhcpRule) {
                        $dhcpPortFilter = Get-NetFirewallPortFilter -AssociatedNetFirewallRule $dhcpRule
                        if ($dhcpPortFilter.LocalPort -ne "67") {
                            $validationErrors += "DHCP Server rule should be on port 67, found: $($dhcpPortFilter.LocalPort)"
                        }
                    }

                    $radiusRule = Get-NetFirewallRule -DisplayName "RADIUS Authentication" -ErrorAction SilentlyContinue
                    if ($radiusRule) {
                        $radiusPortFilter = Get-NetFirewallPortFilter -AssociatedNetFirewallRule $radiusRule
                        if ($radiusPortFilter.LocalPort -ne "1812") {
                            $validationErrors += "RADIUS Authentication rule should be on port 1812, found: $($radiusPortFilter.LocalPort)"
                        }
                    }
                }
                catch {
                    $validationErrors += "Failed to validate system service port configurations: $($_.Exception.Message)"
                }

                # Report validation results
                if ($validationErrors.Count -eq 0) {
                    Write-Host "VALIDATION SUCCESS: System services firewall rules configured correctly"
                    Write-Host "- DHCP: Ports 67 (server) and 68 (client)"
                    Write-Host "- RADIUS: Ports 1812 (auth) and 1813 (accounting)"
                    Write-Host "- LDAP UDP: Port 389"
                    Write-Host "- Print Spooler: Port 515"
                    Write-Host "- All rules restricted to internal networks only"
                    Write-Host "- Total system service rules: $($existingRules | Where-Object { $_.DisplayName -like "*DHCP*" -or $_.DisplayName -like "*RADIUS*" -or $_.DisplayName -like "*LDAP*" -or $_.DisplayName -like "*Print*" } | Measure-Object | Select-Object -ExpandProperty Count)"
                    exit 0
                } else {
                    Write-Error "VALIDATION FAILED: System services firewall configuration errors found:"
                    foreach ($error in $validationErrors) {
                        Write-Error "  - $error"
                    }
                    exit 1
                }
