# Troubleshooting Guide

This guide provides solutions for common issues encountered with the AWS EC2 Deployment Automation system.

## Common Issues and Solutions

### 1. Configuration Loading Issues

#### Error: "Failed to download base config"

**Symptoms:**
- Automation fails at the `loadConfiguration` step
- Error message indicates S3 access issues

**Possible Causes:**
- S3 bucket doesn't exist or is in wrong region
- IAM role lacks S3 permissions
- Configuration file doesn't exist at expected path

**Solutions:**

1. **Verify S3 bucket exists:**
```bash
aws s3 ls s3://your-config-bucket --region af-south-1
```

2. **Check file exists:**
```bash
aws s3 ls s3://your-config-bucket/configs/base_config.json --region af-south-1
```

3. **Test IAM permissions:**
```bash
aws s3 cp s3://your-config-bucket/configs/base_config.json /tmp/test.json --region af-south-1
```

4. **Verify automation role permissions:**
```bash
aws iam get-role-policy \
    --role-name EC2-Deployment-Automation-Role-af-south-1 \
    --policy-name EC2DeploymentAutomationPolicy
```

#### Error: "Invalid Asset Owner: XYZ"

**Symptoms:**
- Configuration validation fails
- Specific parameter values are rejected

**Solutions:**

1. **Check base_config.json structure:**
```bash
aws s3 cp s3://your-config-bucket/configs/base_config.json - --region af-south-1 | jq .
```

2. **Verify case sensitivity:**
- Ensure parameter values match exactly (case-sensitive)
- Check for extra spaces or special characters

3. **Validate JSON syntax:**
```bash
aws s3 cp s3://your-config-bucket/configs/base_config.json - --region af-south-1 | python -m json.tool
```

### 2. Active Directory Issues

#### Error: "Failed to create AD computer object"

**Symptoms:**
- `createADObject` step fails
- Domain join operations fail

**Possible Causes:**
- Service account lacks permissions
- Target OU doesn't exist
- Domain connectivity issues
- RSAT tools installation failure

**Solutions:**

1. **Test domain connectivity:**
```powershell
Test-NetConnection -ComputerName "domain.com" -Port 389
```

2. **Verify service account permissions:**
```powershell
# Test with domain admin account first
$cred = Get-Credential
Get-ADOrganizationalUnit -Filter "Name -eq 'Servers'" -Server "domain.com" -Credential $cred
```

3. **Check target OU exists:**
```powershell
Get-ADOrganizationalUnit -Identity "OU=Server 2022,OU=Windows Server,OU=Servers,OU=SGT,OU=Businesses,DC=domain,DC=com" -Server "domain.com"
```

4. **Install RSAT tools manually:**
```powershell
Add-WindowsFeature -Name RSAT-AD-Tools
```

#### Error: "Computer object already exists"

**Symptoms:**
- AD object creation fails due to existing object
- Object exists in wrong OU

**Solutions:**

1. **Move existing object:**
```powershell
$computer = Get-ADComputer -Identity "ComputerName" -Server "domain.com"
Move-ADObject -Identity $computer.DistinguishedName -TargetPath "OU=Target,DC=domain,DC=com" -Server "domain.com"
```

2. **Delete and recreate:**
```powershell
Remove-ADComputer -Identity "ComputerName" -Server "domain.com" -Confirm:$false
```

### 3. EC2 Deployment Issues

#### Error: "Failed to get ImageBuilder image details"

**Symptoms:**
- EC2 deployment step fails
- ImageBuilder ARN is invalid

**Solutions:**

1. **Verify ImageBuilder image exists:**
```bash
aws imagebuilder get-image --image-build-version-arn "arn:aws:imagebuilder:af-south-1:123456789012:image/image-name/1.0.0/1" --region af-south-1
```

2. **List available images:**
```bash
aws imagebuilder list-images --region af-south-1
```

3. **Check image status:**
```bash
aws imagebuilder get-image --image-build-version-arn "your-arn" --region af-south-1 --query 'image.state.status'
```

#### Error: "Failed to launch EC2 instance"

**Symptoms:**
- Instance launch fails
- Insufficient capacity or permission errors

**Solutions:**

1. **Check subnet capacity:**
```bash
aws ec2 describe-subnets --subnet-ids subnet-12345678 --region af-south-1 --query 'Subnets[0].AvailableIpAddressCount'
```

2. **Verify security group exists:**
```bash
aws ec2 describe-security-groups --group-ids sg-12345678 --region af-south-1
```

3. **Check instance limits:**
```bash
aws service-quotas get-service-quota --service-code ec2 --quota-code L-1216C47A --region af-south-1
```

4. **Test with different instance type:**
```bash
# Try with smaller instance type first
aws ec2 run-instances --image-id ami-12345678 --count 1 --instance-type t3.micro --dry-run --region af-south-1
```

### 4. Domain Join Issues

#### Error: "Domain join failed"

**Symptoms:**
- Instance launches but doesn't join domain
- User data script execution fails

**Solutions:**

1. **Check user data execution:**
```powershell
# On the instance, check user data logs
Get-Content C:\ProgramData\Amazon\EC2-Windows\Launch\Log\UserdataExecution.log
```

2. **Verify DNS resolution:**
```powershell
Resolve-DnsName "domain.com"
```

3. **Test domain join manually:**
```powershell
$cred = Get-Credential
Add-Computer -DomainName "domain.com" -Credential $cred -OUPath "OU=Servers,DC=domain,DC=com"
```

4. **Check Secrets Manager access:**
```powershell
aws secretsmanager get-secret-value --secret-id "your-secret-arn" --region af-south-1
```

### 5. Application Deployment Issues

#### Error: "Application installation failed"

**Symptoms:**
- Applications don't install correctly
- Silent installation parameters incorrect

**Solutions:**

1. **Test application download:**
```powershell
aws s3 cp s3://your-bucket/applications/app.msi C:\temp\app.msi --region af-south-1
```

2. **Test manual installation:**
```powershell
Start-Process msiexec.exe -ArgumentList "/i C:\temp\app.msi /quiet /norestart" -Wait
```

3. **Check installation logs:**
```powershell
Get-Content "$env:TEMP\MSI*.log" | Select-Object -Last 50
```

### 6. Security Configuration Issues

#### Error: "Firewall rule creation failed"

**Symptoms:**
- Security configuration step fails
- Firewall rules not applied

**Solutions:**

1. **Test firewall rule manually:**
```powershell
New-NetFirewallRule -DisplayName "Test Rule" -Direction Inbound -Protocol TCP -LocalPort 3389 -Action Allow
```

2. **Check existing rules:**
```powershell
Get-NetFirewallRule | Where-Object {$_.DisplayName -like "*RDP*"}
```

3. **Verify rule syntax:**
```powershell
# Check security configuration JSON
aws s3 cp s3://your-bucket/security/config.json - --region af-south-1 | python -m json.tool
```

## Debugging Techniques

### 1. Enable Detailed Logging

Add verbose logging to PowerShell scripts:

```powershell
$VerbosePreference = "Continue"
$DebugPreference = "Continue"
```

### 2. Use SSM Session Manager

Connect to instances for debugging:

```bash
aws ssm start-session --target i-1234567890abcdef0 --region af-south-1
```

### 3. Check CloudWatch Logs

Monitor automation execution logs:

```bash
aws logs describe-log-groups --log-group-name-prefix "/aws/ssm" --region af-south-1
```

### 4. Use the Validation Runbook for Comprehensive Testing

**Recommended First Step**: Run the Config-and-AD-Validation runbook to identify issues:

```bash
# Execute comprehensive validation
aws ssm start-automation-execution \
    --document-name "Config-and-AD-Validation" \
    --parameters \
        AssetOwner=Sanlam \
        AppType=Shared \
        Client=SGT \
        Environment=DEV \
        OSVersion="Windows Server 2022" \
        S3ConfigBucket=your-config-bucket \
        SecretsManagerSecretArn=your-secret-arn \
        TestMode=true \
    --region af-south-1
```

**What this tests:**
- Configuration file access and validation
- Parameter validation against base configuration
- Secrets Manager connectivity
- Webhook API connectivity and authentication
- AD object creation simulation
- Overall system readiness

**Benefits:**
- Safe testing with TestMode=true (no actual AD objects created)
- Comprehensive logging for troubleshooting
- Validates entire configuration and integration chain
- Identifies issues before full deployment

### 5. Test Components Individually

If validation runbook identifies specific issues, test components independently:

```powershell
# Test configuration loading
.\Get-DeploymentConfiguration.ps1 -AssetOwner "Sanlam" -AppType "Shared" -Client "SGT" -Environment "DEV" -OSVersion "Windows Server 2022" -S3BucketName "your-bucket"

# Test AD object creation
.\New-ADComputerObject.ps1 -ComputerName "TEST001" -TargetOU "OU=Servers,DC=domain,DC=com" -Domain "domain.com" -SecretsManagerSecretArn "your-secret-arn"
```

## Performance Issues

### 1. Slow Configuration Loading

**Solutions:**
- Use S3 Transfer Acceleration
- Optimize JSON file sizes
- Implement caching mechanisms

### 2. Long Automation Execution Times

**Solutions:**
- Increase timeout values
- Optimize PowerShell scripts
- Use parallel execution where possible

### 3. High Resource Usage

**Solutions:**
- Monitor CloudWatch metrics
- Optimize instance types
- Implement resource cleanup

## Monitoring and Alerting

### 1. Set Up CloudWatch Alarms

```bash
aws cloudwatch put-metric-alarm \
    --alarm-name "EC2-Deployment-Failures" \
    --alarm-description "Alert on automation failures" \
    --metric-name "ExecutionsFailed" \
    --namespace "AWS/SSM-RunCommand" \
    --statistic Sum \
    --period 300 \
    --threshold 1 \
    --comparison-operator GreaterThanOrEqualToThreshold \
    --region af-south-1
```

### 2. Create Custom Metrics

Add custom metrics to PowerShell scripts:

```powershell
aws cloudwatch put-metric-data \
    --namespace "EC2Deployment" \
    --metric-data MetricName=ConfigurationLoadTime,Value=30,Unit=Seconds \
    --region af-south-1
```

## Config-and-AD-Validation Runbook Troubleshooting

### Common Validation Issues

#### Configuration Loading Failures

**Error**: "Failed to download base config from S3"
**Solutions**:
- Verify S3 bucket name and region
- Check IAM permissions for S3 access
- Ensure base_config.json exists in configs/ folder

**Error**: "Invalid Asset Owner/Client/Environment"
**Solutions**:
- Check parameter values against base_config.json
- Verify spelling and case sensitivity
- Ensure values exist in the base configuration

#### AD Integration Issues

**Error**: "Webhook API key or URL not found in Secrets Manager"
**Solutions**:
- Verify Secrets Manager secret ARN
- Check secret contains 'webhookApiKey' and 'webhookApiUrl' fields
- Ensure IAM permissions for Secrets Manager access

**Error**: "Webhook API call failed"
**Solutions**:
- Test webhook connectivity manually
- Verify API key is valid and not expired
- Check network connectivity to on-premises webhook server
- Review webhook server logs for errors

#### Test Mode vs Production Mode

**Test Mode** (`TestMode: true`):
- Safe for testing - no actual AD objects created
- Validates configuration and connectivity
- Use for initial testing and troubleshooting

**Production Mode** (`TestMode: false`):
- Creates actual AD computer objects
- Use only after test mode validation passes
- Monitor for actual AD object creation

### Validation Output Interpretation

**Configuration Validation Results**:
- `HasAssetOwner: PASS` - Asset Owner parameter is valid
- `HasDomain: PASS` - Domain configuration found
- `HasTargetOU: PASS` - Target OU configuration found
- `HasLocalAdmins: PASS` - Local admin groups configured

**AD Object Validation Results**:
- `ApiSuccessful: PASS` - Webhook API call succeeded
- `HasComputerName: PASS` - Computer name generated/provided
- `OUMatches: PASS` - Target OU matches actual OU used

**Overall Status**:
- `PASSED` - All validations successful, ready for deployment
- `FAILED` - Issues found, review detailed results

## Getting Help

### 1. Log Analysis

When reporting issues, include:
- Automation execution ID
- CloudWatch log excerpts
- Error messages with timestamps
- Configuration parameters used
- Validation runbook results (if applicable)

### 2. Support Escalation

For complex issues:
1. Run Config-and-AD-Validation runbook first
2. Gather all relevant logs
3. Document reproduction steps
4. Include environment details
5. Provide configuration samples (sanitized)
6. Include validation runbook output

### 3. Community Resources

- AWS Systems Manager documentation
- PowerShell community forums
- Active Directory administration guides
- AWS re:Post for AWS-specific questions

## Preventive Measures

### 1. Regular Testing

- Test configurations in development environment
- Validate all scripts before production use
- Perform regular disaster recovery tests

### 2. Monitoring

- Set up comprehensive monitoring
- Review logs regularly
- Monitor resource usage trends

### 3. Documentation

- Keep documentation updated
- Document any customizations
- Maintain change logs

### 4. Training

- Train team members on troubleshooting
- Create operational runbooks
- Establish escalation procedures
