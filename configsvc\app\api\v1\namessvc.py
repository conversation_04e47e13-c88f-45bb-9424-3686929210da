from typing import Any
from typing import Annotated, Literal, Any
from fastapi import APIRouter, Request, Query
from fastapi.responses import JSONResponse
from fastapi.encoders import jsonable_encoder
from datetime import datetime, date, time
import json
from app import logging
from pydantic import BaseModel, Field
from app.core.config import config

#from app.api.v1.workers import getClient, getIpConfigs, getADConfigs, getVcConfigs, getDeployment, getDcsRates, getVCompute, getVStorage, getVSpecs, getAnnotations
# For HTML pages
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
# from starlette.exceptions import HTTPException as StarletteHTTPException
from wtforms import StringField, FieldList, FormField, SelectField, IntegerField
from wtforms.validators import DataRequired
from starlette.datastructures import FormData, UploadFile


from app.core.config import config
from app.core.helpers import key_exists, list2str, element_exists
#import MariaDB connection
from app.core.database import dbQuery, df2sql, dbUpdateQuery, write_engine, read_engine #engine, 
import pandas as pd
import sqlalchemy ## SQLAlchemy
from sqlalchemy import Column, Integer, String, DateTime, Date, Boolean #, Text, BLOB, Numeric, Date, ForeignKey, orm
from sqlalchemy.ext.declarative import declarative_base


router = APIRouter()
templates = Jinja2Templates(directory="templates")


restart_time = datetime.now()
last_restart = str(restart_time).split(".")[0]

name_request = {
    "reference" : "DEV-001",
    "owner" : "Marcel Dev",
    "client" : "BCXTest",
    "name_type" : "POC",
    "name_count" : 2,
    "override" : False
    }

json_resp = {
    'reference': '',
    'name_issued': '',
    'status': '',
    'owner': '',
    'date_issued': ''
}
class stdData(BaseModel):
    reference: str | None = None
    name_issued: str | None = None
    status: str | None = None
    owner: str | None = None
    date_issued: str | None = None

class stdResponse(BaseModel):
    status: str | None = None
    message: str | None = None
    data: str | None = Field(default=None, examples=[""])
    success: bool

class NameRequest(BaseModel):
    reference: str | None = Field(default=None, examples=["DCS-999"])
    object_type: str = Field(examples=[config.OBJECT_TYPES])
    name_count: int = Field(examples=[2])
    client: str | None = Field(default=None, examples=["BCX-Test"])
    owner: str | None = Field(default=None, examples=["Marcel Vermeulen (G999999)"])
    override: bool = Field(examples=[False])

server_data = []


# POST: Get Name Object
@router.post("/getname",summary="Reserve Names (status=RSVD)", tags=["namesapi"], response_model=stdResponse) # 
async def getname(request: Request):
    """Issue next available name from Names Service"""

    print("\nNEW NAME REQUEST\n")
    params = await request.json() #
    # params = json.loads(NameRequest) #.data)
    # params = NameRequest()
    logging.info(f"Name Request received: {params}")
    response = ProcessRequest(params)
    # response = {"status":"Connection successful"}
    print(response,"\n")
    # response = params
    return JSONResponse(content=jsonable_encoder(response))

# UI Options POST endpoint from worker tasks
@router.post("/setname/",summary="Set Names to USED", tags=["namesapi"], response_model=stdResponse) # 
async def setname(request: Request):
    """Confirm name issued to reference number, set status to USED"""
    params = await request.json() #
    # params = json.loads(request.data)
    logging.info(f"Name confirmation received: {params}")
    response = ProcessSetRequest(params)
    print(response,"\n")
    # response = params
    return JSONResponse(content=jsonable_encoder(response))

# UI Options POST endpoint from worker tasks
@router.get("/listname/",summary="List Name(s) on Ref", tags=["namesapi"], response_model=stdResponse) # 
def listnames(request: Request):
    """List names issued to reference number"""
    params = request.query_params #.json() # await
    # params = json.loads(request.data)
    logging.info(f"Name confirmation received: {params}")
    response = ProcessListRequest(params)
    print(response,"\n")
    # response = params
    return JSONResponse(content=jsonable_encoder(response))

# UI Options POST endpoint from worker tasks
@router.post("/decommname/",summary="List Name(s) on Ref", tags=["namesapi"], response_model=stdResponse) # 
async def decommnames(request: Request):
    """List names issued to reference number"""
    params = await request.json() #  .query_params #
    # params = json.loads(request.data)
    logging.info(f"Name confirmation received: {params}")
    response = ProcessRemoveRequest(params)
    print(response,"\n")
    # response = params
    return JSONResponse(content=jsonable_encoder(response))

Base = declarative_base()

# Names Generator Table
class NamesTable(Base):
    __tablename__ = "namestbl"
    id = Column(Integer, primary_key=True)
    date_issued = Column(DateTime(), nullable=True)    
    reference = Column(String(20), nullable=True)
    object_name = Column(String(20), unique=True, nullable=False)
    object_type = Column(String(10), nullable=True)
    object_os = Column(String(30), nullable=True)
    object_members = Column(String(100), nullable=True)
    app_type = Column(String(20), nullable=True)
    counter = Column(Integer, nullable=False, default=config.INIT_COUNTER)
    object_domain = Column(String(80), nullable=True)
    object_oupath = Column(String(80), nullable=True)
    client = Column(String(40), nullable=True)
    status = Column(String(20), nullable=True)
    owner = Column(String(40), nullable=True)
    comment = Column(String(40), nullable=True)
    # is_virtual = Column(Boolean(), nullable=True)


#init_db(meta,engine)
Base.metadata.create_all(bind=write_engine)

# Query last name used and get next free name
def queryLastname(dct):
    
    print(f"\nQUERY LAST NAME\n{dct}\n")
    prefix = dct['object_type']
    # reference = dct['reference']
    logging.warning(f"Query {prefix} names ...")
    qry = f"\
        SELECT \
            reference, \
            max(object_name) as last_name, \
            left(max(object_name),{len(prefix)}) as name_grp, \
            cast((substring(max(object_name) from {len(prefix)+1})) as unsigned) as last_issued, \
            cast(cast((substring(max(object_name) from {len(prefix)+1})) as unsigned)+1 as char) as next_free, \
            concat(left(max(object_name),{len(prefix)}),'0',cast(cast((substring(max(object_name) from {len(prefix)+1})) as unsigned)+1 as char)) as next_name \
        FROM namestbl \
        WHERE object_name LIKE '{prefix}%' \
        ORDER BY object_name desc \
        ;"

    print(f"QUERY\n{qry}\n")
    dfName = dbQuery(qry)
    logging.error(f"Last Name Found...\n{dfName}\n")
    # logging.warning(f"Query {prefix} names ...")

    return dfName

def getName(dctIn):
    print("\nGET NEXT AVAILABLE NAME\n")
    prefix = str(dctIn['object_type']).strip().upper()
    dfOut = queryLastname(dctIn)
    print(dfOut.shape,"\n",dfOut.tail(3),"\n")

    if dfOut['next_name'][0] == "None":
        logging.warning(f"Name Type: {prefix} => {config.OBJECT_TYPES}")
        if prefix in config.OBJECT_TYPES:
        # if element_exists(config.object_typeS,prefix):
            logging.error(f"object_type {prefix} does not exist, initializing ...")
            # init_counter = 49010
            init_name = prefix +"0" + str(config.INIT_COUNTER) #"0" + str(init_counter)
            init_ref = config.INIT_REF
            owner = "BCX InfraDevOps"
            try:
                counter = dfOut['next_free']
            except:
                counter = config.INIT_COUNTER
            finally:
                counter = config.INIT_COUNTER
                
            # next_name = prefix + "0" + str(next_free)  ### COUNTER 'counter': counter config.INIT_COUNTER, 
            dctInit = {'reference': init_ref, 'object_name': init_name, 'object_type': prefix, 'counter': counter, 'owner': owner, 'client': 'BCXTest', 'status': 'RSVD'}
            dctTMP = {k:[v] for k,v in dctInit.items()}  # WORKAROUND
            dfInit = pd.DataFrame.from_dict(dctTMP)
            dfInit['date_issued'] = str(datetime.today()).split(".")[0]
            # Reserve the initial name 
            df2sql(dfInit,"namestbl","append")
            logging.warning(f"Initialized\n{dctTMP}")

            # Query again
            dfOut = queryLastname(dctIn)
        else:
            dctOut = {}
            msg = f"Name type {prefix} not allowed ..."
            logging.error(msg)
            dctOut['success'] = False
            dctOut['message'] = msg
            dctOut['status'] = "FAILED"
            return dctOut

    # Continue processing
    dctOut = dfOut.to_dict('records')[0]
    logging.warning(f"Issuing {dctOut['next_free']} ...")
    dctOut['success'] = True
    print(dctOut,"\n")
    return dctOut

# Query last name used and get next free name
def checkName(dct):    
    object_name = dct['object_name']
    reference = dct['reference']
    logging.warning(f"Checking if {object_name} is free...")
    qry = f"""SELECT * FROM namestbl WHERE object_name = '{object_name.upper()}';""" # object_name = '{object_name}' AND reference = '{reference}'
    # qry = "SELECT * FROM namestbl;"
    # print(f"QUERY\n{qry}\n")
    dfOut = dbQuery(qry)
    print(dfOut.shape,"\n",dfOut.tail(3),"\n")
    if dfOut.shape[0] == 0:
        msg = (f"Name {object_name} is free!")
        logging.info(msg)
        return {"success" : False, "message": msg}
    else:
        msg = (f"{object_name} already allocated!")
        logging.error(msg)
        return {"success" : True, "message": msg}

# Reserve next free name against reference
def reserveFreeName(dctIN):
    print(f"RESERVE: {dctIN['object_name']}")
    messages = []
    found = checkName(dctIN)
    if not found['success']:
        dctTMP = {k:[v] for k,v in dctIN.items()} 
        dfIN = pd.DataFrame.from_dict(dctTMP)
        dfIN['status'] = "RSVD"
        dfIN['date_issued'] = str(datetime.today()).split(".")[0]
        # print(dfIN.info(),"\n")
        # Upload data to database
        try:
            # Reserve the name 
            print(f"APPEND TO DATABASE:\n{dfIN}\n")
            df2sql(dfIN,"namestbl","append")
            msg = (f"Name reservation completed!")
            messages.append(msg)
            data = dctIN['object_name']
            logging.info(msg)
            print()
            return {"success" : True, "messages": messages, 'data': data}
        except Exception as e:
            msg = (f"Name reservation failed! {str(e)}\n")
            messages.append(msg)
            data = ''
            logging.error(msg)
            print()
            return {"success" : False, "messages": msg, 'data': []}
    else:
        msg = (f"{dctIN['object_name']} already allocated!")
        messages.append(msg)
        logging.error(msg)
        return {"success" : False, "messages": msg}

# Process Request
def ProcessRequest(dctIn):
    names_issued = []
    messages = []
    status = "NORMAL"
    success = False
    count = 0
    resp = {}

    if not key_exists('count', dctIn):
        count = 1 
    else:
        count = int(dctIn['count'])    
        dctRsrv.pop('count')        # Drop Count field from request

    print(f"Name Count Requested: {count}")
    dctRsrv = dctIn.copy()


    if count <= config.MAX_COUNT:
        for x in range(0, count):
            dctFree = getName(dctIn)
            if dctFree['success']:
                object_name = dctFree['next_name']
                # Reserve name
                dctRsrv['object_name'] = object_name
                dctRsrv['counter'] = dctFree['next_free']
                result = reserveFreeName(dctRsrv)
                if result['success']:
                    # messages.append(f"Name Issued -> {object_name}")
                    names_issued.append(object_name)
                    success = True
                else:
                    messages.append(result['messages'])
                    status = "REMEDIATE"
            else:
                messages.append(dctFree['messages'])
                status = dctFree['status']
        messages.append(f"{count} Name(s) Issued -> {names_issued}")
        print(f"NAMES ISSUED: {names_issued}\n")
    else:
        messages.append(f"Name count is exceeding limit of {config.MAX_COUNT}")
    
    resp.update({"success": success, "data" : names_issued, "messages": messages, "status": status})

    return resp

# Query last name used and get next free name
def setName(dct):
    failed = []
    successes= []
    if key_exists('object_name',dct):
        # convert list of names provided to a comma separated string
        lst_names_issued = dct['object_name']
    else:
        lst_names_issued = []
    reference = dct['reference']
    req_count = len(lst_names_issued)
    if req_count > 0:
        names_issued = list2str(lst_names_issued)
        
        print(f"Set Name: {names_issued}\n")
        for x in lst_names_issued: # range(0,req_count):
            qry = f"\
                SELECT * FROM namestbl WHERE object_name = '{x}' AND reference = '{reference}';" #  in {names_issued}

            print(f"QUERY\n{qry}\n")
            dfOut = dbQuery(qry)
            print(dfOut.shape,"\n",dfOut.tail(3),"\n")
            if dfOut.shape[0] == 0:
                msg = (f"{x} not found!")
                logging.error(msg)
                failed.append(x)
            else:
                qry = f"\
                    UPDATE namestbl SET status = 'USED', date_issued=(CURRENT_TIMESTAMP) WHERE object_name = '{x}' AND reference = '{reference}';"
                dfOut = dbUpdateQuery(qry)
                msg = (f"Name(s) set to USED!") 
                logging.warning(msg)
                successes.append(x)
                # return {"success" : True, "message": msg}
        if len(successes) == req_count:
            msg = f"{len(successes)} updated!"
            data = {"updated": successes}
            return {"success" : True, "message": msg, "status": "NORMAL", "data": data}
        else:
            msg = f"{len(failed)} failed!"
            data = {"updated": successes, "failed": failed}
            return {"success" : False, "message": msg, "status": "REMEDIATE", "data": data}
    else:
        qry = f"\
            SELECT * FROM namestbl WHERE reference = '{reference}';"
        print(f"QUERY\n{qry}\n")
        dfOut = dbQuery(qry)
        if dfOut.shape[0] > 0:
            qry = f"\
                UPDATE namestbl SET status = 'USED', date_issued=(CURRENT_TIMESTAMP) WHERE reference = '{reference}';"
            dbUpdateQuery(qry)
            msg = (f"{dfOut.shape[0]} names issued to {reference} set to USED!")
            return {"success" : True, "message": msg, "status": "NORMAL", "data": ""}
        else:
            msg = (f"Names issued to {reference} not found!")
            return {"success" : False, "message": msg, "status": "FAILED", "data": ""}
        
def ProcessSetRequest(dctIn):
    resp = setName(dctIn)
    return resp

# Query last name used and get next free name
def listNames(dct):
    logging.info(f"Listing names for {dct['reference']}...")
    reference = dct['reference']
    qry = f"SELECT * FROM namestbl WHERE reference = '{reference}';"
    print(f"QUERY\n{qry}\n")
    dfOut = dbQuery(qry)
    data = dfOut.to_dict('records')
    
    return {"success" : True, "message": f"{len(data)} found", "data":data, "status": "NORMAL"}

def ProcessListRequest(dctIn):
    resp = listNames(dctIn)
    return resp

def decommNames(dct):
    failed = []
    successes= []
    if not key_exists('names',dct):
        msg = (f"No names provided!")
        logging.error(msg)
        return {"success" : False, "message": msg, "status": "FAILED", "data": ""}
    else:
        lst_decomm_names = dct['names']
        reference = dct['reference']
        req_count = len(lst_decomm_names)
        if req_count > 0:
            # convert list of names provided to a comma separated string
            decomm_names = list2str(lst_decomm_names)        
            print(f"Set Name: {decomm_names}\n")
            for x in lst_decomm_names:
                qry = f"\
                    SELECT * FROM namestbl WHERE object_name = '{x}';"
                print(f"QUERY\n{qry}\n")
                dfOut = dbQuery(qry)
                print(dfOut.shape,"\n",dfOut.tail(3),"\n")
                if dfOut.shape[0] == 0:
                    msg = (f"{x} not found!")
                    logging.error(msg)
                    failed.append(x)
                else:
                    qry = f"\
                        UPDATE namestbl SET status = 'DCMD', decomm_ref='{reference}', decomm_date=(CURRENT_TIMESTAMP) WHERE object_name = '{x}';"
                    dfOut = dbUpdateQuery(qry)
                    msg = (f"Name(s) set to DCMD!") 
                    # logging.warning(msg)
                    successes.append(x)
                    # return {"success" : True, "message": msg}
            if len(successes) == req_count:
                msg = f"{len(successes)} updated to DCMD!"
                data = {"updated": successes}
                return {"success" : True, "message": msg, "status": "NORMAL", "data": data}
            else:
                msg = f"{len(failed)} failed!"
                data = {"updated": successes, "failed": failed}
                return {"success" : False, "message": msg, "status": "REMEDIATE", "data": data}
        else:
            msg = f"{reference} No names provided!"
            data = {"updated": successes, "failed": failed}
            logging.error(msg)
            return {"success" : False, "message": msg, "status": "REMEDIATE", "data": data}

        
def ProcessRemoveRequest(dctIn):
    resp = decommNames(dctIn)
    return resp