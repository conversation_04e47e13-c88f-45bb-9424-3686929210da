{"FirewallRules": [{"Name": "Allow RDP Inbound", "Direction": "in", "Action": "allow", "Protocol": "tcp", "LocalPort": "3389", "Profile": "domain,private", "RemoteAddress": "10.0.0.0/8,**********/12,***********/16", "Description": "Allow RDP from internal networks only"}, {"Name": "Allow WinRM HTTP", "Direction": "in", "Action": "allow", "Protocol": "tcp", "LocalPort": "5985", "Profile": "domain", "RemoteAddress": "10.0.0.0/8", "Description": "Allow WinRM for PowerShell remoting"}, {"Name": "Allow WinRM HTTPS", "Direction": "in", "Action": "allow", "Protocol": "tcp", "LocalPort": "5986", "Profile": "domain", "RemoteAddress": "10.0.0.0/8", "Description": "Allow secure WinRM for PowerShell remoting"}, {"Name": "Allow ICMP Ping", "Direction": "in", "Action": "allow", "Protocol": "icmpv4", "IcmpType": "8", "Profile": "domain,private", "Description": "Allow ping for network diagnostics"}, {"Name": "Block Internet Outbound", "Direction": "out", "Action": "block", "Protocol": "any", "RemoteAddress": "Internet", "Profile": "domain,private,public", "Description": "Block direct internet access for security"}, {"Name": "Allow Internal Network Outbound", "Direction": "out", "Action": "allow", "Protocol": "any", "RemoteAddress": "10.0.0.0/8,**********/12,***********/16", "Profile": "domain,private", "Description": "Allow communication within internal networks"}], "RegistrySettings": [{"Path": "HKLM:\\SOFTWARE\\Policies\\Microsoft\\Windows\\WindowsUpdate\\AU", "Name": "NoAutoUpdate", "Value": 0, "Type": "DWord", "Description": "Enable automatic updates"}, {"Path": "HKLM:\\SOFTWARE\\Policies\\Microsoft\\Windows\\WindowsUpdate\\AU", "Name": "AUOptions", "Value": 4, "Type": "DWord", "Description": "Auto download and schedule install"}, {"Path": "HKLM:\\SOFTWARE\\Policies\\Microsoft\\Windows\\WindowsUpdate", "Name": "WUServer", "Value": "http://wsus.sanlam.co.za:8530", "Type": "String", "Description": "Internal WSUS server for updates"}, {"Path": "HKLM:\\SOFTWARE\\Policies\\Microsoft\\Windows\\WindowsUpdate", "Name": "WUStatusServer", "Value": "http://wsus.sanlam.co.za:8530", "Type": "String", "Description": "Internal WSUS status server"}, {"Path": "HKLM:\\SYSTEM\\CurrentControlSet\\Control\\Terminal Server", "Name": "fDenyTSConnections", "Value": 0, "Type": "DWord", "Description": "Enable Remote Desktop"}, {"Path": "HKLM:\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\System", "Name": "EnableLUA", "Value": 1, "Type": "DWord", "Description": "Enable User Account Control"}, {"Path": "HKLM:\\SOFTWARE\\Policies\\Microsoft\\Windows\\EventLog\\Application", "Name": "MaxSize", "Value": 32768, "Type": "DWord", "Description": "Set Application log size to 32MB"}, {"Path": "HKLM:\\SOFTWARE\\Policies\\Microsoft\\Windows\\EventLog\\Security", "Name": "MaxSize", "Value": 131072, "Type": "DWord", "Description": "Set Security log size to 128MB"}, {"Path": "HKLM:\\SOFTWARE\\Policies\\Microsoft\\Windows\\EventLog\\System", "Name": "MaxSize", "Value": 32768, "Type": "DWord", "Description": "Set System log size to 32MB"}, {"Path": "HKLM:\\SOFTWARE\\Sanlam\\Deployment", "Name": "Environment", "Value": "DEV", "Type": "String", "Description": "Mark server environment for identification"}, {"Path": "HKLM:\\SOFTWARE\\Sanlam\\Deployment", "Name": "DeploymentDate", "Value": "{{DEPLOYMENT_DATE}}", "Type": "String", "Description": "Record deployment date (replaced at runtime)"}], "SoftwarePackages": [{"Name": "Sanlam Security Agent", "S3Key": "security/sanlam-security-agent-v2.1.msi", "FileName": "san<PERSON>-security-agent.msi", "TargetPath": "C:\\Temp\\ServerInstalls\\sanlam-security-agent.msi", "InstallCommand": "msiexec.exe /i C:\\Temp\\ServerInstalls\\sanlam-security-agent.msi /quiet /norestart ENVIRONMENT=DEV", "ValidateCommand": "Get-Service -Name 'SanlamSecurityAgent' -ErrorAction SilentlyContinue", "Description": "Company security monitoring agent"}, {"Name": "Sanlam Certificate Authority", "S3Key": "security/sanlam-root-ca.cer", "FileName": "sanlam-root-ca.cer", "TargetPath": "C:\\Temp\\ServerInstalls\\sanlam-root-ca.cer", "InstallCommand": "certlm.exe -add C:\\Temp\\ServerInstalls\\sanlam-root-ca.cer -s -r localMachine root", "ValidateCommand": "Get-ChildItem -Path Cert:\\LocalMachine\\Root | Where-Object {$_.Subject -like '*Sanlam*'}", "Description": "Install company root certificate"}, {"Name": "Windows Defender Definition Update", "S3Key": "security/mpam-fe.exe", "FileName": "mpam-fe.exe", "TargetPath": "C:\\Temp\\ServerInstalls\\mpam-fe.exe", "InstallCommand": "C:\\Temp\\ServerInstalls\\mpam-fe.exe", "ValidateCommand": "Get-MpComputerStatus | Select-Object AntivirusSignatureLastUpdated", "Description": "Latest Windows Defender definitions"}], "WindowsFeatures": [{"Name": "IIS-WebServerRole", "Action": "Disable", "Description": "Disable IIS web server role for security"}, {"Name": "SMB1Protocol", "Action": "Disable", "Description": "Disable insecure SMB1 protocol"}, {"Name": "TelnetClient", "Action": "Disable", "Description": "Disable Telnet client for security"}, {"Name": "TFTP", "Action": "Disable", "Description": "Disable TFTP client for security"}], "Services": [{"Name": "<PERSON>pooler", "StartupType": "Disabled", "Status": "Stopped", "Description": "Disable print spooler service if not needed"}, {"Name": "RemoteRegistry", "StartupType": "Disabled", "Status": "Stopped", "Description": "Disable remote registry service for security"}, {"Name": "WinRM", "StartupType": "Automatic", "Status": "Running", "Description": "Enable WinRM for remote management"}, {"Name": "Windows Update", "StartupType": "Automatic", "Status": "Running", "Description": "Ensure Windows Update service is running"}], "AuditPolicies": [{"Category": "Logon/Logoff", "Subcategory": "Logon", "Setting": "Success,Failure", "Description": "Audit all logon attempts"}, {"Category": "Logon/Logoff", "Subcategory": "<PERSON><PERSON><PERSON>", "Setting": "Success", "Description": "Audit successful logoffs"}, {"Category": "Account Management", "Subcategory": "User Account Management", "Setting": "Success,Failure", "Description": "Audit user account changes"}, {"Category": "Policy Change", "Subcategory": "Authentication Policy Change", "Setting": "Success,Failure", "Description": "Audit authentication policy changes"}, {"Category": "Privilege Use", "Subcategory": "Sensitive Privilege Use", "Setting": "Success,Failure", "Description": "Audit sensitive privilege usage"}], "LocalSecurityPolicies": [{"Policy": "SeNetworkLogonRight", "Accounts": ["Domain Admins", "Authenticated Users"], "Description": "Who can log on over the network"}, {"Policy": "SeRemoteInteractiveLogonRight", "Accounts": ["Domain Admins", "Server Operators"], "Description": "Who can log on via Remote Desktop"}, {"Policy": "SeDenyNetworkLogonRight", "Accounts": ["Guest"], "Description": "Deny network logon for Guest account"}, {"Policy": "SeServiceLogonRight", "Accounts": ["NT SERVICE\\ALL SERVICES"], "Description": "Service logon rights"}], "PowerShellExecutionPolicy": {"Scope": "LocalMachine", "ExecutionPolicy": "RemoteSigned", "Description": "Allow local scripts and signed remote scripts"}, "EnvironmentVariables": [{"Name": "SANLAM_ENVIRONMENT", "Value": "DEV", "Scope": "Machine", "Description": "Environment identifier for applications"}, {"Name": "SANLAM_DEPLOYMENT_TYPE", "Value": "Automated", "Scope": "Machine", "Description": "Deployment method identifier"}]}