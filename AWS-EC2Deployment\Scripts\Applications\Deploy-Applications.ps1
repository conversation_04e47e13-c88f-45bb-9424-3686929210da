<#
    .NOTES
    File Name: Deploy-Applications.ps1
    Author: <PERSON><PERSON>
    Version: 1.0.0 - 03/09/2025
    Description: Deploys applications specific to EC2 instances based on configuration
    Note: This script is designed to be called from SSM Automation runbooks

    .NOTES
    Version 1.0.0     - Base Script
#>

param(
    [Parameter(Mandatory=$true)]
    [hashtable]$DeploymentConfig,
    
    [Parameter(Mandatory=$true)]
    [string]$S3BucketName,
    
    [Parameter(Mandatory=$false)]
    [string]$S3ApplicationPrefix = "applications",
    
    [Parameter(Mandatory=$false)]
    [string]$LocalDownloadPath = "C:\Temp\ServerInstalls",
    
    [Parameter(Mandatory=$false)]
    [string]$Region = "af-south-1",
    
    [Parameter(Mandatory=$false)]
    [array]$ApplicationList = @(),
    
    [Parameter(Mandatory=$false)]
    [switch]$CleanupAfterInstall = $true,
    
    [Parameter(Mandatory=$false)]
    [switch]$OutputJson = $false
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Function to write structured log messages
function Write-LogMessage {
    param(
        [string]$Message,
        [ValidateSet("INFO", "WARN", "ERROR", "SUCCESS")]
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    switch ($Level) {
        "INFO" { Write-Host $logMessage -ForegroundColor Cyan }
        "WARN" { Write-Host $logMessage -ForegroundColor Yellow }
        "ERROR" { Write-Host $logMessage -ForegroundColor Red }
        "SUCCESS" { Write-Host $logMessage -ForegroundColor Green }
    }
}

# Function to create download directory
function New-DownloadDirectory {
    param(
        [string]$Path
    )
    
    try {
        if (-not (Test-Path $Path)) {
            Write-LogMessage "Creating download directory: $Path" "INFO"
            New-Item -Path $Path -ItemType Directory -Force | Out-Null
            Write-LogMessage "Download directory created successfully" "SUCCESS"
        } else {
            Write-LogMessage "Download directory already exists: $Path" "INFO"
        }
        
        return $Path
        
    } catch {
        Write-LogMessage "Failed to create download directory: $($_.Exception.Message)" "ERROR"
        throw
    }
}

# Function to get application list from configuration or parameter
function Get-ApplicationList {
    param(
        [hashtable]$Config,
        [array]$ExplicitList
    )
    
    try {
        Write-LogMessage "Determining application list for deployment" "INFO"
        
        $applications = @()
        
        # Use explicit list if provided
        if ($ExplicitList -and $ExplicitList.Count -gt 0) {
            $applications = $ExplicitList
            Write-LogMessage "Using explicit application list: $($applications -join ', ')" "INFO"
        } else {
            # Try to get applications from configuration
            $configKey = "$($Config.AssetOwner)_$($Config.AppType)_$($Config.Environment)"
            
            # Look for applications in various configuration patterns
            $possibleSources = @(
                "Applications",
                "RequiredApplications", 
                "Software",
                "Packages"
            )
            
            foreach ($source in $possibleSources) {
                if ($Config.$source) {
                    $applications = $Config.$source
                    Write-LogMessage "Found applications in config.$($source): $($applications -join ', ')" "INFO"
                    break
                }
            }
            
            # If no applications found in config, use default based on app type
            if ($applications.Count -eq 0) {
                switch ($Config.AppType) {
                    "MSSQL" {
                        $applications = @("sql-server-management-studio", "sql-server-tools")
                    }
                    "Shared" {
                        $applications = @("office-365", "chrome", "notepad-plus-plus")
                    }
                    default {
                        $applications = @("chrome", "notepad-plus-plus")
                    }
                }
                Write-LogMessage "Using default applications for $($Config.AppType): $($applications -join ', ')" "INFO"
            }
        }
        
        Write-LogMessage "Final application list: $($applications -join ', ')" "SUCCESS"
        return $applications
        
    } catch {
        Write-LogMessage "Failed to determine application list: $($_.Exception.Message)" "ERROR"
        throw
    }
}

# Function to download application from S3
function Get-ApplicationFromS3 {
    param(
        [string]$ApplicationName,
        [string]$BucketName,
        [string]$S3Prefix,
        [string]$LocalPath,
        [string]$Region
    )
    
    try {
        Write-LogMessage "Downloading application: $ApplicationName" "INFO"
        
        # Construct S3 key - try multiple patterns
        $possibleKeys = @(
            "$S3Prefix/$ApplicationName.msi",
            "$S3Prefix/$ApplicationName.exe",
            "$S3Prefix/$ApplicationName/$ApplicationName.msi",
            "$S3Prefix/$ApplicationName/$ApplicationName.exe",
            "$S3Prefix/$ApplicationName/setup.msi",
            "$S3Prefix/$ApplicationName/setup.exe"
        )
        
        $downloadedFile = $null
        
        foreach ($key in $possibleKeys) {
            try {
                Write-LogMessage "Trying S3 key: $key" "INFO"
                
                # Check if object exists
                $objectExists = aws s3 ls "s3://$BucketName/$key" --region $Region 2>&1
                if ($LASTEXITCODE -eq 0) {
                    # Download the file
                    $fileName = Split-Path $key -Leaf
                    $localFile = Join-Path $LocalPath $fileName
                    
                    aws s3 cp "s3://$BucketName/$key" $localFile --region $Region | Out-Null
                    
                    if ($LASTEXITCODE -eq 0 -and (Test-Path $localFile)) {
                        $downloadedFile = $localFile
                        Write-LogMessage "Successfully downloaded: $fileName" "SUCCESS"
                        break
                    }
                }
            } catch {
                # Continue to next key pattern
                continue
            }
        }
        
        if (-not $downloadedFile) {
            throw "Application '$ApplicationName' not found in S3 bucket with any expected pattern"
        }
        
        return @{
            ApplicationName = $ApplicationName
            LocalPath = $downloadedFile
            FileName = Split-Path $downloadedFile -Leaf
            FileSize = (Get-Item $downloadedFile).Length
        }
        
    } catch {
        Write-LogMessage "Failed to download application '$ApplicationName': $($_.Exception.Message)" "ERROR"
        throw
    }
}

# Function to install application
function Install-Application {
    param(
        [object]$ApplicationInfo
    )
    
    try {
        Write-LogMessage "Installing application: $($ApplicationInfo.ApplicationName)" "INFO"
        
        $installResult = @{
            ApplicationName = $ApplicationInfo.ApplicationName
            Success = $false
            ExitCode = -1
            Output = ""
            Error = ""
            InstallTime = Get-Date
        }
        
        $extension = [System.IO.Path]::GetExtension($ApplicationInfo.LocalPath).ToLower()
        
        switch ($extension) {
            ".msi" {
                Write-LogMessage "Installing MSI package: $($ApplicationInfo.FileName)" "INFO"
                $process = Start-Process -FilePath "msiexec.exe" -ArgumentList "/i", "`"$($ApplicationInfo.LocalPath)`"", "/quiet", "/norestart" -Wait -PassThru -NoNewWindow
                $installResult.ExitCode = $process.ExitCode
            }
            ".exe" {
                Write-LogMessage "Installing EXE package: $($ApplicationInfo.FileName)" "INFO"
                # Try common silent install parameters
                $silentArgs = @("/S", "/SILENT", "/QUIET", "/q", "/s")
                $success = $false
                
                foreach ($arg in $silentArgs) {
                    try {
                        $process = Start-Process -FilePath $ApplicationInfo.LocalPath -ArgumentList $arg -Wait -PassThru -NoNewWindow -ErrorAction Stop
                        if ($process.ExitCode -eq 0) {
                            $installResult.ExitCode = $process.ExitCode
                            $success = $true
                            break
                        }
                    } catch {
                        continue
                    }
                }
                
                if (-not $success) {
                    # Try without arguments as last resort
                    $process = Start-Process -FilePath $ApplicationInfo.LocalPath -Wait -PassThru -NoNewWindow
                    $installResult.ExitCode = $process.ExitCode
                }
            }
            default {
                throw "Unsupported file type: $extension"
            }
        }
        
        # Check if installation was successful
        if ($installResult.ExitCode -eq 0) {
            $installResult.Success = $true
            Write-LogMessage "Application '$($ApplicationInfo.ApplicationName)' installed successfully" "SUCCESS"
        } else {
            $installResult.Error = "Installation failed with exit code: $($installResult.ExitCode)"
            Write-LogMessage "Application '$($ApplicationInfo.ApplicationName)' installation failed with exit code: $($installResult.ExitCode)" "ERROR"
        }
        
        return $installResult
        
    } catch {
        Write-LogMessage "Failed to install application '$($ApplicationInfo.ApplicationName)': $($_.Exception.Message)" "ERROR"
        
        return @{
            ApplicationName = $ApplicationInfo.ApplicationName
            Success = $false
            ExitCode = -1
            Error = $_.Exception.Message
            InstallTime = Get-Date
        }
    }
}

# Main execution logic
try {
    Write-LogMessage "Starting Application Deployment" "INFO"
    Write-LogMessage "S3 Bucket: $S3BucketName, Prefix: $S3ApplicationPrefix" "INFO"
    Write-LogMessage "Deployment Config: $($DeploymentConfig.AssetOwner)/$($DeploymentConfig.AppType)/$($DeploymentConfig.Environment)" "INFO"

    # Step 1: Create download directory
    $downloadDir = New-DownloadDirectory -Path $LocalDownloadPath

    # Step 2: Get application list
    $applications = Get-ApplicationList -Config $DeploymentConfig -ExplicitList $ApplicationList

    if ($applications.Count -eq 0) {
        Write-LogMessage "No applications to deploy" "WARN"
        $finalResult = @{
            Success = $true
            Message = "No applications to deploy"
            Applications = @()
            Summary = @{
                Total = 0
                Downloaded = 0
                Installed = 0
                Failed = 0
            }
            Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss UTC"
        }
    } else {
        # Step 3: Download and install applications
        $deploymentResults = @()
        $downloadedFiles = @()

        foreach ($app in $applications) {
            try {
                Write-LogMessage "Processing application: $app" "INFO"

                # Download application
                $appInfo = Get-ApplicationFromS3 -ApplicationName $app -BucketName $S3BucketName -S3Prefix $S3ApplicationPrefix -LocalPath $downloadDir -Region $Region
                $downloadedFiles += $appInfo.LocalPath

                # Install application
                $installResult = Install-Application -ApplicationInfo $appInfo

                $deploymentResults += @{
                    ApplicationName = $app
                    Downloaded = $true
                    DownloadPath = $appInfo.LocalPath
                    FileSize = $appInfo.FileSize
                    InstallResult = $installResult
                }

            } catch {
                Write-LogMessage "Failed to process application '$app': $($_.Exception.Message)" "ERROR"

                $deploymentResults += @{
                    ApplicationName = $app
                    Downloaded = $false
                    Error = $_.Exception.Message
                    InstallResult = @{
                        Success = $false
                        Error = $_.Exception.Message
                    }
                }
            }
        }

        # Step 4: Calculate summary
        $summary = @{
            Total = $applications.Count
            Downloaded = ($deploymentResults | Where-Object { $_.Downloaded }).Count
            Installed = ($deploymentResults | Where-Object { $_.InstallResult.Success }).Count
            Failed = ($deploymentResults | Where-Object { -not $_.InstallResult.Success }).Count
        }

        # Step 5: Cleanup downloaded files if requested
        if ($CleanupAfterInstall) {
            Write-LogMessage "Cleaning up downloaded files" "INFO"
            foreach ($file in $downloadedFiles) {
                try {
                    if (Test-Path $file) {
                        Remove-Item $file -Force
                        Write-LogMessage "Cleaned up: $(Split-Path $file -Leaf)" "INFO"
                    }
                } catch {
                    Write-LogMessage "Failed to cleanup file: $file" "WARN"
                }
            }

            # Remove download directory if empty
            try {
                if ((Get-ChildItem $downloadDir -ErrorAction SilentlyContinue).Count -eq 0) {
                    Remove-Item $downloadDir -Force
                    Write-LogMessage "Removed empty download directory" "INFO"
                }
            } catch {
                Write-LogMessage "Failed to remove download directory" "WARN"
            }
        }

        # Step 6: Build final result object
        $finalResult = @{
            Success = ($summary.Failed -eq 0)
            Applications = $deploymentResults
            Summary = $summary
            DeploymentConfig = $DeploymentConfig
            S3Bucket = $S3BucketName
            S3Prefix = $S3ApplicationPrefix
            DownloadPath = $LocalDownloadPath
            CleanupPerformed = $CleanupAfterInstall
            Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss UTC"
        }
    }

    # Output results
    if ($OutputJson) {
        Write-Output ($finalResult | ConvertTo-Json -Depth 10)
    } else {
        Write-LogMessage "=== APPLICATION DEPLOYMENT COMPLETED ===" "SUCCESS"
        Write-LogMessage "Total Applications: $($finalResult.Summary.Total)" "INFO"
        Write-LogMessage "Successfully Downloaded: $($finalResult.Summary.Downloaded)" "INFO"
        Write-LogMessage "Successfully Installed: $($finalResult.Summary.Installed)" "INFO"
        Write-LogMessage "Failed: $($finalResult.Summary.Failed)" "INFO"
        Write-LogMessage "Overall Success: $($finalResult.Success)" $(if ($finalResult.Success) { "SUCCESS" } else { "ERROR" })

        # Show details for failed applications
        $failedApps = $finalResult.Applications | Where-Object { -not $_.InstallResult.Success }
        if ($failedApps) {
            Write-LogMessage "Failed Applications:" "ERROR"
            foreach ($failed in $failedApps) {
                Write-LogMessage "  - $($failed.ApplicationName): $($failed.InstallResult.Error)" "ERROR"
            }
        }
    }

    # Return result for use in runbook
    return $finalResult

} catch {
    Write-LogMessage "Application deployment failed: $($_.Exception.Message)" "ERROR"

    $errorResult = @{
        Success = $false
        Error = $true
        Message = $_.Exception.Message
        DeploymentConfig = $DeploymentConfig
        S3Bucket = $S3BucketName
        ApplicationList = $ApplicationList
        ScriptName = $MyInvocation.MyCommand.Name
        LineNumber = $_.InvocationInfo.ScriptLineNumber
        Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss UTC"
    }

    if ($OutputJson) {
        Write-Output ($errorResult | ConvertTo-Json -Depth 5)
    }

    exit 1
}
