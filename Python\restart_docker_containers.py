#!/usr/bin/env python3
"""
Docker Failed Container Restart Script
This script identifies and restarts only containers that are in a failed/error state.
"""

import subprocess
import sys
import time
from typing import List, Tuple


def run_command(command: List[str]) -> Tuple[bool, str, str]:
    """
    Execute a command and return success status, stdout, and stderr.
    
    Args:
        command: List of command parts to execute
        
    Returns:
        Tuple of (success, stdout, stderr)
    """
    try:
        result = subprocess.run(
            command,
            capture_output=True,
            text=True,
            timeout=300  # 5 minute timeout
        )
        return result.returncode == 0, result.stdout.strip(), result.stderr.strip()
    except subprocess.TimeoutExpired:
        return False, "", "Command timed out after 5 minutes"
    except Exception as e:
        return False, "", str(e)


def check_docker_available() -> bool:
    """Check if Docker is available and running."""
    success, stdout, stderr = run_command(["docker", "version"])
    if not success:
        print(f"❌ Docker is not available or not running: {stderr}")
        return False
    print("✅ Docker is available and running")
    return True


def get_failed_containers() -> List[str]:
    """Get list of container IDs that are in failed/error state."""
    failed_containers = []

    # Get containers that exited with non-zero exit code
    success, stdout, stderr = run_command([
        "docker", "ps", "-a", "--filter", "status=exited",
        "--format", "{{.ID}} {{.Status}}"
    ])
    if success and stdout:
        for line in stdout.split('\n'):
            if line.strip():
                parts = line.strip().split()
                if len(parts) >= 3:
                    # Check if exit code is not 0
                    try:
                        exit_code = parts[2].strip('()')
                        if exit_code != "0":
                            failed_containers.append(parts[0])
                    except (IndexError, ValueError):
                        pass

    # Get containers in restarting state
    success, stdout, stderr = run_command([
        "docker", "ps", "-a", "--filter", "status=restarting", "--format", "{{.ID}}"
    ])
    if success and stdout:
        failed_containers.extend([line.strip() for line in stdout.split('\n') if line.strip()])

    # Get dead containers
    success, stdout, stderr = run_command([
        "docker", "ps", "-a", "--filter", "status=dead", "--format", "{{.ID}}"
    ])
    if success and stdout:
        failed_containers.extend([line.strip() for line in stdout.split('\n') if line.strip()])

    # Remove duplicates and return
    return list(set(failed_containers))


def get_container_info(container_id: str) -> Tuple[str, str]:
    """Get container name and status."""
    # Get container name
    success, name, stderr = run_command(["docker", "inspect", "--format", "{{.Name}}", container_id])
    if not success:
        name = "unknown"
    else:
        name = name.lstrip('/')

    # Get container status
    success, status, stderr = run_command(["docker", "inspect", "--format", "{{.State.Status}}", container_id])
    if not success:
        status = "unknown"

    return name, status


def restart_failed_containers() -> bool:
    """Restart only containers that are in failed/error state."""
    failed_containers = get_failed_containers()

    if not failed_containers:
        print("✅ No failed containers found - all containers are healthy!")
        return True

    print(f"🔍 Found {len(failed_containers)} failed containers:")
    for container_id in failed_containers:
        name, status = get_container_info(container_id)
        print(f"  - {name} ({container_id}): {status}")

    print(f"\n🔄 Restarting failed containers...")

    success_count = 0
    fail_count = 0

    for container_id in failed_containers:
        name, _ = get_container_info(container_id)
        print(f"  � Restarting {name} ({container_id})...")

        success, stdout, stderr = run_command(["docker", "restart", container_id])
        if success:
            print(f"    ✅ Successfully restarted {name}")
            success_count += 1
        else:
            print(f"    ❌ Failed to restart {name}: {stderr}")
            fail_count += 1

    if fail_count == 0:
        print(f"\n✅ Successfully restarted all {success_count} failed containers")
        return True
    else:
        print(f"\n⚠️  Restarted {success_count} containers, but {fail_count} failed")
        return False


def show_container_status():
    """Display current container status with health information."""
    print("\n📊 Current container status:")
    success, stdout, stderr = run_command(["docker", "ps", "-a", "--format", "table {{.Names}}\t{{.Status}}\t{{.Ports}}"])

    if success and stdout:
        print(stdout)

        # Check for any remaining failed containers
        remaining_failed = get_failed_containers()
        if remaining_failed:
            print(f"\n⚠️  Warning: {len(remaining_failed)} containers still in failed state")
        else:
            print("\n✅ All containers are in healthy state")
    else:
        print("No containers found or failed to get status")


def main():
    """Main function to restart failed Docker containers."""
    print("🐳 Docker Failed Container Restart Script")
    print("=" * 46)

    # Check if Docker is available
    if not check_docker_available():
        sys.exit(1)

    # Show current status first
    print("\n� Checking current container status...")
    show_container_status()

    # Restart only failed containers
    print("\n🔄 Identifying and restarting failed containers...")
    if not restart_failed_containers():
        print("⚠️  Some containers could not be restarted, but continuing...")

    # Wait a moment for containers to initialize
    print("\n⏳ Waiting 5 seconds for containers to initialize...")
    time.sleep(5)

    # Show final status
    show_container_status()

    print("\n✅ Failed container restart process completed!")


if __name__ == "__main__":
    main()
