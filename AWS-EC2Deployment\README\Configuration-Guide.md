# Configuration Guide

This guide explains how to configure the AWS EC2 Deployment Automation system.

## Configuration File Structure

### 1. Base Configuration (base_config.json)

The base configuration file defines the valid combinations of deployment parameters.

```json
{
    "ASSET_OWNER": [
        "Sanlam",
        "Santam", 
        "Retail Mass"
    ],
    "Sanlam": {
        "APP_TYPE": ["Shared", "MSSQL"],
        "ENV": ["PRD", "PPE", "DEV"],
        "OS_TYPE": ["Windows"],
        "OS_VERSION": [
            "Windows Server 2022",
            "Windows Server 2019",
            "Windows Server 2025"
        ],
        "CLIENT": ["SGT", "SPF", "GLC", "SC", "MWL"]
    }
}
```

**Key Fields:**
- `ASSET_OWNER`: List of valid asset owners
- `{AssetOwner}.APP_TYPE`: Valid application types for this asset owner
- `{AssetOwner}.ENV`: Valid environments
- `{AssetOwner}.OS_VERSION`: Supported OS versions
- `{AssetOwner}.CLIENT`: Valid client codes

### 2. Specific Configuration ({AssetOwner}_{AppType}_Config.json)

Example: `Sanlam_Shared_Config.json`

```json
{
    "SGT": {
        "PRD": {
            "DOMAIN": "sanlam.co.za",
            "BASE_PATH": "OU=Servers,OU=SGT,OU=Businesses,DC=sanlam,DC=co,DC=za",
            "LOCAL_ADM": ["MUD\\account1", "MUD\\account2"],
            "OS_VERSIONS": {
                "Windows Server 2022": {
                    "OU": "OU=Server 2022,OU=Windows Server,OU=Servers,OU=SGT,OU=Businesses,DC=sanlam,DC=co,DC=za"
                }
            }
        }
    }
}
```

**Key Fields:**
- `{Client}.{Environment}.DOMAIN`: Target domain for domain join
- `{Client}.{Environment}.BASE_PATH`: Base OU path
- `{Client}.{Environment}.LOCAL_ADM`: Local administrator groups to add
- `{Client}.{Environment}.OS_VERSIONS.{OSVersion}.OU`: Specific OU for this OS version

## Secrets Manager Configuration

### Domain Join Secret Structure

The Secrets Manager secret must contain the following JSON structure:

```json
{
    "domainName": "sanlam.co.za",
    "domainJoinUserName": "DOMAIN\\ServiceAccount",
    "domainJoinPassword": "SecurePassword123!",
    "defaultTargetOU": "OU=Servers,OU=Default,DC=sanlam,DC=co,DC=za"
}
```

**Required Fields:**
- `domainJoinUserName`: Service account with domain join permissions
- `domainJoinPassword`: Password for the service account
- `domainName`: (Optional) Domain name override
- `defaultTargetOU`: (Optional) Fallback OU if specific OU not found

### Service Account Permissions

The domain service account requires the following Active Directory permissions:

1. **Computer Object Management:**
   - Create computer objects in target OUs
   - Delete computer objects (for cleanup)
   - Reset computer account passwords
   - Read/Write computer object properties

2. **OU Permissions:**
   - Read permissions on all target OUs
   - Create child objects permission in target OUs

3. **Domain Join Rights:**
   - Add workstations to domain (if not granted via OU permissions)

## S3 Bucket Structure

### Required Directory Structure

```
s3://sgt-imagebuilder/
└── windows/
    ├── config/
    │   ├── base_config.json
    │   ├── Sanlam_Shared_Config.json
    │   ├── Sanlam_MSSQL_Config.json
    │   ├── Santam_Shared_Config.json
    │   └── ...
    ├── scripts/
    │   ├── Get-DeploymentConfiguration.ps1
    │   ├── New-ADComputerObject.ps1
    │   ├── Join-DomainWithLocalAdmins.ps1
    │   ├── Deploy-Applications.ps1
    │   └── Apply-Customizations.ps1
    ├── applications/
    │   ├── office-365.msi
    │   ├── chrome.exe
    │   ├── sql-server-management-studio/
    │   │   └── setup.exe
    │   └── ...
    └── customizations/
        ├── Sanlam_Shared_DEV_customizations.json
        ├── firewall-rules.json
        └── registry-settings.json
```

## System Customizations Configuration

### Customizations Configuration File Structure

Customizations configuration files follow the naming pattern: `{AssetOwner}_{AppType}_{Environment}_customizations.json`

Examples provided:
- `Sanlam_Shared_DEV_customizations.json` - Development shared servers
- `Sanlam_MSSQL_PRD_customizations.json` - Production SQL servers

#### Complete Customizations Configuration Structure

```json
{
    "FirewallRules": [
        {
            "Name": "Allow RDP Inbound",
            "Direction": "in|out",
            "Action": "allow|block",
            "Protocol": "tcp|udp|icmpv4|any",
            "LocalPort": "3389",
            "RemoteAddress": "10.0.0.0/8,**********/12",
            "Profile": "domain,private,public",
            "IcmpType": "8",
            "Description": "Human-readable description"
        }
    ],
    "RegistrySettings": [
        {
            "Path": "HKLM:\\SOFTWARE\\Policies\\Microsoft\\Windows",
            "Name": "SettingName",
            "Value": "SettingValue",
            "Type": "String|DWord|Binary|MultiString|ExpandString",
            "Description": "What this setting does"
        }
    ],
    "SoftwarePackages": [
        {
            "Name": "Security Agent",
            "S3Key": "security/agent.msi",
            "FileName": "agent.msi",
            "TargetPath": "C:\\Temp\\agent.msi",
            "InstallCommand": "msiexec.exe /i C:\\Temp\\agent.msi /quiet",
            "ValidateCommand": "Get-Service -Name 'AgentService'",
            "Description": "Company security monitoring agent"
        }
    ],
    "WindowsFeatures": [
        {
            "Name": "IIS-WebServerRole",
            "Action": "Enable|Disable",
            "Description": "Enable or disable Windows features"
        }
    ],
    "Services": [
        {
            "Name": "Spooler",
            "StartupType": "Automatic|Manual|Disabled",
            "Status": "Running|Stopped",
            "Description": "Service configuration"
        }
    ],
    "AuditPolicies": [
        {
            "Category": "Logon/Logoff",
            "Subcategory": "Logon",
            "Setting": "Success,Failure|Success|Failure|None",
            "Description": "Audit policy configuration"
        }
    ],
    "LocalSecurityPolicies": [
        {
            "Policy": "SeRemoteInteractiveLogonRight",
            "Accounts": ["Domain Admins", "Server Operators"],
            "Description": "User rights assignment"
        }
    ],
    "PowerShellExecutionPolicy": {
        "Scope": "LocalMachine|CurrentUser",
        "ExecutionPolicy": "Restricted|AllSigned|RemoteSigned|Unrestricted",
        "Description": "PowerShell execution policy"
    },
    "EnvironmentVariables": [
        {
            "Name": "COMPANY_ENVIRONMENT",
            "Value": "DEV",
            "Scope": "Machine|User",
            "Description": "Environment identifier"
        }
    ]
}
```

### Application Configuration

Applications can be specified in multiple ways:

1. **In the specific configuration file:**
```json
{
    "SGT": {
        "PRD": {
            "Applications": ["office-365", "chrome", "sql-tools"]
        }
    }
}
```

2. **Default based on AppType:**
- `MSSQL`: sql-server-management-studio, sql-server-tools
- `Shared`: office-365, chrome, notepad-plus-plus

## Validation and Testing

### AWS Systems Manager Validation Runbook (Recommended)

The `Config-and-AD-Validation.yaml` runbook provides comprehensive testing of your entire configuration and AD integration:

#### Running the Validation Runbook

1. **Navigate to AWS Systems Manager > Automation**
2. **Search for**: `Config-and-AD-Validation`
3. **Execute with parameters**:

```yaml
AssetOwner: "Sanlam"
AppType: "Shared"
Client: "SGT"
Environment: "DEV"
OSVersion: "Windows Server 2022"
S3ConfigBucket: "your-config-bucket"
SecretsManagerSecretArn: "arn:aws:secretsmanager:af-south-1:account:secret:your-secret"
TestMode: "true"  # Use true for safe testing
ComputerName: ""  # Optional - will auto-generate if empty
Region: "af-south-1"
```

#### What Gets Validated

**Configuration Loading**:
- ✅ Base configuration file existence and validity
- ✅ Specific configuration file loading (with fallback to shared)
- ✅ Parameter validation against allowed values
- ✅ Client/Environment/OS configuration extraction
- ✅ Domain, OU, and local admin settings

**AD Integration**:
- ✅ Secrets Manager access and credential retrieval
- ✅ Webhook API connectivity and configuration
- ✅ AD object creation simulation (test mode) or actual creation
- ✅ Error handling and fallback OU configuration

**Results Validation**:
- ✅ Comprehensive validation of all configuration elements
- ✅ AD object creation status verification
- ✅ Overall system readiness assessment

#### Test Mode vs Production Mode

- **Test Mode** (`TestMode: "true"`):
  - Simulates webhook API calls
  - Validates configuration and connectivity
  - No actual AD objects created
  - Safe for testing and validation

- **Production Mode** (`TestMode: "false"`):
  - Makes actual webhook API calls
  - Creates real AD computer objects
  - Use only after test mode validation passes

### Configuration Validation Script

Use the PowerShell validation script for local testing:

```powershell
.\Scripts\Configuration\Test-DeploymentConfiguration.ps1 `
    -S3BucketName "your-config-bucket" `
    -ValidateAllConfigs `
    -OutputReport
```

### Manual Testing

1. **Test S3 Access:**
```bash
aws s3 ls s3://your-config-bucket/configs/ --region af-south-1
```

2. **Test Secrets Manager:**
```bash
aws secretsmanager get-secret-value --secret-id your-secret-arn --region af-south-1
```

3. **Test Configuration Loading:**
```powershell
.\Scripts\Configuration\Get-DeploymentConfiguration.ps1 `
    -AssetOwner "Sanlam" `
    -AppType "Shared" `
    -Client "SGT" `
    -Environment "DEV" `
    -OSVersion "Windows Server 2022" `
    -S3BucketName "your-config-bucket" `
    -ValidateOnly
```

## Best Practices

### Configuration Management

1. **Version Control**: Store all configuration files in version control
2. **Environment Separation**: Use separate S3 prefixes or buckets for different environments
3. **Validation**: Always validate configurations before deployment
4. **Documentation**: Document any custom configurations or deviations

### Security

1. **Least Privilege**: Grant minimum required permissions
2. **Secret Rotation**: Regularly rotate domain service account passwords
3. **Encryption**: Ensure S3 bucket encryption is enabled
4. **Access Logging**: Enable S3 access logging for audit purposes

### Maintenance

1. **Regular Reviews**: Periodically review and update configurations
2. **Cleanup**: Remove obsolete configurations and applications
3. **Monitoring**: Monitor configuration usage and errors
4. **Backup**: Maintain backups of critical configurations

## Troubleshooting Common Issues

### Configuration Not Found

**Error**: "Failed to download base config"
**Solution**: 
- Verify S3 bucket name and permissions
- Check file exists at expected path
- Validate IAM role permissions

### Invalid Configuration Values

**Error**: "Invalid Asset Owner: XYZ"
**Solution**:
- Check base_config.json for valid values
- Ensure case sensitivity matches exactly
- Validate all required fields are present

### Domain Join Failures

**Error**: "Failed to retrieve domain credentials"
**Solution**:
- Verify Secrets Manager secret ARN
- Check secret contains required fields
- Validate IAM permissions for Secrets Manager

For more troubleshooting guidance, see [Troubleshooting-Guide.md](Troubleshooting-Guide.md).
