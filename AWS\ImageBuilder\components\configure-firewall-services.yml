# AWS Image Builder Component: Services Windows Firewall Configuration
# This component configures Windows Firewall rules for server role-specific services
# Groups: Active Directory, SQL Server, Web Server

name: configure-firewall-services
description: Configure Windows Firewall rules for Active Directory, SQL Server, and Web Server services
schemaVersion: 1.0
phases:
  - name: build
    steps:
      - name: ConfigureActiveDirectoryFirewallRules
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "=== CONFIGURING ACTIVE DIRECTORY FIREWALL RULES ==="
                
                # Create custom rule group for AD rules
                $adGroup = "Windows Server Active Directory"
                
                # LDAP (Port 389)
                Write-Host "Allowing LDAP (port 389)..."
                New-NetFirewallRule -DisplayName "LDAP" -Group $adGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 389 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow LDAP from internal networks"

                # LDAPS/Secure LDAP (Port 636)
                Write-Host "Allowing LDAPS (port 636)..."
                New-NetFirewallRule -DisplayName "LDAPS" -Group $adGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 636 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow secure LDAP from internal networks"

                # Global Catalog LDAP (Port 3268)
                Write-Host "Allowing Global Catalog LDAP (port 3268)..."
                New-NetFirewallRule -DisplayName "Global Catalog LDAP" -Group $adGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 3268 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow Global Catalog LDAP from internal networks"

                # Global Catalog LDAPS (Port 3269)
                Write-Host "Allowing Global Catalog LDAPS (port 3269)..."
                New-NetFirewallRule -DisplayName "Global Catalog LDAPS" -Group $adGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 3269 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow secure Global Catalog LDAP from internal networks"

                # Kerberos (Port 88)
                Write-Host "Allowing Kerberos (port 88)..."
                New-NetFirewallRule -DisplayName "Kerberos TCP" -Group $adGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 88 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow Kerberos TCP from internal networks"
                New-NetFirewallRule -DisplayName "Kerberos UDP" -Group $adGroup -Direction Inbound -Action Allow -Protocol UDP -LocalPort 88 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow Kerberos UDP from internal networks"

                # DNS (Port 53) - Required for AD domain services
                Write-Host "Allowing DNS (port 53)..."
                New-NetFirewallRule -DisplayName "DNS TCP" -Group $adGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 53 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow DNS TCP from internal networks"
                New-NetFirewallRule -DisplayName "DNS UDP" -Group $adGroup -Direction Inbound -Action Allow -Protocol UDP -LocalPort 53 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow DNS UDP from internal networks"

                # RPC Endpoint Mapper (Port 135)
                Write-Host "Allowing RPC Endpoint Mapper (port 135)..."
                New-NetFirewallRule -DisplayName "RPC Endpoint Mapper" -Group $adGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 135 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow RPC Endpoint Mapper from internal networks"

                # RPC Dynamic Ports (for AD replication and management)
                Write-Host "Allowing RPC Dynamic Ports for AD replication..."
                New-NetFirewallRule -DisplayName "RPC Dynamic Ports AD" -Group $adGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 1024-65535 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow RPC dynamic ports for AD replication from internal networks"

                # SMB/CIFS (Ports 445, 139)
                Write-Host "Allowing SMB/CIFS (ports 445, 139)..."
                New-NetFirewallRule -DisplayName "SMB" -Group $adGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 445 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow SMB from internal networks"
                New-NetFirewallRule -DisplayName "NetBIOS Session" -Group $adGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 139 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow NetBIOS Session from internal networks"

                # NetBIOS Name Service (Port 137 UDP)
                Write-Host "Allowing NetBIOS Name Service (port 137 UDP)..."
                New-NetFirewallRule -DisplayName "NetBIOS Name Service" -Group $adGroup -Direction Inbound -Action Allow -Protocol UDP -LocalPort 137 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow NetBIOS Name Service from internal networks"

                # NetBIOS Datagram Service (Port 138 UDP)
                Write-Host "Allowing NetBIOS Datagram Service (port 138 UDP)..."
                New-NetFirewallRule -DisplayName "NetBIOS Datagram Service" -Group $adGroup -Direction Inbound -Action Allow -Protocol UDP -LocalPort 138 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow NetBIOS Datagram Service from internal networks"

                # NTP (Port 123) - For time synchronization
                Write-Host "Allowing NTP (port 123)..."
                New-NetFirewallRule -DisplayName "NTP" -Group $adGroup -Direction Inbound -Action Allow -Protocol UDP -LocalPort 123 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow NTP from internal networks"

                # Enable built-in Active Directory firewall rules
                Write-Host "Enabling built-in Active Directory firewall rules..."
                $adRuleGroups = @(
                    "Active Directory Domain Services",
                    "Active Directory Domain Services (NP-In)",
                    "Active Directory Web Services (ADWS)",
                    "DFS Management",
                    "DFS Replication",
                    "DNS Service",
                    "Kerberos Key Distribution Center"
                )

                foreach ($ruleGroup in $adRuleGroups) {
                    try {
                        Enable-NetFirewallRule -DisplayGroup $ruleGroup -ErrorAction SilentlyContinue
                        Write-Host "Enabled firewall rule group: $ruleGroup"
                    }
                    catch {
                        Write-Host "Could not enable firewall rule group $ruleGroup`: $($_.Exception.Message)" -ForegroundColor Yellow
                    }
                }

                Write-Host "Active Directory firewall rules configured successfully"

      - name: ConfigureSQLServerFirewallRules
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "=== CONFIGURING SQL SERVER FIREWALL RULES ==="
                
                # Create custom rule group for SQL Server rules
                $sqlGroup = "Windows Server SQL Server"
                
                # SQL Server default instance (Port 1433)
                Write-Host "Allowing SQL Server default instance (port 1433)..."
                New-NetFirewallRule -DisplayName "SQL Server Default Instance" -Group $sqlGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 1433 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow SQL Server default instance from internal networks"

                # SQL Server Browser Service (Port 1434 UDP)
                Write-Host "Allowing SQL Server Browser Service (port 1434 UDP)..."
                New-NetFirewallRule -DisplayName "SQL Server Browser Service" -Group $sqlGroup -Direction Inbound -Action Allow -Protocol UDP -LocalPort 1434 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow SQL Server Browser Service from internal networks"

                # SQL Server named instances (Dynamic ports range)
                Write-Host "Allowing SQL Server named instances (dynamic ports)..."
                New-NetFirewallRule -DisplayName "SQL Server Named Instances" -Group $sqlGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 1024-65535 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow SQL Server named instances from internal networks"

                # SQL Server Analysis Services (Port 2383)
                Write-Host "Allowing SQL Server Analysis Services (port 2383)..."
                New-NetFirewallRule -DisplayName "SQL Server Analysis Services" -Group $sqlGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 2383 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow SQL Server Analysis Services from internal networks"

                # SQL Server Reporting Services (Port 80/443)
                Write-Host "Allowing SQL Server Reporting Services (ports 80, 443)..."
                New-NetFirewallRule -DisplayName "SQL Server Reporting Services HTTP" -Group $sqlGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 80 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow SQL Server Reporting Services HTTP from internal networks"
                New-NetFirewallRule -DisplayName "SQL Server Reporting Services HTTPS" -Group $sqlGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 443 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow SQL Server Reporting Services HTTPS from internal networks"

                # SQL Server Integration Services (Port 135 for RPC)
                Write-Host "Allowing SQL Server Integration Services RPC (port 135)..."
                New-NetFirewallRule -DisplayName "SQL Server Integration Services RPC" -Group $sqlGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 135 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow SQL Server Integration Services RPC from internal networks"

                # SQL Server Service Broker (Port 4022)
                Write-Host "Allowing SQL Server Service Broker (port 4022)..."
                New-NetFirewallRule -DisplayName "SQL Server Service Broker" -Group $sqlGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 4022 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow SQL Server Service Broker from internal networks"

                # SQL Server Database Mirroring (Port 5022)
                Write-Host "Allowing SQL Server Database Mirroring (port 5022)..."
                New-NetFirewallRule -DisplayName "SQL Server Database Mirroring" -Group $sqlGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 5022 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow SQL Server Database Mirroring from internal networks"

                # SQL Server Always On Availability Groups (Port 5022)
                Write-Host "Allowing SQL Server Always On Availability Groups (port 5022)..."
                New-NetFirewallRule -DisplayName "SQL Server Always On AG" -Group $sqlGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 5022 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow SQL Server Always On Availability Groups from internal networks"

                # SQL Server Backup and Restore operations
                Write-Host "Allowing SQL Server backup and restore services..."
                New-NetFirewallRule -DisplayName "SQL Server Backup Services" -Group $sqlGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 3050,5000 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow SQL Server backup services from internal networks"

                # Enable built-in SQL Server firewall rules if they exist
                Write-Host "Enabling built-in SQL Server firewall rules..."
                $sqlRuleGroups = @(
                    "SQL Server",
                    "SQL Server Analysis Services",
                    "SQL Server Browser",
                    "SQL Server Integration Services",
                    "SQL Server Reporting Services"
                )

                foreach ($ruleGroup in $sqlRuleGroups) {
                    try {
                        Enable-NetFirewallRule -DisplayGroup $ruleGroup -ErrorAction SilentlyContinue
                        Write-Host "Enabled firewall rule group: $ruleGroup"
                    }
                    catch {
                        Write-Host "Could not enable firewall rule group $ruleGroup`: $($_.Exception.Message)" -ForegroundColor Yellow
                    }
                }

                Write-Host "SQL Server firewall rules configured successfully"
