# AWS Image Builder Component: Configure Basic Windows Firewall Setup
# This component configures basic Windows Firewall settings and management rules

name: configure-firewall-basic
description: Configure basic Windows Firewall settings, logging, and management rules
schemaVersion: 1.0
phases:
- name: build
  steps:
  - name: EnableWindowsFirewall
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Configuring Windows Firewall..."

        # Enable Windows Firewall for all profiles
        Write-Host "Enabling Windows Firewall for all profiles..."
        Set-NetFirewallProfile -All -Enabled True

        # Set default actions
        Write-Host "Setting default firewall actions..."
        Set-NetFirewallProfile -All -DefaultInboundAction Block -DefaultOutboundAction Allow

        # Configure logging
        Write-Host "Configuring firewall logging..."
        Set-NetFirewallProfile -All -LogFileName "%systemroot%\system32\LogFiles\Firewall\pfirewall.log"
        Set-NetFirewallProfile -All -LogMaxSizeKilobytes 4096
        Set-NetFirewallProfile -All -LogBlocked True
        Set-NetFirewallProfile -All -LogAllowed False

  - name: ConfigureBypassRules
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Configuring bypass firewall rules..."

        # Allow Any Traffic - Any-Any Bypass Rule for Role Based Traffic Rules.
        Write-Host "Allowing All Traffic..."
        New-NetFirewallRule -DisplayName "Allow Any-Any" -Direction Inbound -Action Allow -Protocol Any -Profile Any
        New-NetFirewallRule -DisplayName "Allow Any-Any" -Direction Outbound -Action Allow -Protocol Any -Profile Any

  - name: ConfigureBasicRules
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Configuring basic firewall rules..."

        # Allow RDP (Remote Desktop) - Restricted to internal networks only
        Write-Host "Allowing RDP from internal networks only..."
        New-NetFirewallRule -DisplayName "Allow RDP Inbound" -Direction Inbound -Action Allow -Protocol TCP -LocalPort 3389 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow RDP from internal networks only"

        # Allow WinRM HTTP - Domain profile only for PowerShell remoting
        Write-Host "Allowing WinRM HTTP for PowerShell remoting..."
        New-NetFirewallRule -DisplayName "Allow WinRM HTTP" -Direction Inbound -Action Allow -Protocol TCP -LocalPort 5985 -Profile Domain -RemoteAddress "10.0.0.0/8" -Description "Allow WinRM for PowerShell remoting"

        # Allow WinRM HTTPS - Domain profile only for secure PowerShell remoting
        Write-Host "Allowing WinRM HTTPS for secure PowerShell remoting..."
        New-NetFirewallRule -DisplayName "Allow WinRM HTTPS" -Direction Inbound -Action Allow -Protocol TCP -LocalPort 5986 -Profile Domain -RemoteAddress "10.0.0.0/8" -Description "Allow secure WinRM for PowerShell remoting"

        # Additional WinRM HTTPS rule for ImageBuilder compatibility
        Write-Host "Adding WinRM HTTPS rule for ImageBuilder..."
        New-NetFirewallRule -DisplayName "WinRM-HTTPS" -Direction Inbound -Action Allow -Protocol TCP -LocalPort 5986 -Profile Any -Description "WinRM HTTPS for ImageBuilder and remote management"

        # Allow ICMP (Ping) - For network diagnostics on domain and private networks
        Write-Host "Allowing ICMP Ping for network diagnostics..."
        New-NetFirewallRule -DisplayName "Allow ICMP Ping" -Direction Inbound -Action Allow -Protocol ICMPv4 -IcmpType 8 -Profile Domain,Private -Description "Allow ping for network diagnostics"

        # Enable built-in Windows management firewall rules
        Write-Host "Enabling Windows management firewall rules..."
        $managementRules = @(
            "File and Printer Sharing (Echo Request - ICMPv4-In)",
            "Remote Service Management (RPC-EPMAP)",
            "Remote Service Management (RPC)",
            "Remote Service Management (NP-In)",
            "Windows Management Instrumentation (DCOM-In)",
            "Windows Management Instrumentation (WMI-In)"
        )

        foreach ($ruleName in $managementRules) {
            try {
                Enable-NetFirewallRule -DisplayName $ruleName -ErrorAction SilentlyContinue
                Write-Host "Enabled firewall rule: $ruleName"
            }
            catch {
                Write-Host "Could not enable firewall rule $ruleName`: $($_.Exception.Message)" -ForegroundColor Yellow
            }
        }

        # Enable Remote Desktop firewall rule group
        Write-Host "Enabling Remote Desktop firewall rules..."
        try {
            Enable-NetFirewallRule -DisplayGroup "Remote Desktop" -ErrorAction SilentlyContinue
            Write-Host "Enabled Remote Desktop firewall rule group"
        }
        catch {
            Write-Host "Could not enable Remote Desktop firewall rules: $($_.Exception.Message)" -ForegroundColor Yellow
        }

- name: validate
  steps:
  - name: ValidateBasicFirewallConfiguration
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Validating basic Windows Firewall configuration..."

        # Check if firewall is enabled
        $firewallProfiles = Get-NetFirewallProfile
        $disabledProfiles = $firewallProfiles | Where-Object { $_.Enabled -eq $false }
        if ($disabledProfiles.Count -gt 0) {
            Write-Error "VALIDATION FAILED: Windows Firewall is not enabled on profiles: $($disabledProfiles.Name -join ', ')"
            exit 1
        }

        # Check if key rules exist
        $existingRules = Get-NetFirewallRule | Where-Object { $_.Enabled -eq "True" }

        $requiredRules = @(
            "Allow RDP Inbound",
            "Allow WinRM HTTP",
            "Allow WinRM HTTPS",
            "WinRM-HTTPS",
            "Allow ICMP Ping",
            "Allow Any-Any"
        )

        $missingRules = @()
        foreach ($rule in $requiredRules) {
            $foundRule = $existingRules | Where-Object { $_.DisplayName -like "*$rule*" }
            if (-not $foundRule) {
                $missingRules += $rule
            }
        }

        if ($missingRules.Count -eq 0) {
            Write-Host "VALIDATION SUCCESS: Basic firewall rules are configured"
            Write-Host "- Windows Firewall: Enabled on all profiles"
            Write-Host "- Default Policy: Block inbound, Allow outbound"
            Write-Host "- Logging: Enabled for dropped connections"
            Write-Host "- Management: RDP, WinRM, ICMP, WMI configured"
            exit 0
        } else {
            Write-Error "VALIDATION FAILED: Missing basic firewall rules: $($missingRules -join ', ')"
            exit 1
        }
