<#
    .NOTES
    File Name: New-ADComputerObject.ps1
    Author: <PERSON><PERSON>
    Version: 1.0.0 - 03/09/2025
    Description: Creates AD computer objects for EC2 instances using SecretsManager credentials
    Note: This script is designed to be called from SSM Automation runbooks

    .NOTES
    Version 1.0.0     - Base Script
#>

param(
    [Parameter(Mandatory=$true)]
    [string]$ComputerName,
    
    [Parameter(Mandatory=$true)]
    [string]$TargetOU,
    
    [Parameter(Mandatory=$true)]
    [string]$Domain,
    
    [Parameter(Mandatory=$true)]
    [string]$SecretsManagerSecretArn,
    
    [Parameter(Mandatory=$false)]
    [string]$Description = "",
    
    [Parameter(Mandatory=$false)]
    [string]$Region = "af-south-1",
    
    [Parameter(Mandatory=$false)]
    [switch]$Force = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$OutputJson = $false
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Function to write structured log messages
function Write-LogMessage {
    param(
        [string]$Message,
        [ValidateSet("INFO", "WARN", "ERROR", "SUCCESS")]
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    switch ($Level) {
        "INFO" { Write-Host $logMessage -ForegroundColor Cyan }
        "WARN" { Write-Host $logMessage -ForegroundColor Yellow }
        "ERROR" { Write-Host $logMessage -ForegroundColor Red }
        "SUCCESS" { Write-Host $logMessage -ForegroundColor Green }
    }
}

# Function to get domain credentials from Secrets Manager
function Get-DomainCredentials {
    param(
        [string]$SecretArn,
        [string]$Region
    )
    
    try {
        Write-LogMessage "Retrieving domain credentials from Secrets Manager" "INFO"
        
        # Get secret value using AWS PowerShell module
        $secretValue = Get-SECSecretValue -SecretId $SecretArn -Region $Region
        $secretJson = $secretValue.SecretString | ConvertFrom-Json
        
        # Validate required fields
        if (-not $secretJson.domainJoinUserName) {
            throw "domainJoinUserName not found in secret"
        }
        
        if (-not $secretJson.domainJoinPassword) {
            throw "domainJoinPassword not found in secret"
        }
        
        # Create credential object
        $securePassword = $secretJson.domainJoinPassword | ConvertTo-SecureString -AsPlainText -Force
        $credential = New-Object System.Management.Automation.PSCredential($secretJson.domainJoinUserName, $securePassword)
        
        Write-LogMessage "Successfully retrieved domain credentials" "SUCCESS"
        return @{
            Credential = $credential
            Username = $secretJson.domainJoinUserName
            DomainName = $secretJson.domainName
        }
        
    } catch {
        Write-LogMessage "Failed to retrieve domain credentials: $($_.Exception.Message)" "ERROR"
        throw
    }
}

# Function to install RSAT AD Tools if needed
function Install-RSATADTools {
    try {
        Write-LogMessage "Checking for RSAT AD Tools" "INFO"
        
        $rsatFeature = Get-WindowsFeature -Name 'RSAT-AD-Tools' -ErrorAction SilentlyContinue
        
        if (-not $rsatFeature -or -not $rsatFeature.Installed) {
            Write-LogMessage "Installing RSAT AD Tools" "INFO"
            $installResult = Add-WindowsFeature -Name 'RSAT-AD-Tools' -ErrorAction Stop
            
            if ($installResult.Success) {
                Write-LogMessage "RSAT AD Tools installed successfully" "SUCCESS"
            } else {
                throw "Failed to install RSAT AD Tools"
            }
        } else {
            Write-LogMessage "RSAT AD Tools already installed" "SUCCESS"
        }
        
        return $true
        
    } catch {
        Write-LogMessage "Failed to install RSAT AD Tools: $($_.Exception.Message)" "ERROR"
        throw
    }
}

# Function to test AD connectivity
function Test-ADConnectivity {
    param(
        [string]$DomainName,
        [System.Management.Automation.PSCredential]$Credential
    )
    
    try {
        Write-LogMessage "Testing AD connectivity to domain: $DomainName" "INFO"
        
        # Try to get domain information
        $domainInfo = Get-ADDomain -Server $DomainName -Credential $Credential -ErrorAction Stop
        
        Write-LogMessage "Successfully connected to AD domain: $($domainInfo.DNSRoot)" "SUCCESS"
        return $true
        
    } catch {
        Write-LogMessage "Failed to connect to AD domain: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# Function to create or manage AD computer object
function New-ADComputerObjectSafe {
    param(
        [string]$Name,
        [string]$Path,
        [string]$DomainName,
        [System.Management.Automation.PSCredential]$Credential,
        [string]$Description,
        [bool]$ForceMove
    )
    
    try {
        Write-LogMessage "Creating/managing AD computer object: $Name" "INFO"
        
        # Check if computer object already exists
        $existingComputer = $null
        try {
            $existingComputer = Get-ADComputer -Identity $Name -Server $DomainName -Credential $Credential -ErrorAction SilentlyContinue
        } catch {
            # Computer doesn't exist, which is fine
        }
        
        if ($existingComputer) {
            Write-LogMessage "Computer object '$Name' already exists at: $($existingComputer.DistinguishedName)" "WARN"
            
            # Check if it's in the correct OU
            if ($existingComputer.DistinguishedName -like "*$Path") {
                Write-LogMessage "Computer object is already in the correct OU" "SUCCESS"
                return @{
                    Action = "NO_CHANGE"
                    ComputerName = $Name
                    DistinguishedName = $existingComputer.DistinguishedName
                    Message = "Computer object already exists in correct OU"
                }
            } else {
                if ($ForceMove) {
                    Write-LogMessage "Moving computer object to target OU: $Path" "INFO"
                    Move-ADObject -Identity $existingComputer.DistinguishedName -TargetPath $Path -Server $DomainName -Credential $Credential -ErrorAction Stop
                    
                    # Get updated object
                    $updatedComputer = Get-ADComputer -Identity $Name -Server $DomainName -Credential $Credential
                    
                    Write-LogMessage "Successfully moved computer object to target OU" "SUCCESS"
                    return @{
                        Action = "MOVED"
                        ComputerName = $Name
                        DistinguishedName = $updatedComputer.DistinguishedName
                        PreviousLocation = $existingComputer.DistinguishedName
                        Message = "Computer object moved to correct OU"
                    }
                } else {
                    Write-LogMessage "Computer object exists in different OU. Use -Force to move it." "WARN"
                    return @{
                        Action = "EXISTS_DIFFERENT_OU"
                        ComputerName = $Name
                        DistinguishedName = $existingComputer.DistinguishedName
                        TargetOU = $Path
                        Message = "Computer object exists in different OU"
                    }
                }
            }
        } else {
            # Create new computer object
            Write-LogMessage "Creating new computer object '$Name' in OU: $Path" "INFO"
            
            $computerDescription = if ($Description) { $Description } else { "Created by AWS SSM Automation on $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" }
            
            New-ADComputer -Name $Name -Path $Path -Server $DomainName -Credential $Credential -Description $computerDescription -ErrorAction Stop
            
            # Verify creation
            $newComputer = Get-ADComputer -Identity $Name -Server $DomainName -Credential $Credential
            
            Write-LogMessage "Successfully created computer object '$Name'" "SUCCESS"
            return @{
                Action = "CREATED"
                ComputerName = $Name
                DistinguishedName = $newComputer.DistinguishedName
                Message = "Computer object created successfully"
            }
        }
        
    } catch {
        Write-LogMessage "Failed to create/manage AD computer object: $($_.Exception.Message)" "ERROR"
        throw
    }
}

# Main execution logic
try {
    Write-LogMessage "Starting AD Computer Object Creation" "INFO"
    Write-LogMessage "Computer: $ComputerName, OU: $TargetOU, Domain: $Domain" "INFO"

    # Step 1: Install RSAT AD Tools if needed
    Install-RSATADTools

    # Step 2: Get domain credentials from Secrets Manager
    $domainCreds = Get-DomainCredentials -SecretArn $SecretsManagerSecretArn -Region $Region

    # Use domain from secret if not provided or if different
    $effectiveDomain = if ($domainCreds.DomainName) { $domainCreds.DomainName } else { $Domain }

    # Step 3: Test AD connectivity
    if (-not (Test-ADConnectivity -DomainName $effectiveDomain -Credential $domainCreds.Credential)) {
        throw "Cannot connect to Active Directory domain: $effectiveDomain"
    }

    # Step 4: Create or manage AD computer object
    $result = New-ADComputerObjectSafe -Name $ComputerName -Path $TargetOU -DomainName $effectiveDomain -Credential $domainCreds.Credential -Description $Description -ForceMove $Force

    # Step 5: Build final result object
    $finalResult = @{
        Success = $true
        ComputerName = $ComputerName
        TargetOU = $TargetOU
        Domain = $effectiveDomain
        Action = $result.Action
        DistinguishedName = $result.DistinguishedName
        Message = $result.Message
        Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss UTC"

        # Additional details
        SecretArn = $SecretsManagerSecretArn
        DomainUser = $domainCreds.Username
    }

    # Add previous location if moved
    if ($result.PreviousLocation) {
        $finalResult.PreviousLocation = $result.PreviousLocation
    }

    # Output results
    if ($OutputJson) {
        Write-Output ($finalResult | ConvertTo-Json -Depth 5)
    } else {
        Write-LogMessage "=== AD COMPUTER OBJECT OPERATION COMPLETED ===" "SUCCESS"
        Write-LogMessage "Action: $($result.Action)" "INFO"
        Write-LogMessage "Computer Name: $ComputerName" "INFO"
        Write-LogMessage "Distinguished Name: $($result.DistinguishedName)" "INFO"
        Write-LogMessage "Domain: $effectiveDomain" "INFO"
        Write-LogMessage "Message: $($result.Message)" "INFO"
    }

    # Return result for use in runbook
    return $finalResult

} catch {
    Write-LogMessage "AD Computer Object operation failed: $($_.Exception.Message)" "ERROR"

    $errorResult = @{
        Success = $false
        Error = $true
        Message = $_.Exception.Message
        ComputerName = $ComputerName
        TargetOU = $TargetOU
        Domain = $Domain
        ScriptName = $MyInvocation.MyCommand.Name
        LineNumber = $_.InvocationInfo.ScriptLineNumber
        Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss UTC"
    }

    if ($OutputJson) {
        Write-Output ($errorResult | ConvertTo-Json -Depth 5)
    }

    exit 1
}
