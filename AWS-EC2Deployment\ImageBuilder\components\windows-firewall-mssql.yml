# AWS Image Builder Component: Microsoft SQL Server Windows Firewall Configuration
# This component configures Windows Firewall rules specifically for Microsoft SQL Server services

name: windows-firewall-mssql
description: Configure Windows Firewall rules for Microsoft SQL Server services
schemaVersion: 1.0
phases:
  - name: build
    steps:
      - name: ConfigureSQLServerFirewallRules
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "=== CONFIGURING SQL SERVER FIREWALL RULES ==="

                # Create custom rule group for SQL Server rules
                $sqlGroup = "Windows Server SQL Server"

                # SQL Server default instance (Port 1433)
                Write-Host "Allowing SQL Server default instance (port 1433)..."
                New-NetFirewallRule -DisplayName "SQL Server Default Instance" -Group $sqlGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 1433 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow SQL Server default instance from internal networks"

                # SQL Server Browser Service (Port 1434 UDP)
                Write-Host "Allowing SQL Server Browser Service (port 1434 UDP)..."
                New-NetFirewallRule -DisplayName "SQL Server Browser Service" -Group $sqlGroup -Direction Inbound -Action Allow -Protocol UDP -LocalPort 1434 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow SQL Server Browser Service from internal networks"

                # SQL Server named instances (Dynamic ports range)
                Write-Host "Allowing SQL Server named instances (dynamic ports)..."
                New-NetFirewallRule -DisplayName "SQL Server Named Instances" -Group $sqlGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 1024-65535 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow SQL Server named instances from internal networks"

                # SQL Server Analysis Services (Port 2383)
                Write-Host "Allowing SQL Server Analysis Services (port 2383)..."
                New-NetFirewallRule -DisplayName "SQL Server Analysis Services" -Group $sqlGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 2383 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow SQL Server Analysis Services from internal networks"

                # SQL Server Reporting Services (Port 80/443)
                Write-Host "Allowing SQL Server Reporting Services (ports 80, 443)..."
                New-NetFirewallRule -DisplayName "SQL Server Reporting Services HTTP" -Group $sqlGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 80 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow SQL Server Reporting Services HTTP from internal networks"
                New-NetFirewallRule -DisplayName "SQL Server Reporting Services HTTPS" -Group $sqlGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 443 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow SQL Server Reporting Services HTTPS from internal networks"

                # SQL Server Integration Services (Port 135 for RPC)
                Write-Host "Allowing SQL Server Integration Services RPC (port 135)..."
                New-NetFirewallRule -DisplayName "SQL Server Integration Services RPC" -Group $sqlGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 135 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow SQL Server Integration Services RPC from internal networks"

                # SQL Server Service Broker (Port 4022)
                Write-Host "Allowing SQL Server Service Broker (port 4022)..."
                New-NetFirewallRule -DisplayName "SQL Server Service Broker" -Group $sqlGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 4022 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow SQL Server Service Broker from internal networks"

                # SQL Server Database Mirroring (Port 5022)
                Write-Host "Allowing SQL Server Database Mirroring (port 5022)..."
                New-NetFirewallRule -DisplayName "SQL Server Database Mirroring" -Group $sqlGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 5022 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow SQL Server Database Mirroring from internal networks"

                # SQL Server Always On Availability Groups (Port 5022)
                Write-Host "Allowing SQL Server Always On Availability Groups (port 5022)..."
                New-NetFirewallRule -DisplayName "SQL Server Always On AG" -Group $sqlGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 5022 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow SQL Server Always On Availability Groups from internal networks"

                # SQL Server Backup and Restore operations
                Write-Host "Allowing SQL Server backup and restore services..."
                New-NetFirewallRule -DisplayName "SQL Server Backup Services" -Group $sqlGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 3050,5000 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow SQL Server backup services from internal networks"

                # Enable built-in SQL Server firewall rules if they exist
                Write-Host "Enabling built-in SQL Server firewall rules..."
                $sqlRuleGroups = @(
                    "SQL Server",
                    "SQL Server Analysis Services",
                    "SQL Server Browser",
                    "SQL Server Integration Services",
                    "SQL Server Reporting Services"
                )

                foreach ($ruleGroup in $sqlRuleGroups) {
                    try {
                        Enable-NetFirewallRule -DisplayGroup $ruleGroup -ErrorAction SilentlyContinue
                        Write-Host "Enabled firewall rule group: $ruleGroup"
                    }
                    catch {
                        Write-Host "Could not enable firewall rule group $ruleGroup`: $($_.Exception.Message)" -ForegroundColor Yellow
                    }
                }

                Write-Host "SQL Server firewall rules configured successfully"

  - name: validate
    steps:
      - name: ValidateSQLServerFirewallConfiguration
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "=== VALIDATING SQL SERVER FIREWALL CONFIGURATION ==="

                $validationErrors = @()

                # Check if SQL Server custom rule group exists
                $existingRules = Get-NetFirewallRule | Where-Object { $_.Enabled -eq "True" }

                $sqlGroup = "Windows Server SQL Server"
                $sqlGroupRules = $existingRules | Where-Object { $_.Group -eq $sqlGroup }

                if ($sqlGroupRules.Count -eq 0) {
                    $validationErrors += "No enabled rules found for SQL Server group: $sqlGroup"
                } else {
                    Write-Host "Found $($sqlGroupRules.Count) enabled rules in SQL Server group"
                }

                # Check key SQL Server service rules
                $requiredSQLRules = @("SQL Server Default Instance", "SQL Server Browser Service", "SQL Server Analysis Services", "SQL Server Service Broker")
                foreach ($rule in $requiredSQLRules) {
                    $foundRule = $existingRules | Where-Object { $_.DisplayName -eq $rule }
                    if (-not $foundRule) {
                        $validationErrors += "Missing SQL Server service rule: $rule"
                    }
                }

                # Validate specific SQL Server port configurations
                try {
                    $sqlServerRule = Get-NetFirewallRule -DisplayName "SQL Server Default Instance" -ErrorAction SilentlyContinue
                    if ($sqlServerRule) {
                        $sqlPortFilter = Get-NetFirewallPortFilter -AssociatedNetFirewallRule $sqlServerRule
                        if ($sqlPortFilter.LocalPort -ne "1433") {
                            $validationErrors += "SQL Server Default Instance rule should be on port 1433, found: $($sqlPortFilter.LocalPort)"
                        }
                    }

                    $sqlBrowserRule = Get-NetFirewallRule -DisplayName "SQL Server Browser Service" -ErrorAction SilentlyContinue
                    if ($sqlBrowserRule) {
                        $sqlBrowserPortFilter = Get-NetFirewallPortFilter -AssociatedNetFirewallRule $sqlBrowserRule
                        if ($sqlBrowserPortFilter.LocalPort -ne "1434") {
                            $validationErrors += "SQL Server Browser Service rule should be on port 1434, found: $($sqlBrowserPortFilter.LocalPort)"
                        }
                    }

                    $ssasRule = Get-NetFirewallRule -DisplayName "SQL Server Analysis Services" -ErrorAction SilentlyContinue
                    if ($ssasRule) {
                        $ssasPortFilter = Get-NetFirewallPortFilter -AssociatedNetFirewallRule $ssasRule
                        if ($ssasPortFilter.LocalPort -ne "2383") {
                            $validationErrors += "SQL Server Analysis Services rule should be on port 2383, found: $($ssasPortFilter.LocalPort)"
                        }
                    }

                    $serviceBrokerRule = Get-NetFirewallRule -DisplayName "SQL Server Service Broker" -ErrorAction SilentlyContinue
                    if ($serviceBrokerRule) {
                        $serviceBrokerPortFilter = Get-NetFirewallPortFilter -AssociatedNetFirewallRule $serviceBrokerRule
                        if ($serviceBrokerPortFilter.LocalPort -ne "4022") {
                            $validationErrors += "SQL Server Service Broker rule should be on port 4022, found: $($serviceBrokerPortFilter.LocalPort)"
                        }
                    }
                }
                catch {
                    $validationErrors += "Failed to validate SQL Server port configurations: $($_.Exception.Message)"
                }

                # Report validation results
                if ($validationErrors.Count -eq 0) {
                    Write-Host "VALIDATION SUCCESS: SQL Server firewall configuration completed successfully"
                    Write-Host "=== SQL SERVER SERVICES CONFIGURED ==="
                    Write-Host "Database Engine: Ports 1433 (default), 1434 (browser), 1024-65535 (named instances)"
                    Write-Host "Analysis Services: Port 2383 (internal networks only)"
                    Write-Host "Reporting Services: Ports 80 (HTTP), 443 (HTTPS) (internal networks only)"
                    Write-Host "Integration Services: Port 135 (RPC) (internal networks only)"
                    Write-Host "Service Broker: Port 4022 (internal networks only)"
                    Write-Host "Database Mirroring/Always On: Port 5022 (internal networks only)"
                    Write-Host "Backup Services: Ports 3050, 5000 (internal networks only)"
                    Write-Host "Built-in SQL Server rule groups enabled"
                    Write-Host "Total SQL Server rules: $($sqlGroupRules.Count)"
                    exit 0
                } else {
                    Write-Error "VALIDATION FAILED: SQL Server firewall configuration errors found:"
                    foreach ($error in $validationErrors) {
                        Write-Error "  - $error"
                    }
                    exit 1
                }
