# AWS Image Builder Component: Configure Internet Explorer Security Registry Settings
# This component configures Internet Explorer security-related registry settings

name: configure-registry-iesecurity
description: Configure Internet Explorer security registry settings and cryptography hardening
schemaVersion: 1.0
phases:
  - name: build
    steps:
      - name: ConfigureInternetExplorerSecurity
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "Configuring Internet Explorer security settings..."

                # Internet Explorer security settings for print info disclosure fix
                Write-Host "Applying IE print info disclosure fix..."
                $iePath1 = "HKLM:\SOFTWARE\Microsoft\Internet Explorer\Main\FeatureControl\FEATURE_ENABLE_PRINT_INFO_DISCLOSURE_FIX"
                $iePath2 = "HKLM:\SOFTWARE\WOW6432Node\Microsoft\Internet Explorer\Main\FeatureControl\FEATURE_ENABLE_PRINT_INFO_DISCLOSURE_FIX"

                if (-not (Test-Path $iePath1)) {
                    New-Item -Path $iePath1 -Force | Out-Null
                    Write-Host "Created registry path: $iePath1"
                }
                Set-ItemProperty -Path $iePath1 -Name "iexplore.exe" -Value 1 -Type DWord

                if (-not (Test-Path $iePath2)) {
                    New-Item -Path $iePath2 -Force | Out-Null
                    Write-Host "Created registry path: $iePath2"
                }
                Set-ItemProperty -Path $iePath2 -Name "iexplore.exe" -Value 1 -Type DWord

                # Internet Explorer exception handler hardening
                Write-Host "Applying IE exception handler hardening..."
                $iePath3 = "HKLM:\SOFTWARE\Microsoft\Internet Explorer\MAIN\FeatureControl\FEATURE_ALLOW_USER32_EXCEPTION_HANDLER_HARDENING"
                $iePath4 = "HKLM:\SOFTWARE\Wow6432Node\Microsoft\Internet Explorer\MAIN\FeatureControl\FEATURE_ALLOW_USER32_EXCEPTION_HANDLER_HARDENING"

                if (-not (Test-Path $iePath3)) {
                    New-Item -Path $iePath3 -Force | Out-Null
                    Write-Host "Created registry path: $iePath3"
                }
                Set-ItemProperty -Path $iePath3 -Name "iexplore.exe" -Value 1 -Type DWord

                if (-not (Test-Path $iePath4)) {
                    New-Item -Path $iePath4 -Force | Out-Null
                    Write-Host "Created registry path: $iePath4"
                }
                Set-ItemProperty -Path $iePath4 -Name "iexplore.exe" -Value 1 -Type DWord

                # Additional IE security features
                Write-Host "Configuring additional IE security features..."
                
                # Enable DEP for Internet Explorer
                $depPath1 = "HKLM:\SOFTWARE\Microsoft\Internet Explorer\Main\FeatureControl\FEATURE_ENABLE_DEP_BY_DEFAULT"
                $depPath2 = "HKLM:\SOFTWARE\WOW6432Node\Microsoft\Internet Explorer\Main\FeatureControl\FEATURE_ENABLE_DEP_BY_DEFAULT"

                if (-not (Test-Path $depPath1)) {
                    New-Item -Path $depPath1 -Force | Out-Null
                }
                Set-ItemProperty -Path $depPath1 -Name "iexplore.exe" -Value 1 -Type DWord

                if (-not (Test-Path $depPath2)) {
                    New-Item -Path $depPath2 -Force | Out-Null
                }
                Set-ItemProperty -Path $depPath2 -Name "iexplore.exe" -Value 1 -Type DWord

                # Enable ASLR for Internet Explorer
                $aslrPath1 = "HKLM:\SOFTWARE\Microsoft\Internet Explorer\Main\FeatureControl\FEATURE_ENABLE_ASLR"
                $aslrPath2 = "HKLM:\SOFTWARE\WOW6432Node\Microsoft\Internet Explorer\Main\FeatureControl\FEATURE_ENABLE_ASLR"

                if (-not (Test-Path $aslrPath1)) {
                    New-Item -Path $aslrPath1 -Force | Out-Null
                }
                Set-ItemProperty -Path $aslrPath1 -Name "iexplore.exe" -Value 1 -Type DWord

                if (-not (Test-Path $aslrPath2)) {
                    New-Item -Path $aslrPath2 -Force | Out-Null
                }
                Set-ItemProperty -Path $aslrPath2 -Name "iexplore.exe" -Value 1 -Type DWord

      - name: ConfigureCryptographySettings
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "Configuring cryptography settings..."

                # Cryptography settings for certificate padding check
                Write-Host "Configuring certificate padding check..."
                $cryptoPath1 = "HKLM:\Software\Microsoft\Cryptography\Wintrust\Config"
                $cryptoPath2 = "HKLM:\Software\Wow6432Node\Microsoft\Cryptography\Wintrust\Config"

                if (-not (Test-Path $cryptoPath1)) {
                    New-Item -Path $cryptoPath1 -Force | Out-Null
                    Write-Host "Created registry path: $cryptoPath1"
                }
                Set-ItemProperty -Path $cryptoPath1 -Name "EnableCertPaddingCheck" -Value "1" -Type String

                if (-not (Test-Path $cryptoPath2)) {
                    New-Item -Path $cryptoPath2 -Force | Out-Null
                    Write-Host "Created registry path: $cryptoPath2"
                }
                Set-ItemProperty -Path $cryptoPath2 -Name "EnableCertPaddingCheck" -Value "1" -Type String

                # Configure additional cryptography settings
                Write-Host "Configuring additional cryptography settings..."

                # Enable strong cryptography for .NET Framework
                $netPath1 = "HKLM:\SOFTWARE\Microsoft\.NETFramework\v4.0.30319"
                $netPath2 = "HKLM:\SOFTWARE\Wow6432Node\Microsoft\.NETFramework\v4.0.30319"

                if (Test-Path $netPath1) {
                    Set-ItemProperty -Path $netPath1 -Name "SchUseStrongCrypto" -Value 1 -Type DWord
                }

                if (Test-Path $netPath2) {
                    Set-ItemProperty -Path $netPath2 -Name "SchUseStrongCrypto" -Value 1 -Type DWord
                }

                # Configure TLS settings
                Write-Host "Configuring TLS settings..."
                $schannel = "HKLM:\SYSTEM\CurrentControlSet\Control\SecurityProviders\SCHANNEL\Protocols"

                # Disable SSL 2.0
                $ssl20ServerPath = "$schannel\SSL 2.0\Server"
                $ssl20ClientPath = "$schannel\SSL 2.0\Client"

                if (-not (Test-Path $ssl20ServerPath)) {
                    New-Item -Path $ssl20ServerPath -Force | Out-Null
                }
                Set-ItemProperty -Path $ssl20ServerPath -Name "Enabled" -Value 0 -Type DWord
                Set-ItemProperty -Path $ssl20ServerPath -Name "DisabledByDefault" -Value 1 -Type DWord

                if (-not (Test-Path $ssl20ClientPath)) {
                    New-Item -Path $ssl20ClientPath -Force | Out-Null
                }
                Set-ItemProperty -Path $ssl20ClientPath -Name "Enabled" -Value 0 -Type DWord
                Set-ItemProperty -Path $ssl20ClientPath -Name "DisabledByDefault" -Value 1 -Type DWord

                # Disable SSL 3.0
                $ssl30ServerPath = "$schannel\SSL 3.0\Server"
                $ssl30ClientPath = "$schannel\SSL 3.0\Client"

                if (-not (Test-Path $ssl30ServerPath)) {
                    New-Item -Path $ssl30ServerPath -Force | Out-Null
                }
                Set-ItemProperty -Path $ssl30ServerPath -Name "Enabled" -Value 0 -Type DWord
                Set-ItemProperty -Path $ssl30ServerPath -Name "DisabledByDefault" -Value 1 -Type DWord

                if (-not (Test-Path $ssl30ClientPath)) {
                    New-Item -Path $ssl30ClientPath -Force | Out-Null
                }
                Set-ItemProperty -Path $ssl30ClientPath -Name "Enabled" -Value 0 -Type DWord
                Set-ItemProperty -Path $ssl30ClientPath -Name "DisabledByDefault" -Value 1 -Type DWord

                Write-Host "Internet Explorer security and cryptography settings configured successfully"

  - name: validate
    steps:
      - name: ValidateIESecurityConfiguration
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "Validating Internet Explorer security registry configuration..."

                $validationErrors = @()

                # Validate IE print info disclosure fix
                try {
                    $printFix1 = Get-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Internet Explorer\Main\FeatureControl\FEATURE_ENABLE_PRINT_INFO_DISCLOSURE_FIX" -Name "iexplore.exe" -ErrorAction SilentlyContinue | Select-Object -ExpandProperty "iexplore.exe"
                    if ($printFix1 -ne 1) {
                        $validationErrors += "IE print info disclosure fix (64-bit) should be enabled (1), found: $printFix1"
                    }

                    $printFix2 = Get-ItemProperty -Path "HKLM:\SOFTWARE\WOW6432Node\Microsoft\Internet Explorer\Main\FeatureControl\FEATURE_ENABLE_PRINT_INFO_DISCLOSURE_FIX" -Name "iexplore.exe" -ErrorAction SilentlyContinue | Select-Object -ExpandProperty "iexplore.exe"
                    if ($printFix2 -ne 1) {
                        $validationErrors += "IE print info disclosure fix (32-bit) should be enabled (1), found: $printFix2"
                    }
                }
                catch {
                    $validationErrors += "Failed to validate IE print info disclosure fix: $($_.Exception.Message)"
                }

                # Validate IE exception handler hardening
                try {
                    $exceptionHardening1 = Get-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Internet Explorer\MAIN\FeatureControl\FEATURE_ALLOW_USER32_EXCEPTION_HANDLER_HARDENING" -Name "iexplore.exe" -ErrorAction SilentlyContinue | Select-Object -ExpandProperty "iexplore.exe"
                    if ($exceptionHardening1 -ne 1) {
                        $validationErrors += "IE exception handler hardening (64-bit) should be enabled (1), found: $exceptionHardening1"
                    }

                    $exceptionHardening2 = Get-ItemProperty -Path "HKLM:\SOFTWARE\Wow6432Node\Microsoft\Internet Explorer\MAIN\FeatureControl\FEATURE_ALLOW_USER32_EXCEPTION_HANDLER_HARDENING" -Name "iexplore.exe" -ErrorAction SilentlyContinue | Select-Object -ExpandProperty "iexplore.exe"
                    if ($exceptionHardening2 -ne 1) {
                        $validationErrors += "IE exception handler hardening (32-bit) should be enabled (1), found: $exceptionHardening2"
                    }
                }
                catch {
                    $validationErrors += "Failed to validate IE exception handler hardening: $($_.Exception.Message)"
                }

                # Validate cryptography settings
                try {
                    $certPaddingCheck1 = Get-ItemProperty -Path "HKLM:\Software\Microsoft\Cryptography\Wintrust\Config" -Name "EnableCertPaddingCheck" -ErrorAction SilentlyContinue | Select-Object -ExpandProperty EnableCertPaddingCheck
                    if ($certPaddingCheck1 -ne "1") {
                        $validationErrors += "Certificate padding check (64-bit) should be enabled ('1'), found: $certPaddingCheck1"
                    }

                    $certPaddingCheck2 = Get-ItemProperty -Path "HKLM:\Software\Wow6432Node\Microsoft\Cryptography\Wintrust\Config" -Name "EnableCertPaddingCheck" -ErrorAction SilentlyContinue | Select-Object -ExpandProperty EnableCertPaddingCheck
                    if ($certPaddingCheck2 -ne "1") {
                        $validationErrors += "Certificate padding check (32-bit) should be enabled ('1'), found: $certPaddingCheck2"
                    }
                }
                catch {
                    $validationErrors += "Failed to validate cryptography settings: $($_.Exception.Message)"
                }

                # Validate SSL/TLS settings
                try {
                    $ssl20Disabled = Get-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\SecurityProviders\SCHANNEL\Protocols\SSL 2.0\Server" -Name "Enabled" -ErrorAction SilentlyContinue | Select-Object -ExpandProperty Enabled
                    if ($ssl20Disabled -ne 0) {
                        $validationErrors += "SSL 2.0 should be disabled (0), found: $ssl20Disabled"
                    }

                    $ssl30Disabled = Get-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\SecurityProviders\SCHANNEL\Protocols\SSL 3.0\Server" -Name "Enabled" -ErrorAction SilentlyContinue | Select-Object -ExpandProperty Enabled
                    if ($ssl30Disabled -ne 0) {
                        $validationErrors += "SSL 3.0 should be disabled (0), found: $ssl30Disabled"
                    }
                }
                catch {
                    $validationErrors += "Failed to validate SSL/TLS settings: $($_.Exception.Message)"
                }

                # Report validation results
                if ($validationErrors.Count -eq 0) {
                    Write-Host "VALIDATION SUCCESS: Internet Explorer security registry settings configured correctly"
                    Write-Host "- IE Print Info Disclosure Fix: Enabled for both 32-bit and 64-bit"
                    Write-Host "- IE Exception Handler Hardening: Enabled for both 32-bit and 64-bit"
                    Write-Host "- Certificate Padding Check: Enabled for both 32-bit and 64-bit"
                    Write-Host "- SSL/TLS: SSL 2.0 and SSL 3.0 disabled"
                    Write-Host "- DEP and ASLR: Enabled for Internet Explorer"
                    exit 0
                } else {
                    Write-Error "VALIDATION FAILED: Internet Explorer security registry configuration errors found:"
                    foreach ($error in $validationErrors) {
                        Write-Error "  - $error"
                    }
                    exit 1
                }
