#!/bin/bash

# Shell script equivalent of get-name.py
# Makes a POST request to the getname API endpoint

api_url="http://localhost:8030/getname"

# JSON payload
payload='{
  "object_type": "SRV",
  "reference": "AWS-TEST"
}'

# Make the POST request using curl
response=$(curl -s -w "HTTPSTATUS:%{http_code}" \
  -X POST \
  -H "Content-Type: application/json" \
  -d "$payload" \
  "$api_url")

# Extract HTTP status code and response body
http_code=$(echo "$response" | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
response_body=$(echo "$response" | sed -E 's/HTTPSTATUS:[0-9]*$//')

# Format and display the response
echo "getData >> RESPONSE: $http_code"
echo "$response_body" | jq '.' 2>/dev/null || echo "$response_body"
echo
