#!/bin/bash

# Docker Container Restart Script
# This script identifies and restarts only containers that are in a failed/error state.

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to check if Docker is available
check_docker() {
    print_status $BLUE "🐳 Docker Failed Container Restart Script"
    print_status $BLUE "=============================================="

    if ! command -v docker &> /dev/null; then
        print_status $RED "Docker command not found. Please install Docker."
        exit 1
    fi

    if ! docker version &> /dev/null; then
        print_status $RED "Docker is not running. Please start Docker daemon."
        exit 1
    fi

    print_status $GREEN "Docker is available and running"
}

# Function to get containers in failed/error state
get_failed_containers() {
    # Get containers that are in Exited state with non-zero exit code or Restarting state
    docker ps -a --filter "status=exited" --format "{{.ID}} {{.Status}}" | \
    awk '$3 != "0" {print $1}' 2>/dev/null || true

    # Also get containers that are in restarting state (usually indicates failure)
    docker ps -a --filter "status=restarting" --format "{{.ID}}" 2>/dev/null || true

    # Get containers that have been dead for a while
    docker ps -a --filter "status=dead" --format "{{.ID}}" 2>/dev/null || true
}

# Function to get container names for failed containers
get_failed_container_info() {
    local failed_containers="$1"
    if [ -n "$failed_containers" ]; then
        echo "$failed_containers" | while read -r container_id; do
            if [ -n "$container_id" ]; then
                local name status
                name=$(docker inspect --format '{{.Name}}' "$container_id" 2>/dev/null | sed 's/^\//')
                status=$(docker inspect --format '{{.State.Status}}' "$container_id" 2>/dev/null)
                echo "  - $name ($container_id): $status"
            fi
        done
    fi
}

# Function to restart failed containers
restart_failed_containers() {
    local failed_containers
    failed_containers=$(get_failed_containers | sort -u)

    if [ -z "$failed_containers" ]; then
        print_status $GREEN "✅ No failed containers found - all containers are healthy!"
        return 0
    fi

    local container_count
    container_count=$(echo "$failed_containers" | wc -l)

    print_status $YELLOW "🔍 Found $container_count failed containers:"
    get_failed_container_info "$failed_containers"

    print_status $BLUE "\n� Restarting failed containers..."

    local success_count=0
    local fail_count=0

    echo "$failed_containers" | while read -r container_id; do
        if [ -n "$container_id" ]; then
            local name
            name=$(docker inspect --format '{{.Name}}' "$container_id" 2>/dev/null | sed 's/^\//' || echo "unknown")

            print_status $BLUE "  🔄 Restarting $name ($container_id)..."

            if docker restart "$container_id" &>/dev/null; then
                print_status $GREEN "    ✅ Successfully restarted $name"
                ((success_count++))
            else
                print_status $RED "    ❌ Failed to restart $name"
                ((fail_count++))
            fi
        fi
    done

    if [ $fail_count -eq 0 ]; then
        print_status $GREEN "\n✅ Successfully restarted all $success_count failed containers"
        return 0
    else
        print_status $YELLOW "\n⚠️  Restarted $success_count containers, but $fail_count failed"
        return 1
    fi
}

# Function to show container status with health information
show_status() {
    print_status $BLUE "\n📊 Current container status:"

    # Show all containers with their status
    if docker ps -a --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" 2>/dev/null; then
        echo ""

        # Check for any remaining failed containers
        local remaining_failed
        remaining_failed=$(get_failed_containers)

        if [ -n "$remaining_failed" ]; then
            local count
            count=$(echo "$remaining_failed" | wc -l)
            print_status $YELLOW "⚠️  Warning: $count containers still in failed state"
        else
            print_status $GREEN "✅ All containers are in healthy state"
        fi
    else
        print_status $YELLOW "No containers found or failed to get status"
    fi
}

# Function to wait with countdown
wait_with_countdown() {
    local seconds=$1
    local message=$2
    
    print_status $YELLOW "$message"
    for ((i=seconds; i>0; i--)); do
        echo -ne "\r⏳ Waiting... ${i}s remaining"
        sleep 1
    done
    echo -e "\r⏳ Waiting... Done!     "
}

# Main function
main() {
    # Check Docker availability
    check_docker

    # Show current status first
    print_status $BLUE "\n🔍 Checking current container status..."
    show_status

    # Restart only failed containers
    print_status $BLUE "\n🔄 Identifying and restarting failed containers..."
    if ! restart_failed_containers; then
        print_status $YELLOW "Some containers could not be restarted, but continuing..."
    fi

    # Wait for containers to initialize
    wait_with_countdown 5 "⏳ Waiting for containers to initialize..."

    # Show final status
    show_status

    print_status $GREEN "\n✅ Failed container restart process completed!"
}

# Handle script interruption
trap 'print_status $RED "\n❌ Script interrupted. Some containers may be in inconsistent state."; exit 1' INT TERM

# Run main function
main "$@"
