{% extends "base.html" %}
{% block content %}
    <p class="text-danger"><b>{{ server_data.messages }}</b></p>

    <!-- Tabs navs  data-mdb-tab-init   -->

    <ul class="nav nav-tabs nav-justified">
        <li class="nav-item"><a class="nav-link active" data-toggle="tab" href="#tab_request" aria-current="page"><b>Server Specs</b></a></li>
        <li class="nav-item"><a class="nav-link" data-toggle="tab" href="#tab_annotations"><b>Annotations</b></a></li>
      </ul>
    <!-- Tabs navs -->

    <!-- Tabs content -->
    <form action="/deploy" method="post" name="deploy_server">
        <div class="tab-content" id="request_tabs_content">
        <!-- Portrait view -->

            <div class="tab-pane fade show active" id="tab_request" role="tabpanel" aria-labelledby="tab_request">
                <div class="form-group" name="server_specs"> <p></p>
                    <label for="name_only"><b>Create Object and Annotations Only ?</b></label>
                    <input name="name_only" id="name_only" type="checkbox"><p></p>
                    <!-- Client, Environment and OS/App Options -->
                    <table class="table">
                        <thead>
                            <tr>
                                <th scope="col" style="width: 20%;">Client</th>
                                <th scope="col" style="width: 20%;">Deployment</th>
                                <th scope="col" style="width: 15%;">Platform</th>
                                <th scope="col" style="width: 15%;">Zone</th>
                                <th scope="col" style="width: 15%;">App Type</th>
                                <th scope="col" style="width: 15%;">OS Type</th>
                            </tr>
                        </thead>
                        <tbody>
                            <td scope="col"><select id="client" name="client">
                                {% for x in ui_options.clients %}
                                <option value="{{x}}">{{x}}</option>
                                {% endfor %}
                            </select></td>
                            <td scope="col"><select name="deployment">
                                {% for x in ui_options.deployments %}
                                <option value="{{x}}">{{x}}</option>
                                {% endfor %}
                            </select></td>
                            <td scope="col"><select name="platforms">
                                {% for x in ui_options.platforms %}
                                <option value="{{x}}">{{x}}</option>
                                {% endfor %}
                            </select></td>
                            <td scope="col"><select name="zone">
                                {% for x in ui_options.locations %}
                                <option value="{{x}}">{{x}}</option>
                                {% endfor %}
                            </select></td>
                            <td scope="col"><select name="app_type">
                                {% for x in ui_options.app_types %}
                                <option value="{{x}}">{{x}}</option>
                                {% endfor %}
                            </select></td>
                            <td scope="col"><select name="os_type">
                                {% for x in ui_options.os_types %}
                                <option value="{{x}}">{{x}}</option>
                                {% endfor %}
                            </select></td>
                        </tbody>
                    </table>

                    <label for="reference" ><b>Reference Number</b></label>
                    <input type="text" required class="form-control" name="reference" id="reference" placeholder="Enter reference number"><p></p>
                    <label for="object_type"><b>Server Object Type</b></label>
                    <select id="object_type" name="object_type">
                        <option value="NONE" selected>NONE</option>
                        <option value="SRV">SRV - Server</option>
                        <option value="SDV">SDV - Appliance/Shell</option>
                        <option value="POC">POC - Proof-of-Concept</option>
                        <option value="CLS">CLS - Cluster</option>
                        <option value="LST">LST - Listener</option>
                    </select> <p></p>
                    <label for="vm_count"><b>Instance Count</b></label>
                    <input name="vm_count" id="vm_count" type="number"  min="0" max="10" value="1" placeholder="Enter Instances Count"> 
                    <hr width="100%" size="2">
                    <!-- Compute Resources -->
                    <p><b>Compute:</b></p>
                    <table class="table">
                        <tbody>           
                            <tr>
                                <td width="15%" scope="col">vCPUs</td>  
                                <td width="15%"><input name="add_vcpus" id="add_vcpus" type="number" min="0"  max="8" value="2" placeholder="0 to max 8 cpus"></td>
                                <td width="55%" scope="col"></td>  
                            </tr>
                            <tr>   
                                <td width="15%" scope="col">vRAM GB</td> 
                                <td width="15%"><input name="add_vram_gbs" id="add_vram_gbs" type="number" min="0"  max="256" value="8" placeholder="4 GB to max 256 GB"></td>
                            </tr>
                        </tbody>
                    </table>
                    <hr width="100%" size="2">
                    <p><b>Disks:</b></p>
                    <label for="disk_mirror_type"><b>Disk Mirror Type</b></label>
                    <select id="disk_mirror_type" name="disk_mirror_type">
                            <option value="NA">Not Applicable</option>
                            <option value="NML" selected>Non-Mirrored</option>
                            <option value="MSP">Mirrored</option>
                        </select><p></p>
                    <table class="table">                    
                        <thead>
                            <tr>                        
                                <th width="15%" scope="col">Disk Nr</th>  
                                <th width="15%" scope="col">Disk Type</th>  
                                <th width="15%" scope="col">Capacity in GB</th>  
                                <th width="25%" scope="col">Mount (drive letter or path)</th>
                            </tr>
                        </thead>
                        <tbody>                             
                            <tr>
                                <td width="15%" scope="col">0</td>
                                <td width="15%" scope="col">
                                    <select id="disk_types" name="disk_type_0">
                                        <option value="NONE" selected>NONE</option>
                                        <option value="DIAMOND">DIAMOND</option>
                                        <option value="SAPHIRE">SAPHIRE</option>
                                        <option value="QUARTZ">QUARTZ</option>
                                        <option value="RUBY">RUBY</option>
                                        <option value="AMBER">AMBER</option>
                                        <option value="EMERALD">EMERALD</option>
                                    </select>
                                </td>  
                                <td width="15%"><input name="disk_size_0" id="disk_gbs" type="number" min="0" max="4000" value="0" placeholder="Enter amount in GBs"></td>
                                <td width="15%" scope="col"><input type="text" id="disk_mount" name="disk_mount_0" placeholder="Enter drive or mount path" ></td>
                            </tr> 
                            <tr>
                                <td width="15%" scope="col">1</td>
                                <td width="15%" scope="col">
                                    <select id="disk_types" name="disk_type_1">
                                        <option value="NONE" selected>NONE</option>
                                        <option value="DIAMOND">DIAMOND</option>
                                        <option value="SAPHIRE">SAPHIRE</option>
                                        <option value="QUARTZ">QUARTZ</option>
                                        <option value="RUBY">RUBY</option>
                                        <option value="AMBER">AMBER</option>
                                        <option value="EMERALD">EMERALD</option>
                                    </select>
                                </td>  
                                <td width="15%"><input name="disk_size_1" id="disk_gbs" type="number" min="0" max="4000" value="0" placeholder="Enter amount in GBs"></td>
                                <td width="15%" scope="col"><input type="text" id="disk_mount" name="disk_mount_1" placeholder="Enter drive or mount path" ></td>
                            </tr>  
                            <tr>
                                <td width="15%" scope="col">2</td>
                                <td width="15%" scope="col">
                                    <select id="disk_types" name="disk_type_2">
                                        <option value="NONE" selected>NONE</option>
                                        <option value="DIAMOND">DIAMOND</option>
                                        <option value="SAPHIRE">SAPHIRE</option>
                                        <option value="QUARTZ">QUARTZ</option>
                                        <option value="RUBY">RUBY</option>
                                        <option value="AMBER">AMBER</option>
                                        <option value="EMERALD">EMERALD</option>
                                    </select>
                                </td>  
                                <td width="15%"><input name="disk_size_2" id="disk_gbs" type="number" min="0" max="4000" value="0" placeholder="Enter amount in GBs"></td>
                                <td width="15%" scope="col"><input type="text" id="disk_mount" name="disk_mount_2" placeholder="Enter drive or mount path" ></td>
                            </tr>  
                        </tbody>
                        
                    </table><p></p>
                    <label for="add_srm"><b>Add SRM Backup?</b></label>
                    <input name="add_srm" id="add_srm" type="checkbox"><p></p>
                    <hr width="100%" size="2">
                </div>
            <!-- Tabs content -->
            </div>       
            <div class="tab-pane fade" id="tab_annotations" role="tabpanel" aria-labelledby="tab_annotations">
                <div class="form-group" name="annotations"> <p></p>
                    <label for="update_ebx"><b>Update Existing Annotations?</b></label>
                    <input name="update_ebx" id="update_ebx" type="checkbox"><p></p>
                    <input type="text" class="form-control" name="target_server" id="target_server" placeholder="Target Server"><p></p>
                    
                    <table class="table">
                        <thead>
                            <tr>
                                <th width="25%" scope="col">Item</th> <th width="75%" scope="col">Detail</th>            
                            </tr>
                        </thead>
                        <tbody>                               
                            <tr>
                                <td scope="col">App Name</td>  
                                <td><input type="text" name="app_name" placeholder="Application Name"></td>
                            </tr>
                            <tr>   
                                <td scope="col">App Owner</td>
                                <td><input type="text" name="app_owner" placeholder="Application Owner"></td>                    
                            </tr>
                            <tr>
                                <td scope="col">Tech Owner</td>  
                                <td><input type="text" name="tech_owner" placeholder="Primary Tech Owner"></td>
                            </tr>
                            <tr>   
                                <td scope="col">Competency</td>
                                <td><input type="text" name="competency" placeholder="OS Competency"></td>                    
                            </tr>
                            <tr>   
                                <td scope="col">Location</td>
                                <td><select id="location" name="location">
                                        <option value="AUTO" selected>DC Balanced</option>
                                        <option value="BDC">Bellville DC</option>
                                        <option value="CDC">Cape Town DC</option>
                                        <option value="AWS">AWS Cloud</option>
                                    </select><p></p>
                                </td>                    
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>       

        </div>
        <button type="submit" class="btn btn-primary" id="request_server">Submit</button><p></p>
    </form>

    <!-- Tabs content -->


{% endblock %}


   