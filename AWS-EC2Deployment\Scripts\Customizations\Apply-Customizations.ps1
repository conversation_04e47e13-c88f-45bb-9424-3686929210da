<#
    .NOTES
    File Name: Apply-Customizations.ps1
    Author: <PERSON><PERSON>
    Version: 1.0.0 - 03/09/2025
    Description: Applies system customizations including firewall rules, registry settings, and software deployment
    Note: This script is designed to be called from SSM Automation runbooks

    .NOTES
    Version 1.0.0     - Base Script
#>

param(
    [Parameter(Mandatory=$true)]
    [hashtable]$DeploymentConfig,
    
    [Parameter(Mandatory=$true)]
    [string]$S3BucketName,
    
    [Parameter(Mandatory=$false)]
    [string]$S3CustomizationsPrefix = "windows/customizations",
    
    [Parameter(Mandatory=$false)]
    [string]$LocalWorkingPath = "C:\Temp\ServerInstalls",
    
    [Parameter(Mandatory=$false)]
    [string]$Region = "af-south-1",
    
    [Parameter(Mandatory=$false)]
    [array]$FirewallRules = @(),
    
    [Parameter(Mandatory=$false)]
    [array]$RegistrySettings = @(),
    
    [Parameter(Mandatory=$false)]
    [array]$SoftwarePackages = @(),
    
    [Parameter(Mandatory=$false)]
    [switch]$CleanupAfterApply = $true,
    
    [Parameter(Mandatory=$false)]
    [switch]$OutputJson = $false
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Function to write structured log messages
function Write-LogMessage {
    param(
        [string]$Message,
        [ValidateSet("INFO", "WARN", "ERROR", "SUCCESS")]
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    switch ($Level) {
        "INFO" { Write-Host $logMessage -ForegroundColor Cyan }
        "WARN" { Write-Host $logMessage -ForegroundColor Yellow }
        "ERROR" { Write-Host $logMessage -ForegroundColor Red }
        "SUCCESS" { Write-Host $logMessage -ForegroundColor Green }
    }
}

# Function to create working directory
function New-WorkingDirectory {
    param(
        [string]$Path
    )
    
    try {
        if (-not (Test-Path $Path)) {
            Write-LogMessage "Creating working directory: $Path" "INFO"
            New-Item -Path $Path -ItemType Directory -Force | Out-Null
            Write-LogMessage "Working directory created successfully" "SUCCESS"
        } else {
            Write-LogMessage "Working directory already exists: $Path" "INFO"
        }
        
        return $Path
        
    } catch {
        Write-LogMessage "Failed to create working directory: $($_.Exception.Message)" "ERROR"
        throw
    }
}

# Function to get customizations configuration from S3
function Get-CustomizationsConfigurationFromS3 {
    param(
        [hashtable]$Config,
        [string]$BucketName,
        [string]$S3Prefix,
        [string]$Region
    )

    try {
        Write-LogMessage "Loading customizations configuration from S3" "INFO"
        
        # Try multiple configuration file patterns
        $configPatterns = @(
            "$S3Prefix/$($Config.AssetOwner)_$($Config.AppType)_$($Config.Environment)_customizations.json",
            "$S3Prefix/$($Config.AssetOwner)_$($Config.AppType)_customizations.json",
            "$S3Prefix/$($Config.AssetOwner)_customizations.json",
            "$S3Prefix/default_customizations.json"
        )
        
        $customizationsConfig = $null

        foreach ($pattern in $configPatterns) {
            try {
                Write-LogMessage "Trying customizations config: $pattern" "INFO"
                
                # Check if file exists
                $fileExists = aws s3 ls "s3://$BucketName/$pattern" --region $Region 2>&1
                if ($LASTEXITCODE -eq 0) {
                    # Download and parse
                    $tempFile = [System.IO.Path]::GetTempFileName()
                    aws s3 cp "s3://$BucketName/$pattern" $tempFile --region $Region | Out-Null
                    
                    if ($LASTEXITCODE -eq 0) {
                        $customizationsConfig = Get-Content $tempFile -Raw | ConvertFrom-Json
                        Remove-Item $tempFile -Force -ErrorAction SilentlyContinue
                        Write-LogMessage "Loaded customizations configuration from: $pattern" "SUCCESS"
                        break
                    }
                }
            } catch {
                continue
            }
        }
        
        if (-not $customizationsConfig) {
            Write-LogMessage "No customizations configuration found in S3, using defaults" "WARN"
            $customizationsConfig = @{
                FirewallRules = @()
                RegistrySettings = @()
                SoftwarePackages = @()
            }
        }

        return $customizationsConfig

    } catch {
        Write-LogMessage "Failed to load customizations configuration: $($_.Exception.Message)" "ERROR"
        throw
    }
}

# Function to apply firewall rules
function Set-FirewallRules {
    param(
        [array]$Rules
    )
    
    try {
        Write-LogMessage "Applying firewall rules" "INFO"
        
        $appliedRules = @()
        $failedRules = @()
        
        foreach ($rule in $Rules) {
            try {
                Write-LogMessage "Applying firewall rule: $($rule.Name)" "INFO"
                
                # Build netsh command parameters
                $params = @()
                $params += "advfirewall"
                $params += "firewall"
                $params += "add"
                $params += "rule"
                $params += "name=`"$($rule.Name)`""
                
                if ($rule.Direction) { $params += "dir=$($rule.Direction)" }
                if ($rule.Action) { $params += "action=$($rule.Action)" }
                if ($rule.Protocol) { $params += "protocol=$($rule.Protocol)" }
                if ($rule.LocalPort) { $params += "localport=$($rule.LocalPort)" }
                if ($rule.RemotePort) { $params += "remoteport=$($rule.RemotePort)" }
                if ($rule.LocalIP) { $params += "localip=$($rule.LocalIP)" }
                if ($rule.RemoteIP) { $params += "remoteip=$($rule.RemoteIP)" }
                if ($rule.Profile) { $params += "profile=$($rule.Profile)" }
                
                # Execute netsh command
                $result = & netsh @params 2>&1
                
                if ($LASTEXITCODE -eq 0) {
                    Write-LogMessage "Successfully applied firewall rule: $($rule.Name)" "SUCCESS"
                    $appliedRules += $rule
                } else {
                    throw "netsh command failed: $result"
                }
                
            } catch {
                Write-LogMessage "Failed to apply firewall rule '$($rule.Name)': $($_.Exception.Message)" "ERROR"
                $failedRules += @{
                    Rule = $rule
                    Error = $_.Exception.Message
                }
            }
        }
        
        return @{
            Applied = $appliedRules
            Failed = $failedRules
            Total = $Rules.Count
        }
        
    } catch {
        Write-LogMessage "Failed to apply firewall rules: $($_.Exception.Message)" "ERROR"
        throw
    }
}

# Function to apply registry settings
function Set-RegistrySettings {
    param(
        [array]$Settings
    )
    
    try {
        Write-LogMessage "Applying registry settings" "INFO"
        
        $appliedSettings = @()
        $failedSettings = @()
        
        foreach ($setting in $Settings) {
            try {
                Write-LogMessage "Applying registry setting: $($setting.Path)\$($setting.Name)" "INFO"
                
                # Ensure the registry path exists
                if (-not (Test-Path $setting.Path)) {
                    New-Item -Path $setting.Path -Force | Out-Null
                    Write-LogMessage "Created registry path: $($setting.Path)" "INFO"
                }
                
                # Set the registry value
                Set-ItemProperty -Path $setting.Path -Name $setting.Name -Value $setting.Value -Type $setting.Type -Force
                
                # Verify the setting was applied
                $verifyValue = Get-ItemProperty -Path $setting.Path -Name $setting.Name -ErrorAction SilentlyContinue
                if ($verifyValue -and $verifyValue.($setting.Name) -eq $setting.Value) {
                    Write-LogMessage "Successfully applied registry setting: $($setting.Name)" "SUCCESS"
                    $appliedSettings += $setting
                } else {
                    throw "Registry value verification failed"
                }
                
            } catch {
                Write-LogMessage "Failed to apply registry setting '$($setting.Path)\$($setting.Name)': $($_.Exception.Message)" "ERROR"
                $failedSettings += @{
                    Setting = $setting
                    Error = $_.Exception.Message
                }
            }
        }
        
        return @{
            Applied = $appliedSettings
            Failed = $failedSettings
            Total = $Settings.Count
        }
        
    } catch {
        Write-LogMessage "Failed to apply registry settings: $($_.Exception.Message)" "ERROR"
        throw
    }
}

# Function to deploy software packages from S3
function Deploy-SoftwarePackages {
    param(
        [array]$Packages,
        [string]$BucketName,
        [string]$S3Prefix,
        [string]$LocalPath,
        [string]$Region
    )
    
    try {
        Write-LogMessage "Deploying software packages from S3" "INFO"
        
        $deployedPackages = @()
        $failedPackages = @()
        $downloadedFiles = @()
        
        foreach ($package in $Packages) {
            try {
                Write-LogMessage "Processing software package: $($package.Name)" "INFO"
                
                # Download package from S3
                $s3Key = "$S3Prefix/$($package.S3Key)"
                $localFile = Join-Path $LocalPath $package.FileName
                
                Write-LogMessage "Downloading from s3://$BucketName/$s3Key" "INFO"
                aws s3 cp "s3://$BucketName/$s3Key" $localFile --region $Region | Out-Null
                
                if ($LASTEXITCODE -ne 0 -or -not (Test-Path $localFile)) {
                    throw "Failed to download package from S3"
                }
                
                $downloadedFiles += $localFile
                
                # Deploy/copy to target location
                if ($package.TargetPath) {
                    # Ensure target directory exists
                    $targetDir = Split-Path $package.TargetPath -Parent
                    if (-not (Test-Path $targetDir)) {
                        New-Item -Path $targetDir -ItemType Directory -Force | Out-Null
                    }
                    
                    # Copy file to target location
                    Copy-Item -Path $localFile -Destination $package.TargetPath -Force
                    Write-LogMessage "Copied package to: $($package.TargetPath)" "SUCCESS"
                } else {
                    Write-LogMessage "Package downloaded to: $localFile" "SUCCESS"
                }
                
                $deployedPackages += $package
                
            } catch {
                Write-LogMessage "Failed to deploy package '$($package.Name)': $($_.Exception.Message)" "ERROR"
                $failedPackages += @{
                    Package = $package
                    Error = $_.Exception.Message
                }
            }
        }
        
        return @{
            Deployed = $deployedPackages
            Failed = $failedPackages
            DownloadedFiles = $downloadedFiles
            Total = $Packages.Count
        }
        
    } catch {
        Write-LogMessage "Failed to deploy software packages: $($_.Exception.Message)" "ERROR"
        throw
    }
}

# Main execution logic
try {
    Write-LogMessage "Starting System Customizations Application" "INFO"
    Write-LogMessage "S3 Bucket: $S3BucketName, Prefix: $S3CustomizationsPrefix" "INFO"
    Write-LogMessage "Deployment Config: $($DeploymentConfig.AssetOwner)/$($DeploymentConfig.AppType)/$($DeploymentConfig.Environment)" "INFO"

    # Step 1: Create working directory
    $workingDir = New-WorkingDirectory -Path $LocalWorkingPath

    # Step 2: Load customizations configuration from S3
    $s3CustomizationsConfig = Get-CustomizationsConfigurationFromS3 -Config $DeploymentConfig -BucketName $S3BucketName -S3Prefix $S3CustomizationsPrefix -Region $Region

    # Step 3: Merge configurations (parameters override S3 config)
    $finalFirewallRules = if ($FirewallRules.Count -gt 0) { $FirewallRules } else { $s3CustomizationsConfig.FirewallRules }
    $finalRegistrySettings = if ($RegistrySettings.Count -gt 0) { $RegistrySettings } else { $s3CustomizationsConfig.RegistrySettings }
    $finalSoftwarePackages = if ($SoftwarePackages.Count -gt 0) { $SoftwarePackages } else { $s3CustomizationsConfig.SoftwarePackages }

    Write-LogMessage "Configuration summary: $($finalFirewallRules.Count) firewall rules, $($finalRegistrySettings.Count) registry settings, $($finalSoftwarePackages.Count) software packages" "INFO"

    # Step 4: Apply firewall rules
    $firewallResult = @{ Applied = @(); Failed = @(); Total = 0 }
    if ($finalFirewallRules -and $finalFirewallRules.Count -gt 0) {
        $firewallResult = Set-FirewallRules -Rules $finalFirewallRules
    } else {
        Write-LogMessage "No firewall rules to apply" "INFO"
    }

    # Step 5: Apply registry settings
    $registryResult = @{ Applied = @(); Failed = @(); Total = 0 }
    if ($finalRegistrySettings -and $finalRegistrySettings.Count -gt 0) {
        $registryResult = Set-RegistrySettings -Settings $finalRegistrySettings
    } else {
        Write-LogMessage "No registry settings to apply" "INFO"
    }

    # Step 6: Deploy software packages
    $softwareResult = @{ Deployed = @(); Failed = @(); DownloadedFiles = @(); Total = 0 }
    if ($finalSoftwarePackages -and $finalSoftwarePackages.Count -gt 0) {
        $softwareResult = Deploy-SoftwarePackages -Packages $finalSoftwarePackages -BucketName $S3BucketName -S3Prefix $S3CustomizationsPrefix -LocalPath $workingDir -Region $Region
    } else {
        Write-LogMessage "No software packages to deploy" "INFO"
    }

    # Step 7: Cleanup if requested
    if ($CleanupAfterApply -and $softwareResult.DownloadedFiles) {
        Write-LogMessage "Cleaning up downloaded files" "INFO"
        foreach ($file in $softwareResult.DownloadedFiles) {
            try {
                if (Test-Path $file) {
                    Remove-Item $file -Force
                    Write-LogMessage "Cleaned up: $(Split-Path $file -Leaf)" "INFO"
                }
            } catch {
                Write-LogMessage "Failed to cleanup file: $file" "WARN"
            }
        }

        # Remove working directory if empty
        try {
            if ((Get-ChildItem $workingDir -ErrorAction SilentlyContinue).Count -eq 0) {
                Remove-Item $workingDir -Force
                Write-LogMessage "Removed empty working directory" "INFO"
            }
        } catch {
            Write-LogMessage "Failed to remove working directory" "WARN"
        }
    }

    # Step 8: Calculate overall success
    $totalFailures = $firewallResult.Failed.Count + $registryResult.Failed.Count + $softwareResult.Failed.Count
    $overallSuccess = ($totalFailures -eq 0)

    # Step 9: Build final result object
    $finalResult = @{
        Success = $overallSuccess
        DeploymentConfig = $DeploymentConfig
        S3Bucket = $S3BucketName
        S3Prefix = $S3CustomizationsPrefix
        WorkingPath = $LocalWorkingPath

        # Results by category
        FirewallRules = $firewallResult
        RegistrySettings = $registryResult
        SoftwarePackages = $softwareResult

        # Summary
        Summary = @{
            TotalFirewallRules = $firewallResult.Total
            AppliedFirewallRules = $firewallResult.Applied.Count
            FailedFirewallRules = $firewallResult.Failed.Count

            TotalRegistrySettings = $registryResult.Total
            AppliedRegistrySettings = $registryResult.Applied.Count
            FailedRegistrySettings = $registryResult.Failed.Count

            TotalSoftwarePackages = $softwareResult.Total
            DeployedSoftwarePackages = $softwareResult.Deployed.Count
            FailedSoftwarePackages = $softwareResult.Failed.Count

            OverallFailures = $totalFailures
        }

        # Metadata
        CleanupPerformed = $CleanupAfterApply
        Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss UTC"
    }

    # Output results
    if ($OutputJson) {
        Write-Output ($finalResult | ConvertTo-Json -Depth 10)
    } else {
        Write-LogMessage "=== SYSTEM CUSTOMIZATIONS COMPLETED ===" $(if ($overallSuccess) { "SUCCESS" } else { "WARN" })
        Write-LogMessage "Firewall Rules: $($firewallResult.Applied.Count)/$($firewallResult.Total) applied" "INFO"
        Write-LogMessage "Registry Settings: $($registryResult.Applied.Count)/$($registryResult.Total) applied" "INFO"
        Write-LogMessage "Software Packages: $($softwareResult.Deployed.Count)/$($softwareResult.Total) deployed" "INFO"
        Write-LogMessage "Total Failures: $totalFailures" $(if ($totalFailures -eq 0) { "SUCCESS" } else { "WARN" })

        # Show details for failures
        if ($totalFailures -gt 0) {
            Write-LogMessage "Failed Items:" "ERROR"
            foreach ($failed in $firewallResult.Failed) {
                Write-LogMessage "  Firewall Rule: $($failed.Rule.Name) - $($failed.Error)" "ERROR"
            }
            foreach ($failed in $registryResult.Failed) {
                Write-LogMessage "  Registry Setting: $($failed.Setting.Path)\$($failed.Setting.Name) - $($failed.Error)" "ERROR"
            }
            foreach ($failed in $softwareResult.Failed) {
                Write-LogMessage "  Software Package: $($failed.Package.Name) - $($failed.Error)" "ERROR"
            }
        }
    }

    # Return result for use in runbook
    return $finalResult

} catch {
    Write-LogMessage "System customizations failed: $($_.Exception.Message)" "ERROR"

    $errorResult = @{
        Success = $false
        Error = $true
        Message = $_.Exception.Message
        DeploymentConfig = $DeploymentConfig
        S3Bucket = $S3BucketName
        S3Prefix = $S3CustomizationsPrefix
        ScriptName = $MyInvocation.MyCommand.Name
        LineNumber = $_.InvocationInfo.ScriptLineNumber
        Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss UTC"
    }

    if ($OutputJson) {
        Write-Output ($errorResult | ConvertTo-Json -Depth 5)
    }

    exit 1
}
