# AWS Image Builder Component: Configure SQL Server Firewall Rules
# This component configures Windows Firewall rules specifically for Microsoft SQL Server environments

name: configure-firewall-database
description: Configure Windows Firewall rules for Microsoft SQL Server database services
schemaVersion: 1.0
phases:
  - name: build
    steps:
      - name: ConfigureSQLServerRules
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "Configuring Microsoft SQL Server firewall rules..."

                # SQL Server default instance (Port 1433)
                Write-Host "Allowing SQL Server default instance (port 1433)..."
                New-NetFirewallRule -DisplayName "SQL Server Default Instance" -Direction Inbound -Action Allow -Protocol TCP -LocalPort 1433 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow SQL Server default instance from internal networks"

                # SQL Server Browser Service (Port 1434 UDP)
                Write-Host "Allowing SQL Server Browser Service (port 1434 UDP)..."
                New-NetFirewallRule -DisplayName "SQL Server Browser Service" -Direction Inbound -Action Allow -Protocol UDP -LocalPort 1434 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow SQL Server Browser Service from internal networks"

                # SQL Server named instances (Dynamic ports range)
                Write-Host "Allowing SQL Server named instances (dynamic ports)..."
                New-NetFirewallRule -DisplayName "SQL Server Named Instances" -Direction Inbound -Action Allow -Protocol TCP -LocalPort 1024-65535 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow SQL Server named instances from internal networks"

                # SQL Server Analysis Services (Port 2383)
                Write-Host "Allowing SQL Server Analysis Services (port 2383)..."
                New-NetFirewallRule -DisplayName "SQL Server Analysis Services" -Direction Inbound -Action Allow -Protocol TCP -LocalPort 2383 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow SQL Server Analysis Services from internal networks"

                # SQL Server Reporting Services (Port 80/443)
                Write-Host "Allowing SQL Server Reporting Services (ports 80, 443)..."
                New-NetFirewallRule -DisplayName "SQL Server Reporting Services HTTP" -Direction Inbound -Action Allow -Protocol TCP -LocalPort 80 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow SQL Server Reporting Services HTTP from internal networks"
                New-NetFirewallRule -DisplayName "SQL Server Reporting Services HTTPS" -Direction Inbound -Action Allow -Protocol TCP -LocalPort 443 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow SQL Server Reporting Services HTTPS from internal networks"

                # SQL Server Integration Services (Port 135 for RPC)
                Write-Host "Allowing SQL Server Integration Services RPC (port 135)..."
                New-NetFirewallRule -DisplayName "SQL Server Integration Services RPC" -Direction Inbound -Action Allow -Protocol TCP -LocalPort 135 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow SQL Server Integration Services RPC from internal networks"

                # SQL Server Service Broker (Port 4022)
                Write-Host "Allowing SQL Server Service Broker (port 4022)..."
                New-NetFirewallRule -DisplayName "SQL Server Service Broker" -Direction Inbound -Action Allow -Protocol TCP -LocalPort 4022 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow SQL Server Service Broker from internal networks"

                # SQL Server Database Mirroring (Port 5022)
                Write-Host "Allowing SQL Server Database Mirroring (port 5022)..."
                New-NetFirewallRule -DisplayName "SQL Server Database Mirroring" -Direction Inbound -Action Allow -Protocol TCP -LocalPort 5022 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow SQL Server Database Mirroring from internal networks"

                # SQL Server Always On Availability Groups (Port 5022)
                Write-Host "Allowing SQL Server Always On Availability Groups (port 5022)..."
                New-NetFirewallRule -DisplayName "SQL Server Always On AG" -Direction Inbound -Action Allow -Protocol TCP -LocalPort 5022 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow SQL Server Always On Availability Groups from internal networks"

                # SQL Server Backup and Restore operations
                Write-Host "Allowing SQL Server backup and restore services..."
                New-NetFirewallRule -DisplayName "SQL Server Backup Services" -Direction Inbound -Action Allow -Protocol TCP -LocalPort 3050,5000 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow SQL Server backup services from internal networks"

                # Enable built-in SQL Server firewall rules if they exist
                Write-Host "Enabling built-in SQL Server firewall rules..."
                $sqlRuleGroups = @(
                    "SQL Server",
                    "SQL Server Analysis Services",
                    "SQL Server Reporting Services"
                )

                foreach ($ruleGroup in $sqlRuleGroups) {
                    try {
                        Enable-NetFirewallRule -DisplayGroup $ruleGroup -ErrorAction SilentlyContinue
                        Write-Host "Enabled firewall rule group: $ruleGroup"
                    }
                    catch {
                        Write-Host "Could not enable firewall rule group $ruleGroup (may not exist): $($_.Exception.Message)" -ForegroundColor Yellow
                    }
                }

                Write-Host "Microsoft SQL Server firewall rules configured successfully"

  - name: validate
    steps:
      - name: ValidateSQLServerFirewallConfiguration
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "Validating Microsoft SQL Server firewall configuration..."

                $validationErrors = @()

                # Check if key SQL Server rules exist
                $existingRules = Get-NetFirewallRule | Where-Object { $_.Enabled -eq "True" }

                $requiredSQLServerRules = @(
                    "SQL Server Default Instance",
                    "SQL Server Browser Service",
                    "SQL Server Analysis Services",
                    "SQL Server Reporting Services HTTP",
                    "SQL Server Service Broker",
                    "SQL Server Database Mirroring",
                    "SQL Server Always On AG"
                )

                $missingRules = @()
                foreach ($rule in $requiredSQLServerRules) {
                    $foundRule = $existingRules | Where-Object { $_.DisplayName -like "*$rule*" }
                    if (-not $foundRule) {
                        $missingRules += $rule
                    }
                }

                if ($missingRules.Count -gt 0) {
                    $validationErrors += "Missing SQL Server firewall rules: $($missingRules -join ', ')"
                }

                # Validate specific SQL Server port rules
                try {
                    $sqlServerRule = Get-NetFirewallRule -DisplayName "SQL Server Default Instance" -ErrorAction SilentlyContinue
                    if ($sqlServerRule) {
                        $sqlPortFilter = Get-NetFirewallPortFilter -AssociatedNetFirewallRule $sqlServerRule
                        if ($sqlPortFilter.LocalPort -ne "1433") {
                            $validationErrors += "SQL Server Default Instance rule should be on port 1433, found: $($sqlPortFilter.LocalPort)"
                        }
                    }

                    $sqlBrowserRule = Get-NetFirewallRule -DisplayName "SQL Server Browser Service" -ErrorAction SilentlyContinue
                    if ($sqlBrowserRule) {
                        $sqlBrowserPortFilter = Get-NetFirewallPortFilter -AssociatedNetFirewallRule $sqlBrowserRule
                        if ($sqlBrowserPortFilter.LocalPort -ne "1434") {
                            $validationErrors += "SQL Server Browser Service rule should be on port 1434, found: $($sqlBrowserPortFilter.LocalPort)"
                        }
                    }

                    $ssasRule = Get-NetFirewallRule -DisplayName "SQL Server Analysis Services" -ErrorAction SilentlyContinue
                    if ($ssasRule) {
                        $ssasPortFilter = Get-NetFirewallPortFilter -AssociatedNetFirewallRule $ssasRule
                        if ($ssasPortFilter.LocalPort -ne "2383") {
                            $validationErrors += "SQL Server Analysis Services rule should be on port 2383, found: $($ssasPortFilter.LocalPort)"
                        }
                    }

                    $ssrsHttpRule = Get-NetFirewallRule -DisplayName "SQL Server Reporting Services HTTP" -ErrorAction SilentlyContinue
                    if ($ssrsHttpRule) {
                        $ssrsHttpPortFilter = Get-NetFirewallPortFilter -AssociatedNetFirewallRule $ssrsHttpRule
                        if ($ssrsHttpPortFilter.LocalPort -ne "80") {
                            $validationErrors += "SQL Server Reporting Services HTTP rule should be on port 80, found: $($ssrsHttpPortFilter.LocalPort)"
                        }
                    }

                    $serviceBrokerRule = Get-NetFirewallRule -DisplayName "SQL Server Service Broker" -ErrorAction SilentlyContinue
                    if ($serviceBrokerRule) {
                        $serviceBrokerPortFilter = Get-NetFirewallPortFilter -AssociatedNetFirewallRule $serviceBrokerRule
                        if ($serviceBrokerPortFilter.LocalPort -ne "4022") {
                            $validationErrors += "SQL Server Service Broker rule should be on port 4022, found: $($serviceBrokerPortFilter.LocalPort)"
                        }
                    }

                    $mirroringRule = Get-NetFirewallRule -DisplayName "SQL Server Database Mirroring" -ErrorAction SilentlyContinue
                    if ($mirroringRule) {
                        $mirroringPortFilter = Get-NetFirewallPortFilter -AssociatedNetFirewallRule $mirroringRule
                        if ($mirroringPortFilter.LocalPort -ne "5022") {
                            $validationErrors += "SQL Server Database Mirroring rule should be on port 5022, found: $($mirroringPortFilter.LocalPort)"
                        }
                    }
                }
                catch {
                    $validationErrors += "Failed to validate SQL Server port configurations: $($_.Exception.Message)"
                }

                # Report validation results
                if ($validationErrors.Count -eq 0) {
                    Write-Host "VALIDATION SUCCESS: Microsoft SQL Server firewall rules configured correctly"
                    Write-Host "- SQL Server Database Engine: Default instance (1433), Browser Service (1434), Named instances (dynamic)"
                    Write-Host "- SQL Server Analysis Services: Port 2383"
                    Write-Host "- SQL Server Reporting Services: Ports 80 (HTTP) and 443 (HTTPS)"
                    Write-Host "- SQL Server Integration Services: Port 135 (RPC)"
                    Write-Host "- SQL Server Service Broker: Port 4022"
                    Write-Host "- SQL Server Database Mirroring: Port 5022"
                    Write-Host "- SQL Server Always On Availability Groups: Port 5022"
                    Write-Host "- SQL Server Backup Services: Ports 3050, 5000"
                    Write-Host "- All rules restricted to internal networks only"
                    Write-Host "- Total SQL Server rules: $($existingRules | Where-Object { $_.DisplayName -like "*SQL Server*" } | Measure-Object | Select-Object -ExpandProperty Count)"
                    exit 0
                } else {
                    Write-Error "VALIDATION FAILED: Microsoft SQL Server firewall configuration errors found:"
                    foreach ($error in $validationErrors) {
                        Write-Error "  - $error"
                    }
                    exit 1
                }
