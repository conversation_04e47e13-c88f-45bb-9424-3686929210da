#Requires -RunAsAdministrator

<#
.SYNOPSIS
    Configure Windows Firewall Rules for Sanlam Shared PRD Environment

.DESCRIPTION
    This script configures Windows Firewall rules based on the Sanlam_Shared_PRD_customizations.json configuration.
    It creates specific firewall rules for RDP, WinRM, ICMP, and network access controls.

.PARAMETER ConfigPath
    Path to the JSON configuration file. Defaults to the standard customizations file.

.PARAMETER LogPath
    Path for log file output. Defaults to C:\Logs\Configure-FirewallRules.log

.EXAMPLE
    .\Configure-FirewallRules.ps1
    
.EXAMPLE
    .\Configure-FirewallRules.ps1 -ConfigPath "C:\Config\custom.json" -LogPath "C:\Logs\firewall.log"

.NOTES
    Author: AWS EC2 Deployment Automation
    Version: 1.0
    Requires: PowerShell 5.1 or higher, Administrator privileges
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory = $false)]
    [string]$ConfigPath = "C:\Coding\AWS-EC2Deployment\customizations\Sanlam_Shared_PRD_customizations.json",
    
    [Parameter(Mandatory = $false)]
    [string]$LogPath = "C:\Logs\Configure-FirewallRules.log"
)

# Initialize logging
$LogDir = Split-Path -Path $LogPath -Parent
if (-not (Test-Path -Path $LogDir)) {
    New-Item -Path $LogDir -ItemType Directory -Force | Out-Null
}

function Write-Log {
    param(
        [Parameter(Mandatory = $true)]
        [string]$Message,
        
        [Parameter(Mandatory = $false)]
        [ValidateSet("INFO", "WARN", "ERROR", "SUCCESS")]
        [string]$Level = "INFO"
    )
    
    $Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $LogMessage = "[$Timestamp] [$Level] $Message"
    
    # Write to console with color coding
    switch ($Level) {
        "INFO"    { Write-Host $LogMessage -ForegroundColor White }
        "WARN"    { Write-Host $LogMessage -ForegroundColor Yellow }
        "ERROR"   { Write-Host $LogMessage -ForegroundColor Red }
        "SUCCESS" { Write-Host $LogMessage -ForegroundColor Green }
    }
    
    # Write to log file
    Add-Content -Path $LogPath -Value $LogMessage
}

function Test-FirewallRuleExists {
    param(
        [Parameter(Mandatory = $true)]
        [string]$RuleName
    )
    
    try {
        $existingRule = Get-NetFirewallRule -DisplayName $RuleName -ErrorAction SilentlyContinue
        return ($null -ne $existingRule)
    }
    catch {
        return $false
    }
}

function New-CustomFirewallRule {
    param(
        [Parameter(Mandatory = $true)]
        [PSCustomObject]$Rule
    )
    
    try {
        # Check if rule already exists
        if (Test-FirewallRuleExists -RuleName $Rule.Name) {
            Write-Log "Firewall rule '$($Rule.Name)' already exists. Removing existing rule..." -Level "WARN"
            Remove-NetFirewallRule -DisplayName $Rule.Name -ErrorAction SilentlyContinue
        }
        
        # Build parameters for New-NetFirewallRule
        $ruleParams = @{
            DisplayName = $Rule.Name
            Direction   = $Rule.Direction
            Action      = $Rule.Action
            Profile     = $Rule.Profile
        }
        
        # Add optional parameters based on rule configuration
        if ($Rule.Protocol) {
            if ($Rule.Protocol -eq "icmpv4") {
                $ruleParams.Protocol = "ICMPv4"
                if ($Rule.IcmpType) {
                    $ruleParams.IcmpType = [int]$Rule.IcmpType
                }
            }
            elseif ($Rule.Protocol -eq "any") {
                $ruleParams.Protocol = "Any"
            }
            else {
                $ruleParams.Protocol = $Rule.Protocol.ToUpper()
            }
        }
        
        if ($Rule.LocalPort) {
            $ruleParams.LocalPort = $Rule.LocalPort
        }
        
        if ($Rule.RemoteAddress) {
            if ($Rule.RemoteAddress -eq "Internet") {
                # For Internet blocking, we'll use a more specific approach
                $ruleParams.RemoteAddress = @("0.0.0.0-*************", "********-***************", "***********-**************", "**********-***************", "***********-***************")
            }
            else {
                $ruleParams.RemoteAddress = $Rule.RemoteAddress -split ","
            }
        }
        
        if ($Rule.Description) {
            $ruleParams.Description = $Rule.Description
        }
        
        # Create the firewall rule
        New-NetFirewallRule @ruleParams | Out-Null
        Write-Log "Successfully created firewall rule: $($Rule.Name)" -Level "SUCCESS"
        
        return $true
    }
    catch {
        Write-Log "Failed to create firewall rule '$($Rule.Name)': $($_.Exception.Message)" -Level "ERROR"
        return $false
    }
}

# Main execution
try {
    Write-Log "Starting Windows Firewall Rules Configuration" -Level "INFO"
    Write-Log "Configuration file: $ConfigPath" -Level "INFO"
    Write-Log "Log file: $LogPath" -Level "INFO"
    
    # Verify configuration file exists
    if (-not (Test-Path -Path $ConfigPath)) {
        throw "Configuration file not found: $ConfigPath"
    }
    
    # Load configuration
    Write-Log "Loading configuration from: $ConfigPath" -Level "INFO"
    $config = Get-Content -Path $ConfigPath -Raw | ConvertFrom-Json
    
    if (-not $config.FirewallRules) {
        throw "No FirewallRules section found in configuration file"
    }
    
    Write-Log "Found $($config.FirewallRules.Count) firewall rules to configure" -Level "INFO"
    
    # Ensure Windows Firewall is enabled
    Write-Log "Ensuring Windows Firewall is enabled on all profiles..." -Level "INFO"
    Set-NetFirewallProfile -All -Enabled True
    
    # Configure each firewall rule
    $successCount = 0
    $failureCount = 0
    
    foreach ($rule in $config.FirewallRules) {
        Write-Log "Processing firewall rule: $($rule.Name)" -Level "INFO"
        
        if (New-CustomFirewallRule -Rule $rule) {
            $successCount++
        }
        else {
            $failureCount++
        }
    }
    
    # Summary
    Write-Log "Firewall Rules Configuration Summary:" -Level "INFO"
    Write-Log "- Total rules processed: $($config.FirewallRules.Count)" -Level "INFO"
    Write-Log "- Successfully configured: $successCount" -Level "SUCCESS"
    Write-Log "- Failed to configure: $failureCount" -Level $(if ($failureCount -gt 0) { "ERROR" } else { "INFO" })
    
    if ($failureCount -eq 0) {
        Write-Log "All firewall rules configured successfully!" -Level "SUCCESS"
        exit 0
    }
    else {
        Write-Log "Some firewall rules failed to configure. Check the log for details." -Level "WARN"
        exit 1
    }
}
catch {
    Write-Log "Critical error during firewall configuration: $($_.Exception.Message)" -Level "ERROR"
    Write-Log "Stack trace: $($_.ScriptStackTrace)" -Level "ERROR"
    exit 1
}
