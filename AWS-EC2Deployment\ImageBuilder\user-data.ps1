# ImageBuilder User Data Template
# This script is used during ImageBuilder recipe execution to prepare the base AMI
# It should NOT contain business-specific configurations like admin groups or domain joining

<powershell>
# Set execution policy
Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope LocalMachine -Force

# Create scripts directory structure for future deployment use
$directories = @(
    "C:\Temp\ServerInstalls",
    "C:\Temp\ServerInstalls\Logs",
    "C:\Temp\ServerInstalls\Config",
    "C:\Temp\ServerInstalls\Tools"
)

foreach ($dir in $directories) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
    }
}

# Create log file
$logFile = "C:\Temp\ServerInstalls\imagebuilder-prep.log"
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    Write-Host $logEntry
    $logEntry | Out-File -FilePath $logFile -Append -Encoding UTF8
}

Write-Log "Starting ImageBuilder preparation script..."

# Enable WinRM for remote management
Write-Log "Enabling WinRM for remote management"
Enable-PSRemoting -Force -SkipNetworkProfileCheck

# Configure WinRM settings
Write-Log "Configuring WinRM settings"
winrm quickconfig -q
winrm set winrm/config/winrs '@{MaxMemoryPerShellMB="512"}'
winrm set winrm/config '@{MaxTimeoutms="1800000"}'
winrm set winrm/config/service '@{AllowUnencrypted="false"}'
winrm set winrm/config/service/auth '@{Basic="true"}'

# Create self-signed certificate for HTTPS
Write-Log "Creating self-signed certificate for WinRM HTTPS"
$cert = New-SelfSignedCertificate -DnsName "localhost" -CertStoreLocation "cert:\LocalMachine\My"
winrm create winrm/config/Listener?Address=*+Transport=HTTPS "@{Hostname=`"localhost`";CertificateThumbprint=`"$($cert.Thumbprint)`"}"

# Configure firewall for WinRM HTTPS
Write-Log "Configuring firewall for WinRM HTTPS"
netsh advfirewall firewall add rule name="WinRM-HTTPS" dir=in localport=5986 protocol=TCP action=allow

# Restart WinRM service
Write-Log "Restarting WinRM service"
Restart-Service winrm

Write-Log "WinRM configuration completed"

try {
    # ImageBuilder preparation tasks
    Write-Log "Starting ImageBuilder preparation tasks..."

    # Set up Windows optimizations for server use
    Write-Log "Applying Windows server optimizations..."

    # Configure firewall rules
    Write-Log "Configuring firewall rules..."
    $firewallRules = @(
        @{Name="File and Printer Sharing (Echo Request - ICMPv4-In)"; DisplayName="File and Printer Sharing (Echo Request - ICMPv4-In)"},
        @{Name="Remote Service Management (RPC-EPMAP)"; DisplayName="Remote Service Management (RPC-EPMAP)"},
        @{Name="Remote Service Management (RPC)"; DisplayName="Remote Service Management (RPC)"},
        @{Name="Remote Service Management (NP-In)"; DisplayName="Remote Service Management (NP-In)"},
        @{Name="Windows Management Instrumentation (DCOM-In)"; DisplayName="Windows Management Instrumentation (DCOM-In)"},
        @{Name="Windows Management Instrumentation (WMI-In)"; DisplayName="Windows Management Instrumentation (WMI-In)"}
    )

    foreach ($rule in $firewallRules) {
        try {
            Enable-NetFirewallRule -DisplayName $rule.DisplayName -ErrorAction SilentlyContinue
            Write-Log "Enabled firewall rule: $($rule.DisplayName)"
        } catch {
            Write-Log "Could not enable firewall rule $($rule.DisplayName): $($_.Exception.Message)" "WARNING"
        }
    }

    # Enable RDP with Network Level Authentication
    Write-Log "Enabling RDP with Network Level Authentication..."
    try {
        Set-ItemProperty -Path "HKLM:\System\CurrentControlSet\Control\Terminal Server" -Name "fDenyTSConnections" -Value 0
        Set-ItemProperty -Path "HKLM:\System\CurrentControlSet\Control\Terminal Server\WinStations\RDP-Tcp" -Name "UserAuthentication" -Value 1
        Enable-NetFirewallRule -DisplayGroup "Remote Desktop"
        Write-Log "RDP enabled with NLA"
    } catch {
        Write-Log "Could not configure RDP: $($_.Exception.Message)" "WARNING"
    }

    # Adjust for best performance
    Write-Log "Configuring system for best performance..."
    try {
        Set-ItemProperty -Path "HKCU:\Software\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects" -Name "VisualFXSetting" -Value 2
        Set-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects" -Name "VisualFXSetting" -Value 2
        Write-Log "Set system for best performance"
    } catch {
        Write-Log "Could not set performance settings: $($_.Exception.Message)" "WARNING"
    }

    # Turn on DEP for essential Windows programs and services
    Write-Log "Configuring Data Execution Prevention..."
    try {
        bcdedit /set nx OptIn
        Write-Log "DEP configured for essential Windows programs and services"
    } catch {
        Write-Log "Could not configure DEP: $($_.Exception.Message)" "WARNING"
    }

    # Configure RecycleBin settings for C: drive
    Write-Log "Configuring RecycleBin settings..."
    try {
        Set-ItemProperty -Path "HKCU:\Software\Microsoft\Windows\CurrentVersion\Explorer\BitBucket\Volume\{C:}" -Name "NukeOnDelete" -Value 1 -ErrorAction SilentlyContinue
        Set-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\BitBucket\Volume\{C:}" -Name "NukeOnDelete" -Value 1 -ErrorAction SilentlyContinue
        Write-Log "RecycleBin configured to not move files to recycle bin for C:"
    } catch {
        Write-Log "Could not configure RecycleBin settings: $($_.Exception.Message)" "WARNING"
    }

    # Disable IPv6 (uncheck but not remove)
    Write-Log "Disabling IPv6..."
    try {
        Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Services\Tcpip6\Parameters" -Name "DisabledComponents" -Value 0xFF
        Write-Log "IPv6 disabled"
    } catch {
        Write-Log "Could not disable IPv6: $($_.Exception.Message)" "WARNING"
    }

    # Disable UAC Remote Restrictions
    Write-Log "Disabling UAC Remote Restrictions..."
    try {
        Set-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System" -Name "LocalAccountTokenFilterPolicy" -Value 1
        Write-Log "UAC Remote Restrictions disabled"
    } catch {
        Write-Log "Could not disable UAC Remote Restrictions: $($_.Exception.Message)" "WARNING"
    }

    # Apply additional registry settings
    Write-Log "Applying additional registry settings..."
    try {
        # Internet Explorer security settings
        New-Item -Path "HKLM:\SOFTWARE\Microsoft\Internet Explorer\Main\FeatureControl\FEATURE_ENABLE_PRINT_INFO_DISCLOSURE_FIX" -Force | Out-Null
        Set-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Internet Explorer\Main\FeatureControl\FEATURE_ENABLE_PRINT_INFO_DISCLOSURE_FIX" -Name "iexplore.exe" -Value 1

        New-Item -Path "HKLM:\SOFTWARE\WOW6432Node\Microsoft\Internet Explorer\Main\FeatureControl\FEATURE_ENABLE_PRINT_INFO_DISCLOSURE_FIX" -Force | Out-Null
        Set-ItemProperty -Path "HKLM:\SOFTWARE\WOW6432Node\Microsoft\Internet Explorer\Main\FeatureControl\FEATURE_ENABLE_PRINT_INFO_DISCLOSURE_FIX" -Name "iexplore.exe" -Value 1

        New-Item -Path "HKLM:\SOFTWARE\Microsoft\Internet Explorer\MAIN\FeatureControl\FEATURE_ALLOW_USER32_EXCEPTION_HANDLER_HARDENING" -Force | Out-Null
        Set-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Internet Explorer\MAIN\FeatureControl\FEATURE_ALLOW_USER32_EXCEPTION_HANDLER_HARDENING" -Name "iexplore.exe" -Value 1

        New-Item -Path "HKLM:\SOFTWARE\Wow6432Node\Microsoft\Internet Explorer\MAIN\FeatureControl\FEATURE_ALLOW_USER32_EXCEPTION_HANDLER_HARDENING" -Force | Out-Null
        Set-ItemProperty -Path "HKLM:\SOFTWARE\Wow6432Node\Microsoft\Internet Explorer\MAIN\FeatureControl\FEATURE_ALLOW_USER32_EXCEPTION_HANDLER_HARDENING" -Name "iexplore.exe" -Value 1

        # Cryptography settings
        New-Item -Path "HKLM:\Software\Microsoft\Cryptography\Wintrust\Config" -Force | Out-Null
        Set-ItemProperty -Path "HKLM:\Software\Microsoft\Cryptography\Wintrust\Config" -Name "EnableCertPaddingCheck" -Value "1"

        New-Item -Path "HKLM:\Software\Wow6432Node\Microsoft\Cryptography\Wintrust\Config" -Force | Out-Null
        Set-ItemProperty -Path "HKLM:\Software\Wow6432Node\Microsoft\Cryptography\Wintrust\Config" -Name "EnableCertPaddingCheck" -Value "1"

        Write-Log "Additional registry settings applied"
    } catch {
        Write-Log "Could not apply additional registry settings: $($_.Exception.Message)" "WARNING"
    }

    # Set high performance power plan
    try {
        powercfg /setactive 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c
        Write-Log "Set high performance power plan"
    } catch {
        Write-Log "Could not set power plan: $($_.Exception.Message)" "WARNING"
    }

    # Disable unnecessary services for server use
    $servicesToDisable = @(
        "Themes",
        "TabletInputService",
        "WSearch"  # Windows Search (can be re-enabled if needed)
    )

    foreach ($service in $servicesToDisable) {
        try {
            Set-Service -Name $service -StartupType Disabled -ErrorAction SilentlyContinue
            Write-Log "Disabled service: $service"
        } catch {
            Write-Log "Could not disable service $service`: $($_.Exception.Message)" "WARNING"
        }
    }

    # Configure page file for optimal performance
    Write-Log "Configuring system-managed page file..."
    try {
        $cs = Get-WmiObject -Class Win32_ComputerSystem
        $cs.AutomaticManagedPagefile = $true
        $cs.Put() | Out-Null

        # Remove existing page files first
        $pf = Get-WmiObject -Class Win32_PageFileSetting
        if ($pf) {
            $pf.Delete()
        }

        Write-Log "Configured system-managed page file"
    } catch {
        Write-Log "Could not configure page file: $($_.Exception.Message)" "WARNING"
    }

    # Configure disk allocation unit size to 4K for non-OS drives
    Write-Log "Configuring disk allocation unit size..."
    try {
        $disks = Get-Disk | Where-Object { $_.Number -ne 0 -and $_.PartitionStyle -ne "GPT" }
        foreach ($disk in $disks) {
            # Convert to GPT if not already
            if ($disk.PartitionStyle -eq "MBR") {
                Clear-Disk -Number $disk.Number -RemoveData -Confirm:$false
                Initialize-Disk -Number $disk.Number -PartitionStyle GPT
                Write-Log "Converted disk $($disk.Number) to GPT"
            }
        }
        Write-Log "Non-OS drives configured for GPT with 4K allocation unit size"
    } catch {
        Write-Log "Could not configure disk settings: $($_.Exception.Message)" "WARNING"
    }

    # Create image information file for deployment scripts to reference
    Write-Log "Creating image information file..."

    $imageInfo = @{
        ImageBuildDate = (Get-Date).ToString("yyyy-MM-dd HH:mm:ss")
        ImageBuilderVersion = "1.0"
        BaseAMI = "Windows Server 2022"
        PreparedBy = "ImageBuilder"
        WinRMEnabled = $true
        OptimizedForServer = $true
        ReadyForDeployment = $true
        PowerPlan = "High Performance"
        PageFile = "System Managed"
        ServicesOptimized = $true
    }

    $imageInfoPath = "C:\Temp\ServerInstalls\image-info.json"
    $imageInfo | ConvertTo-Json -Depth 10 | Out-File -FilePath $imageInfoPath -Encoding UTF8
    Write-Log "Image information saved to: $imageInfoPath"

    # Create a placeholder for deployment scripts to reference
    Write-Log "Creating deployment script placeholder..."

    $deploymentPlaceholder = @"
# This file is created during ImageBuilder preparation
# Deployment-specific configurations should be applied via EC2 user-data scripts
#
# Available directories:
# - C:\Temp\ServerInstalls\Logs    - For deployment logs
# - C:\Temp\ServerInstalls\Config  - For configuration files
# - C:\Temp\ServerInstalls\Tools   - For deployment tools
#
# Image prepared on: $(Get-Date)
# Ready for business-specific deployment
"@

    $deploymentPlaceholder | Out-File -FilePath "C:\Temp\ServerInstalls\README-Deployment.txt" -Encoding UTF8
    Write-Log "Deployment placeholder created"

    # Final ImageBuilder preparation steps
    Write-Log "Completing ImageBuilder preparation..."

    # Ensure all required directories exist
    $requiredDirs = @("C:\Temp\ServerInstalls", "C:\Temp\ServerInstalls\Logs", "C:\Temp\ServerInstalls\Config", "C:\Temp\ServerInstalls\Tools")
    foreach ($dir in $requiredDirs) {
        if (!(Test-Path $dir)) {
            New-Item -ItemType Directory -Path $dir -Force | Out-Null
            Write-Log "Created directory: $dir"
        }
    }

    # Set appropriate permissions on Scripts directory
    try {
        $acl = Get-Acl "C:\Temp\ServerInstalls"
        $accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule("Administrators", "FullControl", "ContainerInherit,ObjectInherit", "None", "Allow")
        $acl.SetAccessRule($accessRule)
        Set-Acl "C:\Temp\ServerInstalls" $acl
        Write-Log "Set permissions on C:\Temp\ServerInstalls directory"
    } catch {
        Write-Log "Could not set permissions on Scripts directory: $($_.Exception.Message)" "WARNING"
    }

    Write-Log "ImageBuilder preparation completed successfully"

} catch {
    Write-Log "ImageBuilder preparation failed: $($_.Exception.Message)" "ERROR"
    Write-Log "Stack trace: $($_.Exception.StackTrace)" "ERROR"
    exit 1
}

Write-Log "ImageBuilder user data script completed"
</powershell>

