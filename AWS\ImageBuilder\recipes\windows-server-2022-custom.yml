# AWS Image Builder Recipe: Windows Server 2022 Base
# This recipe creates a clean base Windows Server 2022 AMI for business deployment

name: WindowsServer2022Base
description: Windows Server 2022 base image with .NET 4.8, registry optimizations, and firewall rules for business deployment
schemaVersion: 1.0
version: 1.0.0

# Base image - Windows Server 2022 (automatically uses latest)
# Option 1: Use AMI name pattern (automatically gets latest)
parentImage: Windows_Server-2022-English-Full-Base

# Option 2: Use specific AMI ID (manual updates required)
# parentImage: ami-0c02fb55956c7d316

# Build and test components
components:
# AWS managed components for basic Windows configuration
- name: update-windows
  parameters:
  - name: exclude
    value:
    - "KB5005463" # Example: exclude specific updates if needed
  - name: include
    value: []

# Custom component: Install .NET Framework 4.8
- name: InstallDotNet48
  parameters: []

# Custom components: Apply registry optimizations (split into focused components)
- name: configure-registry-windowsupdate
  parameters: []
- name: configure-registry-security
  parameters: []
- name: configure-registry-performance
  parameters: []
- name: configure-registry-eventlog
  parameters: []
- name: configure-registry-iesecurity
  parameters: []

# Custom components: Configure firewall rules (split into focused components)
- name: configure-firewall-basic
  parameters: []
- name: configure-firewall-webserver
  parameters: []
- name: configure-firewall-database
  parameters: []
- name: configure-firewall-activedirectory
  parameters: []
- name: configure-firewall-system
  parameters: []
- name: configure-firewall-security
  parameters: []

# AWS managed component for final cleanup and optimization
- name: reboot-windows
  parameters: []

# Working directory for build process
workingDirectory: C:\ImageBuilder

# Additional metadata
tags:
  Environment: Production
  OS: Windows Server 2022
  Version: "1.0.0"
  Components: ".NET Framework 4.8, Registry Optimizations, Firewall Rules"
  CreatedBy: AWS Image Builder
  Purpose: Base Windows Server Image
