# AWS Image Builder Component: Core Windows Firewall Configuration
# This component configures essential Windows Firewall rules for base services, security, and system services
# Groups: Base Services, Security Blocks, System Services

name: windows-firewall-base
description: Configure Core Windows Firewall rules for base services, security blocks, and system services
schemaVersion: 1.0
phases:
  - name: build
    steps:
      - name: ConfigureBaseFirewallRules
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "=== CONFIGURING BASE FIREWALL RULES ==="

                # Create custom rule group for base rules
                $baseGroup = "Windows Server Base Services"

                # Allow Any Traffic - Any-Any Bypass Rule for Role Based Traffic Rules.
                Write-Host "Allowing All Traffic..."
                New-NetFirewallRule -DisplayName "Allow Any-Any" -Direction Inbound -Action Allow -Protocol Any -Profile Any
                New-NetFirewallRule -DisplayName "Allow Any-Any" -Direction Outbound -Action Allow -Protocol Any -Profile Any

                # Allow RDP (Remote Desktop) - Restricted to internal networks only
                Write-Host "Allowing RDP from internal networks only..."
                New-NetFirewallRule -DisplayName "Allow RDP Inbound" -Group $baseGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 3389 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow RDP from internal networks only"

                # Allow WinRM HTTP - Domain profile only for PowerShell remoting
                Write-Host "Allowing WinRM HTTP for PowerShell remoting..."
                New-NetFirewallRule -DisplayName "Allow WinRM HTTP" -Group $baseGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 5985 -Profile Domain -RemoteAddress "10.0.0.0/8" -Description "Allow WinRM for PowerShell remoting"

                # Allow WinRM HTTPS - Any profile for ImageBuilder compatibility
                Write-Host "Allowing WinRM HTTPS for secure PowerShell remoting..."
                New-NetFirewallRule -DisplayName "WinRM-HTTPS" -Group $baseGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 5986 -Profile Any -Description "WinRM HTTPS for ImageBuilder and remote management"

                Write-Host "Base firewall rules configured successfully"

      - name: ConfigureSecurityFirewallRules
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "=== CONFIGURING SECURITY FIREWALL RULES ==="

                # Create custom rule group for security rules
                $securityGroup = "Windows Server Security Blocks"

                # Block common attack ports from public networks
                Write-Host "Blocking common attack ports from public networks..."

                # Block Telnet (insecure protocol)
                New-NetFirewallRule -DisplayName "Block Telnet Public" -Group $securityGroup -Direction Inbound -Action Block -Protocol TCP -LocalPort 23 -Profile Public -Description "Block insecure Telnet from public networks"

                # Block FTP (insecure protocol) from public networks
                New-NetFirewallRule -DisplayName "Block FTP Public" -Group $securityGroup -Direction Inbound -Action Block -Protocol TCP -LocalPort 21 -Profile Public -Description "Block insecure FTP from public networks"

                # Block SNMP from public networks (information disclosure)
                New-NetFirewallRule -DisplayName "Block SNMP Public" -Group $securityGroup -Direction Inbound -Action Block -Protocol UDP -LocalPort 161 -Profile Public -Description "Block SNMP from public networks"

                # Block NetBIOS from public networks
                New-NetFirewallRule -DisplayName "Block NetBIOS Public" -Group $securityGroup -Direction Inbound -Action Block -Protocol TCP -LocalPort 139 -Profile Public -Description "Block NetBIOS from public networks"
                New-NetFirewallRule -DisplayName "Block NetBIOS Name Public" -Group $securityGroup -Direction Inbound -Action Block -Protocol UDP -LocalPort 137 -Profile Public -Description "Block NetBIOS Name Service from public networks"
                New-NetFirewallRule -DisplayName "Block NetBIOS Datagram Public" -Group $securityGroup -Direction Inbound -Action Block -Protocol UDP -LocalPort 138 -Profile Public -Description "Block NetBIOS Datagram from public networks"

                # Block SMB from public networks
                New-NetFirewallRule -DisplayName "Block SMB Public" -Group $securityGroup -Direction Inbound -Action Block -Protocol TCP -LocalPort 445 -Profile Public -Description "Block SMB from public networks"

                # Block RDP from public networks (allow only from internal)
                New-NetFirewallRule -DisplayName "Block RDP Public" -Group $securityGroup -Direction Inbound -Action Block -Protocol TCP -LocalPort 3389 -Profile Public -Description "Block RDP from public networks"

                # Block common database ports from public networks
                Write-Host "Blocking database ports from public networks..."
                $databasePorts = @(1433, 1434, 3306, 5432, 1521, 27017, 6379)
                foreach ($port in $databasePorts) {
                    New-NetFirewallRule -DisplayName "Block Database Port $port Public" -Group $securityGroup -Direction Inbound -Action Block -Protocol TCP -LocalPort $port -Profile Public -Description "Block database port $port from public networks"
                }

                # Configure outbound security settings
                Write-Host "Configuring outbound security settings..."

                # Set firewall to stealth mode (don't respond to pings from public networks)
                Set-NetFirewallProfile -Profile Public -NotifyOnListen False -AllowUnicastResponseToMulticast False -LogAllowed False -LogBlocked True

                # Disable local firewall rules creation for security
                Set-NetFirewallProfile -All -AllowLocalFirewallRules False -AllowLocalIPsecRules False

                Write-Host "Security firewall rules configured successfully"

      - name: ConfigureSystemServicesFirewallRules
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "=== CONFIGURING SYSTEM SERVICES FIREWALL RULES ==="

                # Create custom rule group for System Services rules
                $systemGroup = "Windows Server System Services"

                # DHCP Server (Ports 67, 68)
                Write-Host "Allowing DHCP Server (ports 67, 68)..."
                New-NetFirewallRule -DisplayName "DHCP Server" -Group $systemGroup -Direction Inbound -Action Allow -Protocol UDP -LocalPort 67 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow DHCP server from internal networks"
                New-NetFirewallRule -DisplayName "DHCP Client" -Group $systemGroup -Direction Inbound -Action Allow -Protocol UDP -LocalPort 68 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow DHCP client from internal networks"

                # RADIUS (Ports 1812, 1813)
                Write-Host "Allowing RADIUS (ports 1812, 1813)..."
                New-NetFirewallRule -DisplayName "RADIUS Authentication" -Group $systemGroup -Direction Inbound -Action Allow -Protocol UDP -LocalPort 1812 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow RADIUS authentication from internal networks"
                New-NetFirewallRule -DisplayName "RADIUS Accounting" -Group $systemGroup -Direction Inbound -Action Allow -Protocol UDP -LocalPort 1813 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow RADIUS accounting from internal networks"

                # LDAP over UDP (Port 389)
                Write-Host "Allowing LDAP over UDP (port 389)..."
                New-NetFirewallRule -DisplayName "LDAP UDP" -Group $systemGroup -Direction Inbound -Action Allow -Protocol UDP -LocalPort 389 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow LDAP UDP from internal networks"

                # Print Spooler (Port 515)
                Write-Host "Allowing Print Spooler (port 515)..."
                New-NetFirewallRule -DisplayName "Print Spooler" -Group $systemGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 515 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow print spooler from internal networks"

                # Enable built-in system service firewall rules
                Write-Host "Enabling built-in system service firewall rules..."
                $systemRuleGroups = @(
                    "Network Discovery",
                    "Performance Logs and Alerts",
                    "Remote Event Log Management",
                    "Remote Scheduled Tasks Management",
                    "Remote Volume Management",
                    "Windows Firewall Remote Management",
                    "Windows Management Instrumentation (WMI)"
                )

                foreach ($ruleGroup in $systemRuleGroups) {
                    try {
                        Enable-NetFirewallRule -DisplayGroup $ruleGroup -ErrorAction SilentlyContinue
                        Write-Host "Enabled firewall rule group: $ruleGroup"
                    }
                    catch {
                        Write-Host "Could not enable firewall rule group $ruleGroup`: $($_.Exception.Message)" -ForegroundColor Yellow
                    }
                }

                Write-Host "System Services firewall rules configured successfully"

  - name: validate
    steps:
      - name: ValidateCoreFirewallConfiguration
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "=== VALIDATING CORE FIREWALL CONFIGURATION ==="

                $validationErrors = @()

                # Check if firewall is enabled
                try {
                    $firewallProfiles = Get-NetFirewallProfile
                    foreach ($profile in $firewallProfiles) {
                        if ($profile.Enabled -eq $false) {
                            $validationErrors += "Windows Firewall is disabled for profile: $($profile.Name)"
                        }
                    }
                }
                catch {
                    $validationErrors += "Failed to check firewall profile status: $($_.Exception.Message)"
                }

                # Check if core custom rule groups exist
                $existingRules = Get-NetFirewallRule | Where-Object { $_.Enabled -eq "True" }

                $coreGroups = @(
                    "Windows Server Base Services",
                    "Windows Server Security Blocks", 
                    "Windows Server System Services"
                )

                foreach ($group in $coreGroups) {
                    $groupRules = $existingRules | Where-Object { $_.Group -eq $group }
                    if ($groupRules.Count -eq 0) {
                        $validationErrors += "No enabled rules found for core group: $group"
                    } else {
                        Write-Host "Found $($groupRules.Count) enabled rules in group: $group"
                    }
                }

                # Check key base service rules
                $requiredBaseRules = @("Allow RDP Inbound", "Allow WinRM HTTP", "WinRM-HTTPS")
                foreach ($rule in $requiredBaseRules) {
                    $foundRule = $existingRules | Where-Object { $_.DisplayName -eq $rule }
                    if (-not $foundRule) {
                        $validationErrors += "Missing base service rule: $rule"
                    }
                }

                # Check key security block rules
                $requiredSecurityRules = @("Block Telnet Public", "Block FTP Public", "Block SNMP Public", "Block SMB Public")
                foreach ($rule in $requiredSecurityRules) {
                    $foundRule = $existingRules | Where-Object { $_.DisplayName -eq $rule }
                    if (-not $foundRule) {
                        $validationErrors += "Missing security block rule: $rule"
                    }
                }

                # Report validation results
                if ($validationErrors.Count -eq 0) {
                    Write-Host "VALIDATION SUCCESS: Core firewall configuration completed successfully"
                    Write-Host "=== CORE FIREWALL GROUPS CONFIGURED ==="
                    Write-Host "Base Services: RDP, WinRM (internal networks only)"
                    Write-Host "Security Blocks: Attack prevention (public networks blocked)"
                    Write-Host "System Services: DHCP, RADIUS, Print services (internal networks only)"
                    Write-Host "=== SECURITY MODEL ==="
                    Write-Host "Domain/Private profiles: Allow essential internal services"
                    Write-Host "Public profile: Block insecure protocols and database ports"
                    Write-Host "Stealth mode enabled for public networks"
                    Write-Host "Local firewall rule creation disabled for security"
                    
                    $totalCoreRules = 0
                    foreach ($group in $coreGroups) {
                        $groupCount = ($existingRules | Where-Object { $_.Group -eq $group }).Count
                        $totalCoreRules += $groupCount
                    }
                    Write-Host "Total core firewall rules: $totalCoreRules"
                    exit 0
                } else {
                    Write-Error "VALIDATION FAILED: Core firewall configuration errors found:"
                    foreach ($error in $validationErrors) {
                        Write-Error "  - $error"
                    }
                    exit 1
                }
