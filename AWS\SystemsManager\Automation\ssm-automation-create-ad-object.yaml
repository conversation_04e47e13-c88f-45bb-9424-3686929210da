schemaVersion: '0.3'
description: |-
  This document will create an AD computer object for an EC2 Windows instance without joining it to the domain.
assumeRole: '{{AutomationAssumeRole}}'
parameters:
  AutomationAssumeRole:
    default: ''
    description: (Optional) The ARN of the role that allows Automation to perform the actions on your behalf.
    type: String
  InstanceId:
    description: (Required) The Instance running Windows Server.
    type: String
  ConfigS3Bucket:
    description: (Optional) S3 bucket containing the domain join configuration JSON files.
    type: String
    default: ''
  ClientPrefix:
    description: (Optional) Client prefix for config file selection (e.g., STM, SLM).
    type: String
    default: ''
  BusinessUnit:
    description: (Optional) Business Unit for config selection (e.g., SPF, SC).
    type: String
    default: ''
  Environment:
    description: (Optional) Environment for config selection (e.g., DEV, PPE, PRD).
    type: String
    default: ''
  AppType:
    description: (Optional) Application type for OU selection (e.g., Shared, MSSQL).
    type: String
    default: ''
  ComputerName:
    description: (Optional) Custom computer name for the AD object. If not provided, will use the EC2 instance's current computer name.
    type: String
    default: ''
mainSteps:
- name: assertInstanceIsWindows
  action: 'aws:assertAwsResourceProperty'
  description: ''
  inputs:
    Service: ec2
    PropertySelector: '$.Reservations[0].Instances[0].Platform'
    Api: DescribeInstances
    DesiredValues:
    - windows
    InstanceIds:
    - '{{InstanceId}}'
  timeoutSeconds: 10
  nextStep: createADObject
- name: createADObject
  action: aws:runCommand
  description: Execute PowerShell locally on EC2 instance to create the AD computer object.
  inputs:
    Parameters:
      commands: |-
        Try {
            $jsonSecretValue = (Get-SECSecretValue -SecretId arn:aws:secretsmanager:us-east-1:497488328043:secret:SecretKeyADPasswordResource-8c8mC8elYy7i-h8xzcK).SecretString | ConvertFrom-Json
            $domainName = $jsonSecretValue.domainName
            $domainJoinUserName = $jsonSecretValue.domainJoinUserName
            $domainJoinPassword = $jsonSecretValue.domainJoinPassword | ConvertTo-SecureString -AsPlainText -Force

            # Check if S3 config parameters are provided
            $configS3Bucket = "{{ConfigS3Bucket}}"
            $clientPrefix = "{{ClientPrefix}}"
            $businessUnit = "{{BusinessUnit}}"
            $environment = "{{Environment}}"
            $appType = "{{AppType}}"
            $customComputerName = "{{ComputerName}}"

            # Determine computer name to use
            if ($customComputerName -and $customComputerName -ne "") {
                $computerName = $customComputerName
                Write-Output "Using custom computer name: $computerName"
            } else {
                $computerName = $env:COMPUTERNAME
                Write-Output "Using current computer name: $computerName"
            }

            if ($configS3Bucket -and $clientPrefix -and $businessUnit -and $environment -and $configS3Bucket -ne "" -and $clientPrefix -ne "" -and $businessUnit -ne "" -and $environment -ne "") {
                # Construct S3 key based on client prefix: ClientPrefix_Config.json
                $configS3Key = "${clientPrefix}_Config.json"
                Write-Output "Reading target OU configuration from S3: s3://$configS3Bucket/$configS3Key"
                Try {
                    $s3ConfigContent = Read-S3Object -BucketName $configS3Bucket -Key $configS3Key | ConvertFrom-Json

                    # Navigate the new config structure: business_units -> BU -> environments -> ENV
                    if ($s3ConfigContent.business_units -and $s3ConfigContent.business_units.PSObject.Properties.Name -contains $businessUnit) {
                        $buConfig = $s3ConfigContent.business_units.$businessUnit

                        if ($buConfig.environments -and $buConfig.environments.PSObject.Properties.Name -contains $environment) {
                            $envConfig = $buConfig.environments.$environment

                            # Get domain information from environment config
                            if ($envConfig.domain) {
                                $domainName = $envConfig.domain
                                Write-Output "Using domain from S3 config: $domainName"
                            }

                            # Check if AppType is specified and exists in serverOUs
                            if ($appType -and $appType -ne "" -and $envConfig.serverOUs) {
                                # Get OS version from instance (you may need to adjust this logic)
                                $osVersion = "2022"  # Default, could be retrieved from instance metadata or tags
                                $ouKey = "${appType}-${osVersion}"
                                
                                if ($envConfig.serverOUs.PSObject.Properties.Name -contains $ouKey) {
                                    $targetOU = $envConfig.serverOUs.$ouKey
                                    Write-Output "Target OU from S3 config ($configS3Key) for BU '$businessUnit', Env '$environment', AppType '$appType', OS '$osVersion': $targetOU"
                                } else {
                                    # Try to find any OU for this AppType regardless of OS version
                                    $availableOUs = $envConfig.serverOUs.PSObject.Properties.Name | Where-Object { $_ -like "${appType}-*" }
                                    if ($availableOUs.Count -gt 0) {
                                        $targetOU = $envConfig.serverOUs.($availableOUs[0])
                                        Write-Output "Using first available OU for AppType '$appType': $targetOU"
                                    } else {
                                        throw "No OU found for AppType '$appType' in environment config"
                                    }
                                }
                            } elseif ($envConfig.basePath) {
                                # Fall back to basePath if AppType not found or not specified
                                $targetOU = $envConfig.basePath
                                Write-Output "Target OU from S3 config ($configS3Key) using basePath for BU '$businessUnit', Env '$environment' (AppType '$appType' not found or not specified): $targetOU"
                            } else {
                                throw "No valid target OU found in environment config for BU '$businessUnit', Env '$environment'"
                            }
                        } else {
                            throw "Environment '$environment' not found in business unit '$businessUnit' config"
                        }
                    } else {
                        throw "Business unit '$businessUnit' not found in S3 config"
                    }

                    if (-not $targetOU) {
                        throw "No valid target OU found in S3 config"
                    }
                } Catch [System.Exception] {
                    Write-Output "Failed to read or parse S3 config $configS3Key, falling back to default from Secrets Manager: $_"
                    $targetOU = $jsonSecretValue.defaultTargetOU
                }
            } else {
                Write-Output "S3 config parameters not fully specified (Bucket: '$configS3Bucket', Client: '$clientPrefix', BU: '$businessUnit', Env: '$environment'), using default target OU from Secrets Manager"
                $targetOU = $jsonSecretValue.defaultTargetOU
            }
        } Catch [System.Exception] {
            Write-Output "Failed to get SSM Parameter(s) $_"
            Exit 1
        }

        $domainCredential = New-Object System.Management.Automation.PSCredential($domainJoinUserName, $domainJoinPassword)

        # Install RSAT AD Tools if not already installed
        If (-not (Get-WindowsFeature -Name 'RSAT-AD-Tools' -ErrorAction SilentlyContinue | Select-Object -ExpandProperty 'Installed')) {
            Write-Output 'Installing RSAT AD Tools to allow AD computer object creation'
            Try {
                $Null = Add-WindowsFeature -Name 'RSAT-AD-Tools' -ErrorAction Stop
            } Catch [System.Exception] {
                Write-Output "Failed to install RSAT AD Tools $_"
                Exit 1
            }    
        }

        Try {
            # Check if computer object already exists
            $existingComputer = Get-ADComputer -Identity $computerName -Server $domainName -Credential $domainCredential -ErrorAction SilentlyContinue
            
            if ($existingComputer) {
                Write-Output "Computer object '$computerName' already exists in AD at: $($existingComputer.DistinguishedName)"
                
                # Check if it's in the correct OU
                if ($existingComputer.DistinguishedName -like "*$targetOU") {
                    Write-Output "Computer object is already in the correct OU: $targetOU"
                } else {
                    Write-Output "Moving computer object from current location to target OU: $targetOU"
                    Move-ADObject -Identity $existingComputer.DistinguishedName -TargetPath $targetOU -Server $domainName -Credential $domainCredential -ErrorAction Stop
                    Write-Output "Successfully moved computer object to target OU"
                }
            } else {
                Write-Output "Creating new computer object '$computerName' in OU: $targetOU"
                New-ADComputer -Name $computerName -Path $targetOU -Server $domainName -Credential $domainCredential -Description "Created by SSM Automation for EC2 Instance {{InstanceId}}" -ErrorAction Stop
                Write-Output "Successfully created computer object '$computerName' in AD"
            }
        } Catch [System.Exception] {
            Write-Output "Failed to create or manage AD computer object: $_"
            Exit 1
        }
    InstanceIds:
    - '{{InstanceId}}'
    DocumentName: AWS-RunPowerShellScript
  timeoutSeconds: 600
  nextStep: createADObjectEC2Tag
  isEnd: false
  onFailure: step:failADObjectEC2Tag
- name: createADObjectEC2Tag
  action: aws:createTags
  description: Add the ADObjectCreated EC2 tag to reflect AD object creation.
  inputs:
    ResourceIds:
    - '{{InstanceId}}'
    ResourceType: EC2
    Tags:
    - Value: Create-complete
      Key: ADObjectCreated
  isEnd: true
- name: failADObjectEC2Tag
  action: aws:createTags
  description: Update the ADObjectCreated EC2 tag to reflect a failure in the AD object creation process.
  inputs:
    ResourceIds:
    - '{{InstanceId}}'
    ResourceType: EC2
    Tags:
    - Value: Failed
      Key: ADObjectCreated
  timeoutSeconds: 30
  isEnd: true
