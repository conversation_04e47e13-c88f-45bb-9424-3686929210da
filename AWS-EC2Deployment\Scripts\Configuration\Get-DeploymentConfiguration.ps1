<#
    .NOTES
    File Name: Get-DeploymentConfiguration.ps1
    Author: <PERSON><PERSON>
    Version: 1.0.0 - 03/09/2025
    Description: Main configuration loader for AWS EC2 Deployment Automation
    Note: This script loads and validates configurations from S3 buckets for EC2 deployment

    .NOTES
    Version 1.0.0     - Base Script
#>

param(
    [Parameter(Mandatory=$true)]
    [string]$AssetOwner,
    
    [Parameter(Mandatory=$true)]
    [string]$AppType,
    
    [Parameter(Mandatory=$true)]
    [string]$Client,
    
    [Parameter(Mandatory=$true)]
    [string]$Environment,
    
    [Parameter(Mandatory=$true)]
    [string]$OSVersion,
    
    [Parameter(Mandatory=$true)]
    [string]$S3BucketName,
    
    [Parameter(Mandatory=$false)]
    [string]$S3ConfigPrefix = "configs",
    
    [Parameter(Mandatory=$false)]
    [string]$Region = "af-south-1",
    
    [Parameter(Mandatory=$false)]
    [switch]$ValidateOnly = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$OutputJson = $false
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Function to write structured log messages
function Write-LogMessage {
    param(
        [string]$Message,
        [ValidateSet("INFO", "WARN", "ERROR", "SUCCESS")]
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    switch ($Level) {
        "INFO" { Write-Host $logMessage -ForegroundColor Cyan }
        "WARN" { Write-Host $logMessage -ForegroundColor Yellow }
        "ERROR" { Write-Host $logMessage -ForegroundColor Red }
        "SUCCESS" { Write-Host $logMessage -ForegroundColor Green }
    }
}

# Function to download configuration from S3
function Get-S3Configuration {
    param(
        [string]$BucketName,
        [string]$Key,
        [string]$Region
    )
    
    try {
        Write-LogMessage "Downloading configuration from s3://$BucketName/$Key" "INFO"
        
        # Create temporary file
        $tempFile = [System.IO.Path]::GetTempFileName()
        
        # Download from S3 using AWS CLI
        $downloadResult = aws s3 cp "s3://$BucketName/$Key" $tempFile --region $Region 2>&1
        
        if ($LASTEXITCODE -ne 0) {
            throw "Failed to download from S3: $downloadResult"
        }
        
        # Read and parse JSON
        $content = Get-Content $tempFile -Raw | ConvertFrom-Json
        
        # Clean up temp file
        Remove-Item $tempFile -Force -ErrorAction SilentlyContinue
        
        Write-LogMessage "Successfully downloaded and parsed configuration" "SUCCESS"
        return $content
        
    } catch {
        Write-LogMessage "Failed to download configuration from S3: $($_.Exception.Message)" "ERROR"
        throw
    }
}

# Function to validate base configuration
function Test-BaseConfiguration {
    param(
        [object]$BaseConfig,
        [string]$AssetOwner,
        [string]$AppType,
        [string]$Client,
        [string]$Environment,
        [string]$OSVersion
    )
    
    try {
        Write-LogMessage "Validating base configuration parameters" "INFO"
        
        # Validate Asset Owner
        if ($AssetOwner -notin $BaseConfig.ASSET_OWNER) {
            throw "Invalid Asset Owner: '$AssetOwner'. Valid options: $($BaseConfig.ASSET_OWNER -join ', ')"
        }
        
        # Get asset-specific configuration
        $assetConfig = $BaseConfig.$AssetOwner
        if (-not $assetConfig) {
            throw "Asset Owner '$AssetOwner' configuration not found in base config"
        }
        
        # Validate App Type
        if ($AppType -notin $assetConfig.APP_TYPE) {
            throw "Invalid App Type: '$AppType' for $AssetOwner. Valid options: $($assetConfig.APP_TYPE -join ', ')"
        }
        
        # Validate Environment
        if ($Environment -notin $assetConfig.ENV) {
            throw "Invalid Environment: '$Environment' for $AssetOwner. Valid options: $($assetConfig.ENV -join ', ')"
        }
        
        # Validate OS Version
        if ($OSVersion -notin $assetConfig.OS_VERSION) {
            throw "Invalid OS Version: '$OSVersion' for $AssetOwner. Valid options: $($assetConfig.OS_VERSION -join ', ')"
        }
        
        # Validate Client
        if ($Client -notin $assetConfig.CLIENT) {
            throw "Invalid Client: '$Client' for $AssetOwner. Valid options: $($assetConfig.CLIENT -join ', ')"
        }
        
        Write-LogMessage "Base configuration validation successful" "SUCCESS"
        return $true
        
    } catch {
        Write-LogMessage "Base configuration validation failed: $($_.Exception.Message)" "ERROR"
        throw
    }
}

# Function to extract specific configuration
function Get-SpecificConfiguration {
    param(
        [object]$SpecificConfig,
        [string]$Client,
        [string]$Environment,
        [string]$OSVersion
    )
    
    try {
        Write-LogMessage "Extracting specific configuration for Client: $Client, Environment: $Environment, OS: $OSVersion" "INFO"
        
        # Get client configuration
        $clientConfig = $SpecificConfig.$Client
        if (-not $clientConfig) {
            throw "Client '$Client' not found in specific configuration"
        }
        
        # Get environment configuration
        $envConfig = $clientConfig.$Environment
        if (-not $envConfig) {
            throw "Environment '$Environment' not found for client '$Client'"
        }
        
        # Get OS-specific configuration
        $osConfig = $envConfig.OS_VERSIONS.$OSVersion
        if (-not $osConfig) {
            throw "OS Version '$OSVersion' not found for client '$Client' in environment '$Environment'"
        }
        
        Write-LogMessage "Successfully extracted specific configuration" "SUCCESS"
        
        return @{
            Domain = $envConfig.DOMAIN
            BasePath = $envConfig.BASE_PATH
            LocalAdmins = $envConfig.LOCAL_ADM
            TargetOU = $osConfig.OU
            EnvironmentConfig = $envConfig
            OSConfig = $osConfig
        }
        
    } catch {
        Write-LogMessage "Failed to extract specific configuration: $($_.Exception.Message)" "ERROR"
        throw
    }
}

# Main execution logic
try {
    Write-LogMessage "Starting EC2 Deployment Configuration Loading" "INFO"
    Write-LogMessage "Parameters: AssetOwner=$AssetOwner, AppType=$AppType, Client=$Client, Environment=$Environment, OSVersion=$OSVersion" "INFO"

    # Step 1: Load base configuration
    $baseConfigKey = "$S3ConfigPrefix/base_config.json"
    $baseConfig = Get-S3Configuration -BucketName $S3BucketName -Key $baseConfigKey -Region $Region

    # Step 2: Validate inputs against base configuration
    Test-BaseConfiguration -BaseConfig $baseConfig -AssetOwner $AssetOwner -AppType $AppType -Client $Client -Environment $Environment -OSVersion $OSVersion

    # Step 3: Load specific configuration
    $specificConfigKey = "$S3ConfigPrefix/${AssetOwner}_${AppType}_Config.json"
    $specificConfig = Get-S3Configuration -BucketName $S3BucketName -Key $specificConfigKey -Region $Region

    # Step 4: Extract the required configuration
    $extractedConfig = Get-SpecificConfiguration -SpecificConfig $specificConfig -Client $Client -Environment $Environment -OSVersion $OSVersion

    # Step 5: Build final configuration object
    $finalConfig = @{
        # Input parameters
        AssetOwner = $AssetOwner
        AppType = $AppType
        Client = $Client
        Environment = $Environment
        OSVersion = $OSVersion

        # Extracted configuration
        Domain = $extractedConfig.Domain
        BasePath = $extractedConfig.BasePath
        LocalAdmins = $extractedConfig.LocalAdmins
        TargetOU = $extractedConfig.TargetOU

        # Additional metadata
        S3Bucket = $S3BucketName
        S3ConfigPrefix = $S3ConfigPrefix
        Region = $Region
        LoadedAt = (Get-Date -Format "yyyy-MM-dd HH:mm:ss UTC")

        # Raw configurations for advanced use cases
        BaseConfig = $baseConfig
        SpecificConfig = $specificConfig
        ExtractedConfig = $extractedConfig
    }

    if ($ValidateOnly) {
        Write-LogMessage "Validation completed successfully - no errors found" "SUCCESS"
        return $true
    }

    # Output results
    if ($OutputJson) {
        $jsonOutput = $finalConfig | ConvertTo-Json -Depth 10
        Write-Output $jsonOutput
    } else {
        Write-LogMessage "Configuration loaded successfully:" "SUCCESS"
        Write-LogMessage "  Asset Owner: $($finalConfig.AssetOwner)" "INFO"
        Write-LogMessage "  App Type: $($finalConfig.AppType)" "INFO"
        Write-LogMessage "  Client: $($finalConfig.Client)" "INFO"
        Write-LogMessage "  Environment: $($finalConfig.Environment)" "INFO"
        Write-LogMessage "  OS Version: $($finalConfig.OSVersion)" "INFO"
        Write-LogMessage "  Domain: $($finalConfig.Domain)" "INFO"
        Write-LogMessage "  Target OU: $($finalConfig.TargetOU)" "INFO"
        Write-LogMessage "  Local Admins: $($finalConfig.LocalAdmins -join ', ')" "INFO"
    }

    # Return the configuration for use in runbook
    return $finalConfig

} catch {
    Write-LogMessage "Configuration loading failed: $($_.Exception.Message)" "ERROR"

    # Output error details for SSM
    $errorDetails = @{
        Error = $true
        Message = $_.Exception.Message
        ScriptName = $MyInvocation.MyCommand.Name
        LineNumber = $_.InvocationInfo.ScriptLineNumber
        Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss UTC"
    }

    if ($OutputJson) {
        Write-Output ($errorDetails | ConvertTo-Json -Depth 5)
    }

    exit 1
}
