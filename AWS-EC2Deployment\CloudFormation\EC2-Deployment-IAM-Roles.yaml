AWSTemplateFormatVersion: '2010-09-09'
Description: 'IAM Roles and Policies for EC2 Deployment Automation'

Parameters:
  S3ConfigBucketName:
    Type: String
    Description: Name of the S3 bucket containing configuration files
  SecretsManagerSecretArn:
    Type: String
    Description: ARN of the Secrets Manager secret containing domain credentials
  Region:
    Type: String
    Default: af-south-1
    Description: AWS Region for the deployment

Resources:
  # IAM Role for SSM Automation
  EC2DeploymentAutomationRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Sub 'EC2-Deployment-Automation-Role-${AWS::Region}'
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - ssm.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AmazonSSMAutomationRole
      Policies:
        - PolicyName: EC2DeploymentAutomationPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              # EC2 permissions
              - Effect: Allow
                Action:
                  - ec2:RunInstances
                  - ec2:DescribeInstances
                  - ec2:DescribeImages
                  - ec2:DescribeSnapshots
                  - ec2:DescribeVolumes
                  - ec2:CreateTags
                  - ec2:DescribeTags
                  - ec2:DescribeInstanceAttribute
                  - ec2:ModifyInstanceAttribute
                  - ec2:DescribeInstanceStatus
                  - ec2:DescribeSecurityGroups
                  - ec2:DescribeSubnets
                  - ec2:DescribeVpcs
                  - ec2:DescribeKeyPairs
                Resource: '*'
              
              # ImageBuilder permissions
              - Effect: Allow
                Action:
                  - imagebuilder:GetImage
                  - imagebuilder:ListImages
                  - imagebuilder:GetImageRecipe
                  - imagebuilder:ListImageRecipes
                Resource: '*'
              
              # S3 permissions for configuration files
              - Effect: Allow
                Action:
                  - s3:GetObject
                  - s3:ListBucket
                Resource:
                  - !Sub 'arn:aws:s3:::${S3ConfigBucketName}'
                  - !Sub 'arn:aws:s3:::${S3ConfigBucketName}/*'
              
              # Secrets Manager permissions
              - Effect: Allow
                Action:
                  - secretsmanager:GetSecretValue
                Resource: !Ref SecretsManagerSecretArn
              
              # SSM permissions
              - Effect: Allow
                Action:
                  - ssm:SendCommand
                  - ssm:ListCommands
                  - ssm:ListCommandInvocations
                  - ssm:DescribeInstanceInformation
                  - ssm:GetCommandInvocation
                  - ssm:GetParameter
                  - ssm:GetParameters
                  - ssm:PutParameter
                Resource: '*'
              
              # IAM permissions for instance profiles
              - Effect: Allow
                Action:
                  - iam:PassRole
                Resource: !GetAtt EC2InstanceRole.Arn

  # IAM Role for EC2 Instances
  EC2InstanceRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Sub 'EC2-Deployment-Instance-Role-${AWS::Region}'
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - ec2.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore
        - arn:aws:iam::aws:policy/CloudWatchAgentServerPolicy
      Policies:
        - PolicyName: EC2DeploymentInstancePolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              # S3 permissions for downloading scripts and applications
              - Effect: Allow
                Action:
                  - s3:GetObject
                  - s3:ListBucket
                Resource:
                  - !Sub 'arn:aws:s3:::${S3ConfigBucketName}'
                  - !Sub 'arn:aws:s3:::${S3ConfigBucketName}/*'
              
              # Secrets Manager permissions for domain join
              - Effect: Allow
                Action:
                  - secretsmanager:GetSecretValue
                Resource: !Ref SecretsManagerSecretArn
              
              # EC2 permissions for self-management
              - Effect: Allow
                Action:
                  - ec2:DescribeInstances
                  - ec2:DescribeTags
                  - ec2:CreateTags
                Resource: '*'
              
              # CloudWatch Logs permissions
              - Effect: Allow
                Action:
                  - logs:CreateLogGroup
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:DescribeLogStreams
                Resource: '*'

  # Instance Profile for EC2 Instances
  EC2InstanceProfile:
    Type: AWS::IAM::InstanceProfile
    Properties:
      InstanceProfileName: !Sub 'EC2-Deployment-Instance-Profile-${AWS::Region}'
      Roles:
        - !Ref EC2InstanceRole

  # IAM Role for Lambda functions (if needed for advanced automation)
  EC2DeploymentLambdaRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Sub 'EC2-Deployment-Lambda-Role-${AWS::Region}'
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole
      Policies:
        - PolicyName: EC2DeploymentLambdaPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              # EC2 permissions
              - Effect: Allow
                Action:
                  - ec2:DescribeInstances
                  - ec2:DescribeTags
                  - ec2:CreateTags
                  - ec2:StartInstances
                  - ec2:StopInstances
                  - ec2:RebootInstances
                Resource: '*'
              
              # SSM permissions
              - Effect: Allow
                Action:
                  - ssm:StartAutomationExecution
                  - ssm:GetAutomationExecution
                  - ssm:DescribeAutomationExecutions
                  - ssm:SendCommand
                Resource: '*'
              
              # S3 permissions
              - Effect: Allow
                Action:
                  - s3:GetObject
                  - s3:ListBucket
                Resource:
                  - !Sub 'arn:aws:s3:::${S3ConfigBucketName}'
                  - !Sub 'arn:aws:s3:::${S3ConfigBucketName}/*'

Outputs:
  AutomationRoleArn:
    Description: ARN of the SSM Automation Role
    Value: !GetAtt EC2DeploymentAutomationRole.Arn
    Export:
      Name: !Sub '${AWS::StackName}-AutomationRoleArn'

  InstanceRoleArn:
    Description: ARN of the EC2 Instance Role
    Value: !GetAtt EC2InstanceRole.Arn
    Export:
      Name: !Sub '${AWS::StackName}-InstanceRoleArn'

  InstanceProfileArn:
    Description: ARN of the EC2 Instance Profile
    Value: !GetAtt EC2InstanceProfile.Arn
    Export:
      Name: !Sub '${AWS::StackName}-InstanceProfileArn'

  InstanceProfileName:
    Description: Name of the EC2 Instance Profile
    Value: !Ref EC2InstanceProfile
    Export:
      Name: !Sub '${AWS::StackName}-InstanceProfileName'

  LambdaRoleArn:
    Description: ARN of the Lambda Role
    Value: !GetAtt EC2DeploymentLambdaRole.Arn
    Export:
      Name: !Sub '${AWS::StackName}-LambdaRoleArn'
