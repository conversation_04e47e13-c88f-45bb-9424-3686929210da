AWSTemplateFormatVersion: '2010-09-09'
Description: Lambda function that will domain join or unjoin EC2 instances based on EC2 tag values.
Parameters:
  SSMAutomationRunbookNameParameter:
    Type: String
    Description: Enter the AWS Systems Manager Automation runbook name used to perform the AD domain join or domain unjoin.
Metadata:
  AWS::CloudFormation::Interface:
    ParameterGroups:
    - Label:
        default: AWS Systems Manager Autmation
      Parameters:
      - SSMAutomationRunbookNameParameter
    ParameterLabels:
      SSMAutomationRunbookNameParameter:
        default: Automation runbook name.
Resources:
  IAMRoleLambdaFunctionResource:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
        - Effect: Allow
          Principal:
            Service: lambda.amazonaws.com
          Action: sts:AssumeRole
      Path: /
      Policies:
      - PolicyName: !Sub 'LambdaBasicExecutionPolicy-${AWS::StackName}'
        PolicyDocument:
          Version: '2012-10-17'
          Statement:
          - Effect: Allow
            Action:
            - logs:CreateLogGroup
            - logs:CreateLogStream
            - logs:PutLogEvents
            Resource: '*'
      - PolicyName: !Sub 'EC2SSMPermissions-${AWS::StackName}'
        PolicyDocument:
          Version: '2012-10-17'
          Statement:
          - Effect: Allow
            Action:
            - ec2:StartInstances
            - ec2:StopInstances
            - ec2:DescribeInstanceStatus
            - ec2:CreateTags
            - ec2:DescribeTags
            - ec2:RebootInstances
            - ec2:DescribeInstances
            - ssm:StartAutomationExecution
            - ssm:DescribeInstanceInformation
            - ssm:SendCommand
            - ssm:ListCommands
            - ssm:ListCommandInvocations
            Resource:
            - '*'
            - !Sub 'arn:${AWS::Partition}:ssm:${AWS::Region}:${AWS::AccountId}:automation-definition/${SSMAutomationRunbookNameParameter}:$DEFAULT'
  ADJoinUnjoinLambdaFunctionResource:
    Type: AWS::Lambda::Function
    Properties:
      Runtime: python3.12
      MemorySize: 128
      Timeout: 15
      Role: !GetAtt IAMRoleLambdaFunctionResource.Arn
      Handler: index.lambda_handler
      Code:
        ZipFile: !Sub |
          import boto3
          import os
          import re

          def lambda_handler(event, context):
              region = os.environ.get('AWS_REGION')
              resources = event['resources']
              instance_id = resources[0]
              instance_id_match = re.search('i-[a-f0-9]{8}(?:[a-f0-9]{9})?$',instance_id)
              instance_id = instance_id_match.group(0)
              automation_runbook_arn = 'arn:${AWS::Partition}:ssm:${AWS::Region}:${AWS::AccountId}:automation-definition/${SSMAutomationRunbookNameParameter}'
              ec2_client = boto3.client('ec2',region)
              ssm_client = boto3.client('ssm',region)
              instance_state_code = ec2_client.describe_instances(InstanceIds=[instance_id])['Reservations'][0]['Instances'][0]['State']['Code']
              tags_output = ec2_client.describe_tags(Filters=[{'Name': 'resource-id','Values': [instance_id]}])['Tags']

              for tag_output in tags_output:
                  tag_output_key = tag_output['Key']
                  tag_output_value = tag_output['Value']
                  if tag_output_key == "StartEvent":
                      if tag_output_value == "Join" or tag_output_value == "Unjoin":
                          if instance_state_code == 80:
                              ec2_client.start_instances(InstanceIds=[instance_id])
                          instance_status = ec2_client.describe_instance_status(InstanceIds=[instance_id,],)['InstanceStatuses'][0]['InstanceStatus']['Details'][0]['Status']
                          while instance_status != 'passed':
                              waiter_running = ec2_client.get_waiter('instance_running')
                              waiter_status_ok = ec2_client.get_waiter('instance_status_ok')
                              waiter_running.wait(InstanceIds=[instance_id])
                              waiter_status_ok.wait(InstanceIds=[instance_id])
                          ssm_response = ssm_client.start_automation_execution(DocumentName=automation_runbook_arn,DocumentVersion="$DEFAULT",Parameters={"InstanceId":[instance_id],"DomainJoinActivity":[tag_output_value]})
                          print(ssm_response)
                          break
                      else:
                          print("The tag value for " + tag_output_key + " is not valid to perform the domain join/unjoin automation. Value must be Join or Unjoin.")
                      break
      Description: 'Python based Lambda function that will trigger AD domain join or unjoin EC2 instances based on EC2 tag values for key StartEvent.'
      TracingConfig:
        Mode: Active
  LambdaFunctionPolicyUpdate:
    Type: AWS::IAM::RolePolicy
    Properties:
      PolicyName: !Sub 'InvokeFunctionPermissions-${AWS::StackName}'
      PolicyDocument:
        Version: '2012-10-17'
        Statement:
        - Effect: Allow
          Action: lambda:InvokeFunction
          Resource:
          - !GetAtt ADJoinUnjoinLambdaFunctionResource.Arn
      RoleName: !Ref IAMRoleLambdaFunctionResource
  ADJoinUnjoinTagEVBRuleResource:
    Type: AWS::Events::Rule
    Properties:
      Description: Rule that will join or unjoin EC2 Windows instances to an AD domain with tags.
      EventPattern:
        source:
        - aws.tag
        detail-type:
        - Tag Change on Resource
        detail:
          changed-tag-keys:
          - StartEvent
          service:
          - ec2
          resource-type:
          - instance
      State: ENABLED
      EventBusName: default
      Targets:
      - Id: 'TargetLambdaFunction-ADJoinUnjoin'
        Arn: !GetAtt ADJoinUnjoinLambdaFunctionResource.Arn
  PermissionForEventsToInvokeLambdaResource:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref ADJoinUnjoinLambdaFunctionResource
      Action: lambda:InvokeFunction
      Principal: events.amazonaws.com
      SourceArn: !GetAtt ADJoinUnjoinTagEVBRuleResource.Arn
