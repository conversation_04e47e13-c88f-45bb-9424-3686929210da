# AWS Image Builder Component: Install .NET Framework 4.8
# This component installs .NET Framework 4.8 on Windows Server 2022

name: win-server-dotnet48
description: Install Microsoft .NET Framework 4.8 on Windows Server
schemaVersion: 1.0
phases:
- name: build
  steps:
  - name: CheckExistingDotNet
    action: ExecutePowerShell
    onFailure: Continue
    inputs:
      commands:
      - |
        Write-Host "Checking existing .NET Framework versions..."
        $dotNetVersions = Get-ChildItem "HKLM:SOFTWARE\Microsoft\NET Framework Setup\NDP" -Recurse |
          Get-ItemProperty -Name Version -EA 0 |
          Where-Object { $_.PSChildName -match '^(?!S)\p{L}' } |
          Select-Object PSChildName, Version

        Write-Host "Installed .NET Framework versions:"
        $dotNetVersions | Format-Table -AutoSize

        # Check if .NET 4.8 is already installed
        $net48Installed = Get-ItemProperty "HKLM:SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full\" -Name Release -ErrorAction SilentlyContinue
        if ($net48Installed -and $net48Installed.Release -ge 528040) {
            Write-Host ".NET Framework 4.8 or later is already installed (Release: $($net48Installed.Release))"
            exit 0
        } else {
            Write-Host ".NET Framework 4.8 is not installed. Proceeding with installation..."
        }

  - name: DownloadDotNet48
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        # Official Microsoft .NET Framework 4.8 web installer URL
        $downloadUrl = "https://go.microsoft.com/fwlink/?linkid=2088631"
        $downloadPath = "C:\temp\ServerInstalls\ndp48-web.exe"

        # Create temp directory if it doesn't exist
        if (!(Test-Path "C:\temp\ServerInstalls")) {
            New-Item -ItemType Directory -Path "C:\temp\ServerInstalls" -Force
        }

        Write-Host "Downloading .NET Framework 4.8 installer..."
        try {
            # Add timeout and better error handling
            Invoke-WebRequest -Uri $downloadUrl -OutFile $downloadPath -UseBasicParsing -TimeoutSec 300
            Write-Host "Download completed successfully"

            # Verify file was downloaded and has reasonable size
            if ((Test-Path $downloadPath) -and ((Get-Item $downloadPath).Length -gt 1MB)) {
                Write-Host "Download verification successful"
            } else {
                throw "Downloaded file is missing or too small"
            }
        } catch {
            Write-Error "Failed to download .NET Framework 4.8: $($_.Exception.Message)"
            Write-Host "Attempting alternative download method..."

            # Try alternative URL (offline installer) as fallback
            try {
                $offlineUrl = "https://go.microsoft.com/fwlink/?linkid=2088517"
                $downloadPath = "C:\temp\ServerInstalls\ndp48-offline.exe"
                Write-Host "Downloading offline installer as fallback..."
                Invoke-WebRequest -Uri $offlineUrl -OutFile $downloadPath -UseBasicParsing -TimeoutSec 600
                Write-Host "Offline installer download completed"
            } catch {
                Write-Error "Both download attempts failed: $($_.Exception.Message)"
                exit 1
            }
        }

  - name: InstallDotNet48
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        # Check for both possible installer paths
        $webInstallerPath = "C:\temp\ServerInstalls\ndp48-web.exe"
        $offlineInstallerPath = "C:\temp\ServerInstalls\ndp48-offline.exe"

        if (Test-Path $webInstallerPath) {
            $installerPath = $webInstallerPath
            Write-Host "Using web installer"
        } elseif (Test-Path $offlineInstallerPath) {
            $installerPath = $offlineInstallerPath
            Write-Host "Using offline installer"
        } else {
            Write-Error "No installer found at expected paths"
            exit 1
        }

        Write-Host "Installing .NET Framework 4.8..."
        Write-Host "This may take several minutes and require a reboot..."

        # Install .NET Framework 4.8 silently
        $process = Start-Process -FilePath $installerPath -ArgumentList "/quiet", "/norestart" -Wait -PassThru

        Write-Host "Installation process completed with exit code: $($process.ExitCode)"

        # Exit codes for .NET Framework installer:
        # 0 = Success
        # 1641 = Success, reboot initiated
        # 3010 = Success, reboot required
        # Other = Error

        switch ($process.ExitCode) {
            0 { 
                Write-Host ".NET Framework 4.8 installed successfully"
            }
            1641 { 
                Write-Host ".NET Framework 4.8 installed successfully (reboot initiated)"
            }
            3010 { 
                Write-Host ".NET Framework 4.8 installed successfully (reboot required)"
            }
            default { 
                Write-Error "Installation failed with exit code: $($process.ExitCode)"
                exit 1
            }
        }

  - name: VerifyInstallation
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Verifying .NET Framework 4.8 installation..."

        # Check registry for .NET 4.8
        $net48Check = Get-ItemProperty "HKLM:SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full\" -Name Release -ErrorAction SilentlyContinue

        if ($net48Check -and $net48Check.Release -ge 528040) {
            Write-Host "SUCCESS: .NET Framework 4.8 is installed (Release: $($net48Check.Release))"
            
            # Display all installed .NET versions for verification
            Write-Host "`nAll installed .NET Framework versions:"
            Get-ChildItem "HKLM:SOFTWARE\Microsoft\NET Framework Setup\NDP" -Recurse |
              Get-ItemProperty -Name Version -EA 0 |
              Where-Object { $_.PSChildName -match '^(?!S)\p{L}' } |
              Select-Object PSChildName, Version |
              Format-Table -AutoSize
        } else {
            Write-Error "FAILED: .NET Framework 4.8 installation could not be verified"
            exit 1
        }

  - name: Cleanup
    action: ExecutePowerShell
    onFailure: Continue
    inputs:
      commands:
      - |
        Write-Host "Cleaning up installation files..."

        # Clean up both possible installer files
        if (Test-Path "C:\temp\ServerInstalls\ndp48-web.exe") {
            Remove-Item "C:\temp\ServerInstalls\ndp48-web.exe" -Force
            Write-Host "Web installer file removed"
        }

        if (Test-Path "C:\temp\ServerInstalls\ndp48-offline.exe") {
            Remove-Item "C:\temp\ServerInstalls\ndp48-offline.exe" -Force
            Write-Host "Offline installer file removed"
        }

- name: validate
  steps:
  - name: ValidateDotNet48
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Final validation of .NET Framework 4.8..."

        $net48Check = Get-ItemProperty "HKLM:SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full\" -Name Release -ErrorAction SilentlyContinue

        if ($net48Check -and $net48Check.Release -ge 528040) {
            Write-Host "VALIDATION SUCCESS: .NET Framework 4.8 is properly installed"
            exit 0
        } else {
            Write-Error "VALIDATION FAILED: .NET Framework 4.8 is not installed"
            exit 1
        }
