#Requires -RunAsAdministrator

<#
.SYNOPSIS
    Configure Windows Registry Settings for Sanlam Shared PRD Environment

.DESCRIPTION
    This script configures Windows Registry settings based on the Sanlam_Shared_PRD_customizations.json configuration.
    It applies registry settings for Windows Update, Remote Desktop, UAC, Event Logs, and custom Sanlam settings.

.PARAMETER ConfigPath
    Path to the JSON configuration file. Defaults to the standard customizations file.

.PARAMETER LogPath
    Path for log file output. Defaults to C:\Logs\Configure-RegistrySettings.log

.PARAMETER DeploymentDate
    Override deployment date. If not specified, current date/time will be used.

.EXAMPLE
    .\Configure-RegistrySettings.ps1
    
.EXAMPLE
    .\Configure-RegistrySettings.ps1 -ConfigPath "C:\Config\custom.json" -LogPath "C:\Logs\registry.log"

.EXAMPLE
    .\Configure-RegistrySettings.ps1 -DeploymentDate "2024-01-15 10:30:00"

.NOTES
    Author: AWS EC2 Deployment Automation
    Version: 1.0
    Requires: PowerShell 5.1 or higher, Administrator privileges
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory = $false)]
    [string]$ConfigPath = "C:\Coding\AWS-EC2Deployment\customizations\Sanlam_Shared_PRD_customizations.json",
    
    [Parameter(Mandatory = $false)]
    [string]$LogPath = "C:\Logs\Configure-RegistrySettings.log",
    
    [Parameter(Mandatory = $false)]
    [string]$DeploymentDate = (Get-Date -Format "yyyy-MM-dd HH:mm:ss")
)

# Initialize logging
$LogDir = Split-Path -Path $LogPath -Parent
if (-not (Test-Path -Path $LogDir)) {
    New-Item -Path $LogDir -ItemType Directory -Force | Out-Null
}

function Write-Log {
    param(
        [Parameter(Mandatory = $true)]
        [string]$Message,
        
        [Parameter(Mandatory = $false)]
        [ValidateSet("INFO", "WARN", "ERROR", "SUCCESS")]
        [string]$Level = "INFO"
    )
    
    $Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $LogMessage = "[$Timestamp] [$Level] $Message"
    
    # Write to console with color coding
    switch ($Level) {
        "INFO"    { Write-Host $LogMessage -ForegroundColor White }
        "WARN"    { Write-Host $LogMessage -ForegroundColor Yellow }
        "ERROR"   { Write-Host $LogMessage -ForegroundColor Red }
        "SUCCESS" { Write-Host $LogMessage -ForegroundColor Green }
    }
    
    # Write to log file
    Add-Content -Path $LogPath -Value $LogMessage
}

function Test-RegistryPath {
    param(
        [Parameter(Mandatory = $true)]
        [string]$Path
    )
    
    try {
        return Test-Path -Path $Path
    }
    catch {
        return $false
    }
}

function New-RegistryPath {
    param(
        [Parameter(Mandatory = $true)]
        [string]$Path
    )
    
    try {
        if (-not (Test-RegistryPath -Path $Path)) {
            Write-Log "Creating registry path: $Path" -Level "INFO"
            New-Item -Path $Path -Force | Out-Null
            return $true
        }
        return $true
    }
    catch {
        Write-Log "Failed to create registry path '$Path': $($_.Exception.Message)" -Level "ERROR"
        return $false
    }
}

function Set-RegistryValue {
    param(
        [Parameter(Mandatory = $true)]
        [PSCustomObject]$Setting,
        
        [Parameter(Mandatory = $false)]
        [string]$DeploymentDate
    )
    
    try {
        # Process value replacements
        $processedValue = $Setting.Value
        if ($Setting.Value -eq "{{DEPLOYMENT_DATE}}" -and $DeploymentDate) {
            $processedValue = $DeploymentDate
        }
        
        # Ensure the registry path exists
        if (-not (New-RegistryPath -Path $Setting.Path)) {
            return $false
        }
        
        # Get current value for comparison
        $currentValue = $null
        try {
            $currentValue = Get-ItemProperty -Path $Setting.Path -Name $Setting.Name -ErrorAction SilentlyContinue | Select-Object -ExpandProperty $Setting.Name
        }
        catch {
            # Value doesn't exist, which is fine
        }
        
        # Set the registry value based on type
        switch ($Setting.Type.ToLower()) {
            "dword" {
                $processedValue = [int]$processedValue
                Set-ItemProperty -Path $Setting.Path -Name $Setting.Name -Value $processedValue -Type DWord
            }
            "string" {
                Set-ItemProperty -Path $Setting.Path -Name $Setting.Name -Value $processedValue -Type String
            }
            "expandstring" {
                Set-ItemProperty -Path $Setting.Path -Name $Setting.Name -Value $processedValue -Type ExpandString
            }
            "binary" {
                Set-ItemProperty -Path $Setting.Path -Name $Setting.Name -Value $processedValue -Type Binary
            }
            "multistring" {
                Set-ItemProperty -Path $Setting.Path -Name $Setting.Name -Value $processedValue -Type MultiString
            }
            "qword" {
                $processedValue = [long]$processedValue
                Set-ItemProperty -Path $Setting.Path -Name $Setting.Name -Value $processedValue -Type QWord
            }
            default {
                Write-Log "Unknown registry type '$($Setting.Type)' for setting '$($Setting.Name)'" -Level "WARN"
                Set-ItemProperty -Path $Setting.Path -Name $Setting.Name -Value $processedValue
            }
        }
        
        # Verify the value was set correctly
        $newValue = Get-ItemProperty -Path $Setting.Path -Name $Setting.Name | Select-Object -ExpandProperty $Setting.Name
        
        if ($currentValue -ne $newValue) {
            Write-Log "Successfully set registry value: $($Setting.Path)\$($Setting.Name) = $processedValue (was: $currentValue)" -Level "SUCCESS"
        }
        else {
            Write-Log "Registry value unchanged: $($Setting.Path)\$($Setting.Name) = $processedValue" -Level "INFO"
        }
        
        return $true
    }
    catch {
        Write-Log "Failed to set registry value '$($Setting.Path)\$($Setting.Name)': $($_.Exception.Message)" -Level "ERROR"
        return $false
    }
}

function Backup-RegistrySettings {
    param(
        [Parameter(Mandatory = $true)]
        [array]$Settings,
        
        [Parameter(Mandatory = $true)]
        [string]$BackupPath
    )
    
    try {
        $backupData = @()
        
        foreach ($setting in $Settings) {
            try {
                $currentValue = Get-ItemProperty -Path $setting.Path -Name $setting.Name -ErrorAction SilentlyContinue | Select-Object -ExpandProperty $setting.Name
                
                $backupData += [PSCustomObject]@{
                    Path        = $setting.Path
                    Name        = $setting.Name
                    Value       = $currentValue
                    Type        = $setting.Type
                    Description = $setting.Description
                    BackupDate  = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
                }
            }
            catch {
                # Value doesn't exist, record as null
                $backupData += [PSCustomObject]@{
                    Path        = $setting.Path
                    Name        = $setting.Name
                    Value       = $null
                    Type        = $setting.Type
                    Description = $setting.Description
                    BackupDate  = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
                }
            }
        }
        
        $backupData | ConvertTo-Json -Depth 3 | Out-File -FilePath $BackupPath -Encoding UTF8
        Write-Log "Registry backup created: $BackupPath" -Level "SUCCESS"
        return $true
    }
    catch {
        Write-Log "Failed to create registry backup: $($_.Exception.Message)" -Level "ERROR"
        return $false
    }
}

# Main execution
try {
    Write-Log "Starting Windows Registry Settings Configuration" -Level "INFO"
    Write-Log "Configuration file: $ConfigPath" -Level "INFO"
    Write-Log "Log file: $LogPath" -Level "INFO"
    Write-Log "Deployment date: $DeploymentDate" -Level "INFO"
    
    # Verify configuration file exists
    if (-not (Test-Path -Path $ConfigPath)) {
        throw "Configuration file not found: $ConfigPath"
    }
    
    # Load configuration
    Write-Log "Loading configuration from: $ConfigPath" -Level "INFO"
    $config = Get-Content -Path $ConfigPath -Raw | ConvertFrom-Json
    
    if (-not $config.RegistrySettings) {
        throw "No RegistrySettings section found in configuration file"
    }
    
    Write-Log "Found $($config.RegistrySettings.Count) registry settings to configure" -Level "INFO"
    
    # Create backup of current registry settings
    $backupPath = $LogPath -replace "\.log$", "_backup_$(Get-Date -Format 'yyyyMMdd_HHmmss').json"
    Write-Log "Creating backup of current registry settings..." -Level "INFO"
    Backup-RegistrySettings -Settings $config.RegistrySettings -BackupPath $backupPath
    
    # Configure each registry setting
    $successCount = 0
    $failureCount = 0
    
    foreach ($setting in $config.RegistrySettings) {
        Write-Log "Processing registry setting: $($setting.Path)\$($setting.Name)" -Level "INFO"
        
        if (Set-RegistryValue -Setting $setting -DeploymentDate $DeploymentDate) {
            $successCount++
        }
        else {
            $failureCount++
        }
    }
    
    # Summary
    Write-Log "Registry Settings Configuration Summary:" -Level "INFO"
    Write-Log "- Total settings processed: $($config.RegistrySettings.Count)" -Level "INFO"
    Write-Log "- Successfully configured: $successCount" -Level "SUCCESS"
    Write-Log "- Failed to configure: $failureCount" -Level $(if ($failureCount -gt 0) { "ERROR" } else { "INFO" })
    Write-Log "- Backup file: $backupPath" -Level "INFO"
    
    if ($failureCount -eq 0) {
        Write-Log "All registry settings configured successfully!" -Level "SUCCESS"
        exit 0
    }
    else {
        Write-Log "Some registry settings failed to configure. Check the log for details." -Level "WARN"
        exit 1
    }
}
catch {
    Write-Log "Critical error during registry configuration: $($_.Exception.Message)" -Level "ERROR"
    Write-Log "Stack trace: $($_.ScriptStackTrace)" -Level "ERROR"
    exit 1
}
