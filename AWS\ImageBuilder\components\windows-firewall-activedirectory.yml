# AWS Image Builder Component: Configure Active Directory Firewall Rules
# This component configures Windows Firewall rules for Active Directory services

name: configure-firewall-activedirectory
description: Configure Windows Firewall rules for Active Directory domain services and authentication
schemaVersion: 1.0
phases:
- name: build
  steps:
  - name: ConfigureActiveDirectoryRules
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Configuring Active Directory firewall rules..."

        # LDAP (Port 389)
        Write-Host "Allowing LDAP (port 389)..."
        New-NetFirewallRule -DisplayName "LDAP" -Direction Inbound -Action Allow -Protocol TCP -LocalPort 389 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow LDAP from internal networks"

        # LDAPS/Secure LDAP (Port 636)
        Write-Host "Allowing LDAPS (port 636)..."
        New-NetFirewallRule -DisplayName "LDAPS" -Direction Inbound -Action Allow -Protocol TCP -LocalPort 636 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow secure LDAP from internal networks"

        # Global Catalog LDAP (Port 3268)
        Write-Host "Allowing Global Catalog LDAP (port 3268)..."
        New-NetFirewallRule -DisplayName "Global Catalog LDAP" -Direction Inbound -Action Allow -Protocol TCP -LocalPort 3268 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow Global Catalog LDAP from internal networks"

        # Global Catalog LDAPS (Port 3269)
        Write-Host "Allowing Global Catalog LDAPS (port 3269)..."
        New-NetFirewallRule -DisplayName "Global Catalog LDAPS" -Direction Inbound -Action Allow -Protocol TCP -LocalPort 3269 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow secure Global Catalog LDAP from internal networks"

        # Kerberos (Port 88)
        Write-Host "Allowing Kerberos (port 88)..."
        New-NetFirewallRule -DisplayName "Kerberos TCP" -Direction Inbound -Action Allow -Protocol TCP -LocalPort 88 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow Kerberos TCP from internal networks"
        New-NetFirewallRule -DisplayName "Kerberos UDP" -Direction Inbound -Action Allow -Protocol UDP -LocalPort 88 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow Kerberos UDP from internal networks"

        # DNS (Port 53) - Required for AD domain services
        Write-Host "Allowing DNS (port 53)..."
        New-NetFirewallRule -DisplayName "DNS TCP" -Direction Inbound -Action Allow -Protocol TCP -LocalPort 53 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow DNS TCP from internal networks"
        New-NetFirewallRule -DisplayName "DNS UDP" -Direction Inbound -Action Allow -Protocol UDP -LocalPort 53 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow DNS UDP from internal networks"

        # RPC Endpoint Mapper (Port 135)
        Write-Host "Allowing RPC Endpoint Mapper (port 135)..."
        New-NetFirewallRule -DisplayName "RPC Endpoint Mapper" -Direction Inbound -Action Allow -Protocol TCP -LocalPort 135 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow RPC Endpoint Mapper from internal networks"

        # RPC Dynamic Ports (for AD replication and management)
        Write-Host "Allowing RPC Dynamic Ports for AD replication..."
        New-NetFirewallRule -DisplayName "RPC Dynamic Ports AD" -Direction Inbound -Action Allow -Protocol TCP -LocalPort 1024-65535 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow RPC dynamic ports for AD replication from internal networks"

        # SMB/CIFS (Ports 445, 139)
        Write-Host "Allowing SMB/CIFS (ports 445, 139)..."
        New-NetFirewallRule -DisplayName "SMB" -Direction Inbound -Action Allow -Protocol TCP -LocalPort 445 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow SMB from internal networks"
        New-NetFirewallRule -DisplayName "NetBIOS Session" -Direction Inbound -Action Allow -Protocol TCP -LocalPort 139 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow NetBIOS Session from internal networks"

        # NetBIOS Name Service (Port 137 UDP)
        Write-Host "Allowing NetBIOS Name Service (port 137 UDP)..."
        New-NetFirewallRule -DisplayName "NetBIOS Name Service" -Direction Inbound -Action Allow -Protocol UDP -LocalPort 137 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow NetBIOS Name Service from internal networks"

        # NetBIOS Datagram Service (Port 138 UDP)
        Write-Host "Allowing NetBIOS Datagram Service (port 138 UDP)..."
        New-NetFirewallRule -DisplayName "NetBIOS Datagram Service" -Direction Inbound -Action Allow -Protocol UDP -LocalPort 138 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow NetBIOS Datagram Service from internal networks"

        # NTP (Port 123) - For time synchronization
        Write-Host "Allowing NTP (port 123)..."
        New-NetFirewallRule -DisplayName "NTP" -Direction Inbound -Action Allow -Protocol UDP -LocalPort 123 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow NTP from internal networks"

        # Enable built-in Active Directory firewall rules
        Write-Host "Enabling built-in Active Directory firewall rules..."
        $adRuleGroups = @(
            "Active Directory Domain Services",
            "DFS Management",
            "DFS Replication",
            "File and Printer Sharing"
        )

        foreach ($ruleGroup in $adRuleGroups) {
            try {
                Enable-NetFirewallRule -DisplayGroup $ruleGroup -ErrorAction SilentlyContinue
                Write-Host "Enabled firewall rule group: $ruleGroup"
            }
            catch {
                Write-Host "Could not enable firewall rule group $ruleGroup`: $($_.Exception.Message)" -ForegroundColor Yellow
            }
        }

        Write-Host "Active Directory firewall rules configured successfully"

- name: validate
  steps:
  - name: ValidateActiveDirectoryFirewallConfiguration
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Validating Active Directory firewall configuration..."

        $validationErrors = @()

        # Check if key Active Directory rules exist
        $existingRules = Get-NetFirewallRule | Where-Object { $_.Enabled -eq "True" }

        $requiredADRules = @(
            "LDAP",
            "LDAPS",
            "Global Catalog LDAP",
            "Global Catalog LDAPS",
            "Kerberos TCP",
            "Kerberos UDP",
            "DNS TCP",
            "DNS UDP",
            "RPC Endpoint Mapper",
            "SMB",
            "NetBIOS Session"
        )

        $missingRules = @()
        foreach ($rule in $requiredADRules) {
            $foundRule = $existingRules | Where-Object { $_.DisplayName -like "*$rule*" }
            if (-not $foundRule) {
                $missingRules += $rule
            }
        }

        if ($missingRules.Count -gt 0) {
            $validationErrors += "Missing Active Directory firewall rules: $($missingRules -join ', ')"
        }

        # Validate specific AD port rules
        try {
            $ldapRule = Get-NetFirewallRule -DisplayName "LDAP" -ErrorAction SilentlyContinue
            if ($ldapRule) {
                $ldapPortFilter = Get-NetFirewallPortFilter -AssociatedNetFirewallRule $ldapRule
                if ($ldapPortFilter.LocalPort -ne "389") {
                    $validationErrors += "LDAP rule should be on port 389, found: $($ldapPortFilter.LocalPort)"
                }
            }

            $ldapsRule = Get-NetFirewallRule -DisplayName "LDAPS" -ErrorAction SilentlyContinue
            if ($ldapsRule) {
                $ldapsPortFilter = Get-NetFirewallPortFilter -AssociatedNetFirewallRule $ldapsRule
                if ($ldapsPortFilter.LocalPort -ne "636") {
                    $validationErrors += "LDAPS rule should be on port 636, found: $($ldapsPortFilter.LocalPort)"
                }
            }

            $gcLdapRule = Get-NetFirewallRule -DisplayName "Global Catalog LDAP" -ErrorAction SilentlyContinue
            if ($gcLdapRule) {
                $gcLdapPortFilter = Get-NetFirewallPortFilter -AssociatedNetFirewallRule $gcLdapRule
                if ($gcLdapPortFilter.LocalPort -ne "3268") {
                    $validationErrors += "Global Catalog LDAP rule should be on port 3268, found: $($gcLdapPortFilter.LocalPort)"
                }
            }

            $kerberosRule = Get-NetFirewallRule -DisplayName "Kerberos TCP" -ErrorAction SilentlyContinue
            if ($kerberosRule) {
                $kerberosPortFilter = Get-NetFirewallPortFilter -AssociatedNetFirewallRule $kerberosRule
                if ($kerberosPortFilter.LocalPort -ne "88") {
                    $validationErrors += "Kerberos TCP rule should be on port 88, found: $($kerberosPortFilter.LocalPort)"
                }
            }

            $dnsRule = Get-NetFirewallRule -DisplayName "DNS TCP" -ErrorAction SilentlyContinue
            if ($dnsRule) {
                $dnsPortFilter = Get-NetFirewallPortFilter -AssociatedNetFirewallRule $dnsRule
                if ($dnsPortFilter.LocalPort -ne "53") {
                    $validationErrors += "DNS TCP rule should be on port 53, found: $($dnsPortFilter.LocalPort)"
                }
            }

            $smbRule = Get-NetFirewallRule -DisplayName "SMB" -ErrorAction SilentlyContinue
            if ($smbRule) {
                $smbPortFilter = Get-NetFirewallPortFilter -AssociatedNetFirewallRule $smbRule
                if ($smbPortFilter.LocalPort -ne "445") {
                    $validationErrors += "SMB rule should be on port 445, found: $($smbPortFilter.LocalPort)"
                }
            }
        }
        catch {
            $validationErrors += "Failed to validate Active Directory port configurations: $($_.Exception.Message)"
        }

        # Report validation results
        if ($validationErrors.Count -eq 0) {
            Write-Host "VALIDATION SUCCESS: Active Directory firewall rules configured correctly"
            Write-Host "- LDAP: Port 389 (standard) and 636 (secure)"
            Write-Host "- Global Catalog: Ports 3268 (standard) and 3269 (secure)"
            Write-Host "- Kerberos: Port 88 (TCP and UDP)"
            Write-Host "- DNS: Port 53 (TCP and UDP)"
            Write-Host "- RPC: Port 135 (endpoint mapper) and dynamic ports"
            Write-Host "- SMB/NetBIOS: Ports 445, 139, 137, 138"
            Write-Host "- NTP: Port 123 (time synchronization)"
            Write-Host "- All rules restricted to internal networks only"
            Write-Host "- Total AD rules: $($existingRules | Where-Object { $_.DisplayName -like "*LDAP*" -or $_.DisplayName -like "*Kerberos*" -or $_.DisplayName -like "*DNS*" -or $_.DisplayName -like "*SMB*" -or $_.DisplayName -like "*NetBIOS*" -or $_.DisplayName -like "*RPC*" } | Measure-Object | Select-Object -ExpandProperty Count)"
            exit 0
        } else {
            Write-Error "VALIDATION FAILED: Active Directory firewall configuration errors found:"
            foreach ($error in $validationErrors) {
                Write-Error "  - $error"
            }
            exit 1
        }
