# ImageBuilder Component Restructure

## Overview

The large ImageBuilder components have been split into smaller, focused components to resolve the AWS ImageBuilder 16,000 character limit error. This restructure improves maintainability, modularity, and allows for more granular control over which configurations are applied.

## Registry Components

The original `configure-registry-settings.yml` has been split into:

### 1. configure-windows-update-registry.yml
- **Purpose**: Windows Update settings for AWS Systems Manager Patch Manager
- **Key Features**:
  - Disables automatic updates (Systems Manager control)
  - Removes WSUS server configuration
  - Sets manual update control
  - Configures scheduled install times

### 2. configure-security-registry.yml
- **Purpose**: Security-related registry settings
- **Key Features**:
  - UAC (User Account Control) configuration
  - RDP security settings with NLA
  - Authentication settings (NTLMv2, LM hash disabled)
  - SMB security signing requirements

### 3. configure-performance-registry.yml
- **Purpose**: Performance optimization registry settings
- **Key Features**:
  - System performance for background services
  - Memory management (paging executive, large system cache)
  - I/O optimization for throughput
  - Network performance (TCP window scaling)
  - Power management (hibernation disabled)

### 4. configure-eventlog-registry.yml
- **Purpose**: Event log configuration and monitoring
- **Key Features**:
  - Event log sizes (Application: 32MB, Security: 128MB, System: 32MB)
  - Log retention policies
  - Audit settings for security events
  - PowerShell logging (module and script block)
  - Noise reduction (WER and CEIP disabled)

### 5. configure-ie-security-registry.yml
- **Purpose**: Internet Explorer security and cryptography
- **Key Features**:
  - IE print info disclosure fix
  - IE exception handler hardening
  - DEP and ASLR for IE
  - Certificate padding check
  - Strong cryptography for .NET Framework
  - SSL/TLS security (SSL 2.0 and 3.0 disabled)

## Firewall Components

The original `configure-firewall-rules.yml` has been split into:

### 1. configure-basic-firewall.yml
- **Purpose**: Basic firewall setup and management rules
- **Key Features**:
  - Enable Windows Firewall on all profiles
  - Default actions (block inbound, allow outbound)
  - Logging configuration
  - Any-Any bypass rules
  - RDP, WinRM, ICMP rules
  - Windows management rules (WMI, RPC)

### 2. configure-webserver-firewall.yml
- **Purpose**: Web server application firewall rules
- **Key Features**:
  - HTTP (80) and HTTPS (443)
  - Common web application ports (8080, 8443, 8000, 8888, 9000)
  - FTP for web content management
  - Outbound web traffic rules

### 3. configure-database-firewall.yml
- **Purpose**: Database server firewall rules
- **Key Features**:
  - SQL Server (1433, 1434, 2383, dynamic ports)
  - MySQL/MariaDB (3306)
  - PostgreSQL (5432)
  - Oracle (1521)
  - MongoDB (27017)
  - Redis (6379)
  - Elasticsearch (9200, 9300)

### 4. configure-activedirectory-firewall.yml
- **Purpose**: Active Directory services firewall rules
- **Key Features**:
  - DNS (53 TCP/UDP)
  - LDAP (389) and LDAPS (636)
  - Global Catalog (3268, 3269)
  - Kerberos (88 TCP/UDP)
  - Inbound and outbound rules for domain services

### 5. configure-system-firewall.yml
- **Purpose**: System services and protocols
- **Key Features**:
  - RPC Endpoint Mapper (135) and dynamic ports
  - NetBIOS (137, 138, 139)
  - SMB/CIFS (445)
  - SNMP (161)
  - NTP (123)
  - DHCP client (67, 68)
  - SCCM/ConfigMgr (10123)

### 6. configure-security-firewall.yml
- **Purpose**: Security rules and outbound traffic management
- **Key Features**:
  - Block insecure protocols (Telnet, FTP, TFTP)
  - Block common malware ports
  - Internal network outbound rules
  - Secure outbound protocols (HTTPS, LDAPS, SMTPS, IMAPS, FTPS, SFTP)

## Updated Recipe Files

The following recipe files have been updated to use the new components:

### AWS\ImageBuilder\recipes\windows-server-2022-custom.yml
- Replaced `ConfigureRegistryTweaks` with 5 focused registry components
- Replaced `ConfigureFirewallRules` with 6 focused firewall components

### AWS\ImageBuilder\recipes\templates\example-recipe-with-sccm.yml
- Updated to use new focused components
- SCCM-specific rules are covered by existing components:
  - HTTP/HTTPS by `configure-webserver-firewall`
  - WMI/RPC by `configure-system-firewall`

## Benefits of the New Structure

1. **Size Compliance**: Each component is well under the 16,000 character limit
2. **Modularity**: Apply only the configurations needed for specific server roles
3. **Maintainability**: Easier to update and troubleshoot specific configuration areas
4. **Validation**: Each component has focused validation for its specific area
5. **Flexibility**: Mix and match components based on server requirements

## Usage Examples

### Basic Server (Minimal Configuration)
```yaml
components:
- name: configure-windows-update-registry
- name: configure-security-registry
- name: configure-basic-firewall
- name: configure-security-firewall
```

### Web Server
```yaml
components:
- name: configure-windows-update-registry
- name: configure-security-registry
- name: configure-performance-registry
- name: configure-basic-firewall
- name: configure-webserver-firewall
- name: configure-security-firewall
```

### Database Server
```yaml
components:
- name: configure-windows-update-registry
- name: configure-security-registry
- name: configure-performance-registry
- name: configure-eventlog-registry
- name: configure-basic-firewall
- name: configure-database-firewall
- name: configure-security-firewall
```

### Domain Controller
```yaml
components:
- name: configure-windows-update-registry
- name: configure-security-registry
- name: configure-eventlog-registry
- name: configure-basic-firewall
- name: configure-activedirectory-firewall
- name: configure-security-firewall
```

## Migration Notes

- Old component files have been removed from `component-templates` directory
- All new components include comprehensive validation steps
- Component names follow the pattern `configure-[area]-[type]`
- Each component is self-contained with its own validation logic
