# AWS Image Builder Component: Configure Windows Event Log Registry Settings
# This component configures Windows Event Log registry settings for business environments

name: windows-registry-eventlog
description: Configure Windows Event Log registry settings for optimal logging and monitoring
schemaVersion: 1.0
phases:
  - name: build
    steps:
      - name: ConfigureEventLogSettings
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "Configuring Event Log registry settings..."

                # Create Event Log policy paths if they don't exist
                $appLogPath = "HKLM:\SOFTWARE\Policies\Microsoft\Windows\EventLog\Application"
                $secLogPath = "HKLM:\SOFTWARE\Policies\Microsoft\Windows\EventLog\Security"
                $sysLogPath = "HKLM:\SOFTWARE\Policies\Microsoft\Windows\EventLog\System"

                foreach ($path in @($appLogPath, $secLogPath, $sysLogPath)) {
                    if (-not (Test-Path $path)) {
                        New-Item -Path $path -Force | Out-Null
                        Write-Host "Created registry path: $path"
                    }
                }

                # Set Application log size to 32MB
                Write-Host "Setting Application log size to 32MB..."
                Set-ItemProperty -Path $appLogPath -Name "MaxSize" -Value 32768 -Type DWord

                # Set Security log size to 128MB (larger for security events)
                Write-Host "Setting Security log size to 128MB..."
                Set-ItemProperty -Path $secLogPath -Name "MaxSize" -Value 131072 -Type DWord

                # Set System log size to 32MB
                Write-Host "Setting System log size to 32MB..."
                Set-ItemProperty -Path $sysLogPath -Name "MaxSize" -Value 32768 -Type DWord

                # Configure log retention policies
                Write-Host "Configuring log retention policies..."

                # Set retention method to overwrite as needed (0 = overwrite as needed)
                Set-ItemProperty -Path $appLogPath -Name "Retention" -Value 0 -Type DWord
                Set-ItemProperty -Path $secLogPath -Name "Retention" -Value 0 -Type DWord
                Set-ItemProperty -Path $sysLogPath -Name "Retention" -Value 0 -Type DWord

                # Configure additional event log settings
                Write-Host "Configuring additional event log settings..."

                # Enable audit policy for security events
                $auditPath = "HKLM:\SYSTEM\CurrentControlSet\Control\Lsa"
                Set-ItemProperty -Path $auditPath -Name "AuditBaseObjects" -Value 1 -Type DWord
                Set-ItemProperty -Path $auditPath -Name "FullPrivilegeAuditing" -Value 1 -Type DWord

                # Configure Windows Error Reporting to reduce noise
                Write-Host "Configuring Windows Error Reporting for event logs..."
                $werPath = "HKLM:\SOFTWARE\Microsoft\Windows\Windows Error Reporting"
                if (-not (Test-Path $werPath)) {
                    New-Item -Path $werPath -Force | Out-Null
                }
                Set-ItemProperty -Path $werPath -Name "Disabled" -Value 1 -Type DWord

                # Configure Customer Experience Improvement Program to reduce telemetry events
                Write-Host "Disabling Customer Experience Improvement Program..."
                $ceipPath = "HKLM:\SOFTWARE\Microsoft\SQMClient\Windows"
                if (-not (Test-Path $ceipPath)) {
                    New-Item -Path $ceipPath -Force | Out-Null
                }
                Set-ItemProperty -Path $ceipPath -Name "CEIPEnable" -Value 0 -Type DWord

                # Configure PowerShell logging
                Write-Host "Configuring PowerShell logging settings..."
                $psLoggingPath = "HKLM:\SOFTWARE\Policies\Microsoft\Windows\PowerShell\ModuleLogging"
                $psScriptBlockPath = "HKLM:\SOFTWARE\Policies\Microsoft\Windows\PowerShell\ScriptBlockLogging"

                if (-not (Test-Path $psLoggingPath)) {
                    New-Item -Path $psLoggingPath -Force | Out-Null
                }
                if (-not (Test-Path $psScriptBlockPath)) {
                    New-Item -Path $psScriptBlockPath -Force | Out-Null
                }

                # Enable PowerShell module logging
                Set-ItemProperty -Path $psLoggingPath -Name "EnableModuleLogging" -Value 1 -Type DWord

                # Enable PowerShell script block logging
                Set-ItemProperty -Path $psScriptBlockPath -Name "EnableScriptBlockLogging" -Value 1 -Type DWord

                Write-Host "Event Log registry settings configured successfully"

  - name: validate
    steps:
      - name: ValidateEventLogConfiguration
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "Validating Windows Event Log registry configuration..."

                $validationErrors = @()

                # Validate Event Log settings
                try {
                    $appLogSize = Get-ItemProperty -Path "HKLM:\SOFTWARE\Policies\Microsoft\Windows\EventLog\Application" -Name "MaxSize" -ErrorAction SilentlyContinue | Select-Object -ExpandProperty MaxSize
                    if ($appLogSize -ne 32768) {
                        $validationErrors += "Application log size should be 32768 KB (32MB), found: $appLogSize"
                    }

                    $secLogSize = Get-ItemProperty -Path "HKLM:\SOFTWARE\Policies\Microsoft\Windows\EventLog\Security" -Name "MaxSize" -ErrorAction SilentlyContinue | Select-Object -ExpandProperty MaxSize
                    if ($secLogSize -ne 131072) {
                        $validationErrors += "Security log size should be 131072 KB (128MB), found: $secLogSize"
                    }

                    $sysLogSize = Get-ItemProperty -Path "HKLM:\SOFTWARE\Policies\Microsoft\Windows\EventLog\System" -Name "MaxSize" -ErrorAction SilentlyContinue | Select-Object -ExpandProperty MaxSize
                    if ($sysLogSize -ne 32768) {
                        $validationErrors += "System log size should be 32768 KB (32MB), found: $sysLogSize"
                    }

                    # Validate retention policies
                    $appRetention = Get-ItemProperty -Path "HKLM:\SOFTWARE\Policies\Microsoft\Windows\EventLog\Application" -Name "Retention" -ErrorAction SilentlyContinue | Select-Object -ExpandProperty Retention
                    if ($appRetention -ne 0) {
                        $validationErrors += "Application log retention should be 0 (overwrite as needed), found: $appRetention"
                    }
                }
                catch {
                    $validationErrors += "Failed to validate Event Log settings: $($_.Exception.Message)"
                }

                # Validate audit settings
                try {
                    $auditBaseObjects = Get-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Lsa" -Name "AuditBaseObjects" -ErrorAction SilentlyContinue | Select-Object -ExpandProperty AuditBaseObjects
                    if ($auditBaseObjects -ne 1) {
                        $validationErrors += "AuditBaseObjects should be 1 (enabled), found: $auditBaseObjects"
                    }

                    $fullPrivilegeAuditing = Get-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Lsa" -Name "FullPrivilegeAuditing" -ErrorAction SilentlyContinue | Select-Object -ExpandProperty FullPrivilegeAuditing
                    if ($fullPrivilegeAuditing -ne 1) {
                        $validationErrors += "FullPrivilegeAuditing should be 1 (enabled), found: $fullPrivilegeAuditing"
                    }
                }
                catch {
                    $validationErrors += "Failed to validate audit settings: $($_.Exception.Message)"
                }

                # Validate PowerShell logging
                try {
                    $psModuleLogging = Get-ItemProperty -Path "HKLM:\SOFTWARE\Policies\Microsoft\Windows\PowerShell\ModuleLogging" -Name "EnableModuleLogging" -ErrorAction SilentlyContinue | Select-Object -ExpandProperty EnableModuleLogging
                    if ($psModuleLogging -ne 1) {
                        $validationErrors += "PowerShell module logging should be enabled (1), found: $psModuleLogging"
                    }

                    $psScriptBlockLogging = Get-ItemProperty -Path "HKLM:\SOFTWARE\Policies\Microsoft\Windows\PowerShell\ScriptBlockLogging" -Name "EnableScriptBlockLogging" -ErrorAction SilentlyContinue | Select-Object -ExpandProperty EnableScriptBlockLogging
                    if ($psScriptBlockLogging -ne 1) {
                        $validationErrors += "PowerShell script block logging should be enabled (1), found: $psScriptBlockLogging"
                    }
                }
                catch {
                    $validationErrors += "Failed to validate PowerShell logging settings: $($_.Exception.Message)"
                }

                # Validate noise reduction settings
                try {
                    $werDisabled = Get-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows\Windows Error Reporting" -Name "Disabled" -ErrorAction SilentlyContinue | Select-Object -ExpandProperty Disabled
                    if ($werDisabled -ne 1) {
                        $validationErrors += "Windows Error Reporting should be disabled (1), found: $werDisabled"
                    }

                    $ceipDisabled = Get-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\SQMClient\Windows" -Name "CEIPEnable" -ErrorAction SilentlyContinue | Select-Object -ExpandProperty CEIPEnable
                    if ($ceipDisabled -ne 0) {
                        $validationErrors += "Customer Experience Improvement Program should be disabled (0), found: $ceipDisabled"
                    }
                }
                catch {
                    $validationErrors += "Failed to validate noise reduction settings: $($_.Exception.Message)"
                }

                # Report validation results
                if ($validationErrors.Count -eq 0) {
                    Write-Host "VALIDATION SUCCESS: Event Log registry settings configured correctly"
                    Write-Host "- Application Log: 32MB with overwrite as needed"
                    Write-Host "- Security Log: 128MB with overwrite as needed"
                    Write-Host "- System Log: 32MB with overwrite as needed"
                    Write-Host "- Audit Settings: Base objects and full privilege auditing enabled"
                    Write-Host "- PowerShell Logging: Module and script block logging enabled"
                    Write-Host "- Noise Reduction: WER and CEIP disabled"
                    exit 0
                } else {
                    Write-Error "VALIDATION FAILED: Event Log registry configuration errors found:"
                    foreach ($error in $validationErrors) {
                        Write-Error "  - $error"
                    }
                    exit 1
                }
