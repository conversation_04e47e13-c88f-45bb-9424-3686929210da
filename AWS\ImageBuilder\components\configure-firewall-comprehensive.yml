# AWS Image Builder Component: Comprehensive Windows Firewall Configuration
# This component configures Windows Firewall rules organized by functional groups
# Groups: Base, Security, Active Directory, SQL Server, Web Server, System Services

name: configure-firewall-comprehensive
description: Configure comprehensive Windows Firewall rules organized by functional groups
schemaVersion: 1.0
phases:
- name: build
  steps:
  - name: ConfigureBaseFirewallRules
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "=== CONFIGURING BASE FIREWALL RULES ==="

        # Create custom rule group for base rules
        $baseGroup = "Windows Server Base Services"

        # Allow RDP (Remote Desktop) - Restricted to internal networks only
        Write-Host "Allowing RDP from internal networks only..."
        New-NetFirewallRule -DisplayName "Allow RDP Inbound" -Group $baseGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 3389 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow RDP from internal networks only"

        # Allow WinRM HTTP - Domain profile only for PowerShell remoting
        Write-Host "Allowing WinRM HTTP for PowerShell remoting..."
        New-NetFirewallRule -DisplayName "Allow WinRM HTTP" -Group $baseGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 5985 -Profile Domain -RemoteAddress "10.0.0.0/8" -Description "Allow WinRM for PowerShell remoting"

        # Allow WinRM HTTPS - Any profile for ImageBuilder compatibility
        Write-Host "Allowing WinRM HTTPS for secure PowerShell remoting..."
        New-NetFirewallRule -DisplayName "WinRM-HTTPS" -Group $baseGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 5986 -Profile Any -Description "WinRM HTTPS for ImageBuilder and remote management"

        Write-Host "Base firewall rules configured successfully"

  - name: ConfigureSecurityFirewallRules
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "=== CONFIGURING SECURITY FIREWALL RULES ==="

        # Create custom rule group for security rules
        $securityGroup = "Windows Server Security Blocks"

        # Block common attack ports from public networks
        Write-Host "Blocking common attack ports from public networks..."

        # Block Telnet (insecure protocol)
        New-NetFirewallRule -DisplayName "Block Telnet Public" -Group $securityGroup -Direction Inbound -Action Block -Protocol TCP -LocalPort 23 -Profile Public -Description "Block insecure Telnet from public networks"

        # Block FTP (insecure protocol) from public networks
        New-NetFirewallRule -DisplayName "Block FTP Public" -Group $securityGroup -Direction Inbound -Action Block -Protocol TCP -LocalPort 21 -Profile Public -Description "Block insecure FTP from public networks"

        # Block SNMP from public networks (information disclosure)
        New-NetFirewallRule -DisplayName "Block SNMP Public" -Group $securityGroup -Direction Inbound -Action Block -Protocol UDP -LocalPort 161 -Profile Public -Description "Block SNMP from public networks"

        # Block NetBIOS from public networks
        New-NetFirewallRule -DisplayName "Block NetBIOS Public" -Group $securityGroup -Direction Inbound -Action Block -Protocol TCP -LocalPort 139 -Profile Public -Description "Block NetBIOS from public networks"
        New-NetFirewallRule -DisplayName "Block NetBIOS Name Public" -Group $securityGroup -Direction Inbound -Action Block -Protocol UDP -LocalPort 137 -Profile Public -Description "Block NetBIOS Name Service from public networks"
        New-NetFirewallRule -DisplayName "Block NetBIOS Datagram Public" -Group $securityGroup -Direction Inbound -Action Block -Protocol UDP -LocalPort 138 -Profile Public -Description "Block NetBIOS Datagram from public networks"

        # Block SMB from public networks
        New-NetFirewallRule -DisplayName "Block SMB Public" -Group $securityGroup -Direction Inbound -Action Block -Protocol TCP -LocalPort 445 -Profile Public -Description "Block SMB from public networks"

        # Block RDP from public networks (allow only from internal)
        New-NetFirewallRule -DisplayName "Block RDP Public" -Group $securityGroup -Direction Inbound -Action Block -Protocol TCP -LocalPort 3389 -Profile Public -Description "Block RDP from public networks"

        # Block common database ports from public networks
        Write-Host "Blocking database ports from public networks..."
        $databasePorts = @(1433, 1434, 3306, 5432, 1521, 27017, 6379)
        foreach ($port in $databasePorts) {
            New-NetFirewallRule -DisplayName "Block Database Port $port Public" -Group $securityGroup -Direction Inbound -Action Block -Protocol TCP -LocalPort $port -Profile Public -Description "Block database port $port from public networks"
        }

        Write-Host "Security firewall rules configured successfully"

  - name: ConfigureActiveDirectoryFirewallRules
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "=== CONFIGURING ACTIVE DIRECTORY FIREWALL RULES ==="

        # Create custom rule group for AD rules
        $adGroup = "Windows Server Active Directory"

        # LDAP (Port 389)
        Write-Host "Allowing LDAP (port 389)..."
        New-NetFirewallRule -DisplayName "LDAP" -Group $adGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 389 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow LDAP from internal networks"

        # LDAPS/Secure LDAP (Port 636)
        Write-Host "Allowing LDAPS (port 636)..."
        New-NetFirewallRule -DisplayName "LDAPS" -Group $adGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 636 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow secure LDAP from internal networks"

        # Global Catalog LDAP (Port 3268)
        Write-Host "Allowing Global Catalog LDAP (port 3268)..."
        New-NetFirewallRule -DisplayName "Global Catalog LDAP" -Group $adGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 3268 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow Global Catalog LDAP from internal networks"

        # Global Catalog LDAPS (Port 3269)
        Write-Host "Allowing Global Catalog LDAPS (port 3269)..."
        New-NetFirewallRule -DisplayName "Global Catalog LDAPS" -Group $adGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 3269 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow secure Global Catalog LDAP from internal networks"

        # Kerberos (Port 88)
        Write-Host "Allowing Kerberos (port 88)..."
        New-NetFirewallRule -DisplayName "Kerberos TCP" -Group $adGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 88 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow Kerberos TCP from internal networks"
        New-NetFirewallRule -DisplayName "Kerberos UDP" -Group $adGroup -Direction Inbound -Action Allow -Protocol UDP -LocalPort 88 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow Kerberos UDP from internal networks"

        # DNS (Port 53) - Required for AD domain services
        Write-Host "Allowing DNS (port 53)..."
        New-NetFirewallRule -DisplayName "DNS TCP" -Group $adGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 53 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow DNS TCP from internal networks"
        New-NetFirewallRule -DisplayName "DNS UDP" -Group $adGroup -Direction Inbound -Action Allow -Protocol UDP -LocalPort 53 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow DNS UDP from internal networks"

        # RPC Endpoint Mapper (Port 135)
        Write-Host "Allowing RPC Endpoint Mapper (port 135)..."
        New-NetFirewallRule -DisplayName "RPC Endpoint Mapper" -Group $adGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 135 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow RPC Endpoint Mapper from internal networks"

        # RPC Dynamic Ports (for AD replication and management)
        Write-Host "Allowing RPC Dynamic Ports for AD replication..."
        New-NetFirewallRule -DisplayName "RPC Dynamic Ports AD" -Group $adGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 1024-65535 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow RPC dynamic ports for AD replication from internal networks"

        # SMB/CIFS (Ports 445, 139)
        Write-Host "Allowing SMB/CIFS (ports 445, 139)..."
        New-NetFirewallRule -DisplayName "SMB" -Group $adGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 445 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow SMB from internal networks"
        New-NetFirewallRule -DisplayName "NetBIOS Session" -Group $adGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 139 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow NetBIOS Session from internal networks"

        # NetBIOS Name Service (Port 137 UDP)
        Write-Host "Allowing NetBIOS Name Service (port 137 UDP)..."
        New-NetFirewallRule -DisplayName "NetBIOS Name Service" -Group $adGroup -Direction Inbound -Action Allow -Protocol UDP -LocalPort 137 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow NetBIOS Name Service from internal networks"

        # NetBIOS Datagram Service (Port 138 UDP)
        Write-Host "Allowing NetBIOS Datagram Service (port 138 UDP)..."
        New-NetFirewallRule -DisplayName "NetBIOS Datagram Service" -Group $adGroup -Direction Inbound -Action Allow -Protocol UDP -LocalPort 138 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow NetBIOS Datagram Service from internal networks"

        # NTP (Port 123) - For time synchronization
        Write-Host "Allowing NTP (port 123)..."
        New-NetFirewallRule -DisplayName "NTP" -Group $adGroup -Direction Inbound -Action Allow -Protocol UDP -LocalPort 123 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow NTP from internal networks"

        # Enable built-in Active Directory firewall rules
        Write-Host "Enabling built-in Active Directory firewall rules..."
        $adRuleGroups = @(
            "Active Directory Domain Services",
            "Active Directory Domain Services (NP-In)",
            "Active Directory Web Services (ADWS)",
            "DFS Management",
            "DFS Replication",
            "DNS Service",
            "Kerberos Key Distribution Center"
        )

        foreach ($ruleGroup in $adRuleGroups) {
            try {
                Enable-NetFirewallRule -DisplayGroup $ruleGroup -ErrorAction SilentlyContinue
                Write-Host "Enabled firewall rule group: $ruleGroup"
            }
            catch {
                Write-Host "Could not enable firewall rule group $ruleGroup`: $($_.Exception.Message)" -ForegroundColor Yellow
            }
        }

        Write-Host "Active Directory firewall rules configured successfully"

  - name: ConfigureSQLServerFirewallRules
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "=== CONFIGURING SQL SERVER FIREWALL RULES ==="

        # Create custom rule group for SQL Server rules
        $sqlGroup = "Windows Server SQL Server"

        # SQL Server default instance (Port 1433)
        Write-Host "Allowing SQL Server default instance (port 1433)..."
        New-NetFirewallRule -DisplayName "SQL Server Default Instance" -Group $sqlGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 1433 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow SQL Server default instance from internal networks"

        # SQL Server Browser Service (Port 1434 UDP)
        Write-Host "Allowing SQL Server Browser Service (port 1434 UDP)..."
        New-NetFirewallRule -DisplayName "SQL Server Browser Service" -Group $sqlGroup -Direction Inbound -Action Allow -Protocol UDP -LocalPort 1434 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow SQL Server Browser Service from internal networks"

        # SQL Server named instances (Dynamic ports range)
        Write-Host "Allowing SQL Server named instances (dynamic ports)..."
        New-NetFirewallRule -DisplayName "SQL Server Named Instances" -Group $sqlGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 1024-65535 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow SQL Server named instances from internal networks"

        # SQL Server Analysis Services (Port 2383)
        Write-Host "Allowing SQL Server Analysis Services (port 2383)..."
        New-NetFirewallRule -DisplayName "SQL Server Analysis Services" -Group $sqlGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 2383 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow SQL Server Analysis Services from internal networks"

        # SQL Server Reporting Services (Port 80/443)
        Write-Host "Allowing SQL Server Reporting Services (ports 80, 443)..."
        New-NetFirewallRule -DisplayName "SQL Server Reporting Services HTTP" -Group $sqlGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 80 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow SQL Server Reporting Services HTTP from internal networks"
        New-NetFirewallRule -DisplayName "SQL Server Reporting Services HTTPS" -Group $sqlGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 443 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow SQL Server Reporting Services HTTPS from internal networks"

        # SQL Server Integration Services (Port 135 for RPC)
        Write-Host "Allowing SQL Server Integration Services RPC (port 135)..."
        New-NetFirewallRule -DisplayName "SQL Server Integration Services RPC" -Group $sqlGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 135 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow SQL Server Integration Services RPC from internal networks"

        # SQL Server Service Broker (Port 4022)
        Write-Host "Allowing SQL Server Service Broker (port 4022)..."
        New-NetFirewallRule -DisplayName "SQL Server Service Broker" -Group $sqlGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 4022 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow SQL Server Service Broker from internal networks"

        # SQL Server Database Mirroring (Port 5022)
        Write-Host "Allowing SQL Server Database Mirroring (port 5022)..."
        New-NetFirewallRule -DisplayName "SQL Server Database Mirroring" -Group $sqlGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 5022 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow SQL Server Database Mirroring from internal networks"

        # SQL Server Always On Availability Groups (Port 5022)
        Write-Host "Allowing SQL Server Always On Availability Groups (port 5022)..."
        New-NetFirewallRule -DisplayName "SQL Server Always On AG" -Group $sqlGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 5022 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow SQL Server Always On Availability Groups from internal networks"

        # SQL Server Backup and Restore operations
        Write-Host "Allowing SQL Server backup and restore services..."
        New-NetFirewallRule -DisplayName "SQL Server Backup Services" -Group $sqlGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 3050,5000 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow SQL Server backup services from internal networks"

        # Enable built-in SQL Server firewall rules if they exist
        Write-Host "Enabling built-in SQL Server firewall rules..."
        $sqlRuleGroups = @(
            "SQL Server",
            "SQL Server Analysis Services",
            "SQL Server Browser",
            "SQL Server Integration Services",
            "SQL Server Reporting Services"
        )

        foreach ($ruleGroup in $sqlRuleGroups) {
            try {
                Enable-NetFirewallRule -DisplayGroup $ruleGroup -ErrorAction SilentlyContinue
                Write-Host "Enabled firewall rule group: $ruleGroup"
            }
            catch {
                Write-Host "Could not enable firewall rule group $ruleGroup`: $($_.Exception.Message)" -ForegroundColor Yellow
            }
        }

        Write-Host "SQL Server firewall rules configured successfully"

  - name: ConfigureWebServerFirewallRules
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "=== CONFIGURING WEB SERVER FIREWALL RULES ==="

        # Create custom rule group for Web Server rules
        $webGroup = "Windows Server Web Services"

        # Allow HTTP traffic (port 80)
        Write-Host "Allowing HTTP traffic (port 80)..."
        New-NetFirewallRule -DisplayName "HTTP Inbound" -Group $webGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 80 -Profile Any -Description "Allow HTTP web traffic"

        # Allow HTTPS traffic (port 443)
        Write-Host "Allowing HTTPS traffic (port 443)..."
        New-NetFirewallRule -DisplayName "HTTPS Inbound" -Group $webGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 443 -Profile Any -Description "Allow HTTPS secure web traffic"

        # Allow common web application ports
        Write-Host "Allowing common web application ports..."
        New-NetFirewallRule -DisplayName "Web App Port 8080" -Group $webGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 8080 -Profile Any -Description "Allow web application traffic on port 8080"
        New-NetFirewallRule -DisplayName "Web App Port 8443" -Group $webGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 8443 -Profile Any -Description "Allow secure web application traffic on port 8443"

        # Allow additional common web ports
        Write-Host "Allowing additional web server ports..."
        New-NetFirewallRule -DisplayName "Web App Port 8000" -Group $webGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 8000 -Profile Any -Description "Allow web application traffic on port 8000"
        New-NetFirewallRule -DisplayName "Web App Port 8888" -Group $webGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 8888 -Profile Any -Description "Allow web application traffic on port 8888"
        New-NetFirewallRule -DisplayName "Web App Port 9000" -Group $webGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 9000 -Profile Any -Description "Allow web application traffic on port 9000"

        # Enable built-in Web Server firewall rules
        Write-Host "Enabling built-in Web Server firewall rules..."
        $webRuleGroups = @(
            "World Wide Web Services (HTTP)",
            "Secure World Wide Web Services (HTTPS)",
            "Web Management Service (HTTP)",
            "Web Management Service (HTTPS)",
            "IIS Management Console"
        )

        foreach ($ruleGroup in $webRuleGroups) {
            try {
                Enable-NetFirewallRule -DisplayGroup $ruleGroup -ErrorAction SilentlyContinue
                Write-Host "Enabled firewall rule group: $ruleGroup"
            }
            catch {
                Write-Host "Could not enable firewall rule group $ruleGroup`: $($_.Exception.Message)" -ForegroundColor Yellow
            }
        }

        Write-Host "Web Server firewall rules configured successfully"

  - name: ConfigureSystemServicesFirewallRules
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "=== CONFIGURING SYSTEM SERVICES FIREWALL RULES ==="

        # Create custom rule group for System Services rules
        $systemGroup = "Windows Server System Services"

        # DHCP Server (Ports 67, 68)
        Write-Host "Allowing DHCP Server (ports 67, 68)..."
        New-NetFirewallRule -DisplayName "DHCP Server" -Group $systemGroup -Direction Inbound -Action Allow -Protocol UDP -LocalPort 67 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow DHCP server from internal networks"
        New-NetFirewallRule -DisplayName "DHCP Client" -Group $systemGroup -Direction Inbound -Action Allow -Protocol UDP -LocalPort 68 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow DHCP client from internal networks"

        # RADIUS (Ports 1812, 1813)
        Write-Host "Allowing RADIUS (ports 1812, 1813)..."
        New-NetFirewallRule -DisplayName "RADIUS Authentication" -Group $systemGroup -Direction Inbound -Action Allow -Protocol UDP -LocalPort 1812 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow RADIUS authentication from internal networks"
        New-NetFirewallRule -DisplayName "RADIUS Accounting" -Group $systemGroup -Direction Inbound -Action Allow -Protocol UDP -LocalPort 1813 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow RADIUS accounting from internal networks"

        # LDAP over UDP (Port 389)
        Write-Host "Allowing LDAP over UDP (port 389)..."
        New-NetFirewallRule -DisplayName "LDAP UDP" -Group $systemGroup -Direction Inbound -Action Allow -Protocol UDP -LocalPort 389 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow LDAP UDP from internal networks"

        # Print Spooler (Port 515)
        Write-Host "Allowing Print Spooler (port 515)..."
        New-NetFirewallRule -DisplayName "Print Spooler" -Group $systemGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 515 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow print spooler from internal networks"

        # Enable built-in system service firewall rules
        Write-Host "Enabling built-in system service firewall rules..."
        $systemRuleGroups = @(
            "Network Discovery",
            "Performance Logs and Alerts",
            "Remote Event Log Management",
            "Remote Scheduled Tasks Management",
            "Remote Volume Management",
            "Windows Firewall Remote Management",
            "Windows Management Instrumentation (WMI)"
        )

        foreach ($ruleGroup in $systemRuleGroups) {
            try {
                Enable-NetFirewallRule -DisplayGroup $ruleGroup -ErrorAction SilentlyContinue
                Write-Host "Enabled firewall rule group: $ruleGroup"
            }
            catch {
                Write-Host "Could not enable firewall rule group $ruleGroup`: $($_.Exception.Message)" -ForegroundColor Yellow
            }
        }

        Write-Host "System Services firewall rules configured successfully"

  - name: validate
    steps:
    - name: ValidateFirewallConfiguration
      action: ExecutePowerShell
      inputs:
        commands:
        - |
          Write-Host "=== VALIDATING COMPREHENSIVE FIREWALL CONFIGURATION ==="

          $validationErrors = @()

          # Check if firewall is enabled
          try {
              $firewallProfiles = Get-NetFirewallProfile
              foreach ($profile in $firewallProfiles) {
                  if ($profile.Enabled -eq $false) {
                      $validationErrors += "Windows Firewall is disabled for profile: $($profile.Name)"
                  }
              }
          }
          catch {
              $validationErrors += "Failed to check firewall profile status: $($_.Exception.Message)"
          }

          # Check if custom rule groups exist
          $existingRules = Get-NetFirewallRule | Where-Object { $_.Enabled -eq "True" }

          $customGroups = @(
              "Windows Server Base Services",
              "Windows Server Security Blocks",
              "Windows Server Active Directory",
              "Windows Server SQL Server",
              "Windows Server Web Services",
              "Windows Server System Services"
          )

          foreach ($group in $customGroups) {
              $groupRules = $existingRules | Where-Object { $_.Group -eq $group }
              if ($groupRules.Count -eq 0) {
                  $validationErrors += "No enabled rules found for custom group: $group"
              } else {
                  Write-Host "Found $($groupRules.Count) enabled rules in group: $group"
              }
          }

          # Check key base service rules
          $requiredBaseRules = @("Allow RDP Inbound", "Allow WinRM HTTP", "WinRM-HTTPS")
          foreach ($rule in $requiredBaseRules) {
              $foundRule = $existingRules | Where-Object { $_.DisplayName -eq $rule }
              if (-not $foundRule) {
                  $validationErrors += "Missing base service rule: $rule"
              }
          }

          # Check key security block rules
          $requiredSecurityRules = @("Block Telnet Public", "Block FTP Public", "Block SNMP Public", "Block SMB Public")
          foreach ($rule in $requiredSecurityRules) {
              $foundRule = $existingRules | Where-Object { $_.DisplayName -eq $rule }
              if (-not $foundRule) {
                  $validationErrors += "Missing security block rule: $rule"
              }
          }

          # Report validation results
          if ($validationErrors.Count -eq 0) {
              Write-Host "VALIDATION SUCCESS: Comprehensive firewall configuration completed successfully"
              Write-Host "=== FIREWALL GROUPS CONFIGURED ==="
              Write-Host "✓ Base Services: RDP, WinRM (internal networks only)"
              Write-Host "✓ Security Blocks: Attack prevention (public networks blocked)"
              Write-Host "✓ Active Directory: LDAP, Kerberos, DNS, SMB, RPC (internal networks only)"
              Write-Host "✓ SQL Server: Database engine, Analysis, Reporting, Integration Services (internal networks only)"
              Write-Host "✓ Web Services: HTTP, HTTPS, common web application ports (all networks)"
              Write-Host "✓ System Services: DHCP, RADIUS, Print services (internal networks only)"
              Write-Host "=== SECURITY MODEL ==="
              Write-Host "• Domain/Private profiles: Allow internal services"
              Write-Host "• Public profile: Block insecure protocols and database ports"
              Write-Host "• All custom rules organized into logical groups"
              Write-Host "• Built-in Windows rule groups enabled where appropriate"

              $totalCustomRules = 0
              foreach ($group in $customGroups) {
                  $groupCount = ($existingRules | Where-Object { $_.Group -eq $group }).Count
                  $totalCustomRules += $groupCount
              }
              Write-Host "• Total custom firewall rules: $totalCustomRules"
              exit 0
          } else {
              Write-Error "VALIDATION FAILED: Comprehensive firewall configuration errors found:"
              foreach ($error in $validationErrors) {
                  Write-Error "  - $error"
              }
              exit 1
          }
