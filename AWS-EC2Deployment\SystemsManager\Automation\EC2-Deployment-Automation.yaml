schemaVersion: "0.3"
description: |-
  Comprehensive EC2 Deployment Automation Runbook
  This runbook orchestrates the complete deployment of EC2 instances from ImageBuilder recipes
  including configuration loading, AD object creation, instance deployment, domain join, 
  application installation, and security configuration.
assumeRole: '{{AutomationAssumeRole}}'
parameters:
  AutomationAssumeRole:
    default: ''
    description: (Optional) The ARN of the role that allows Automation to perform the actions on your behalf.
    type: String
  AssetOwner:
    description: (Required) Asset Owner (e.g., Sanlam, Santam, Retail Mass)
    type: String
  AppType:
    description: (Required) Application Type (e.g., Shared, MSSQL)
    type: String
  Client:
    description: (Required) Client identifier (e.g., SGT, SPF, SC, STM, etc.)
    type: String
  Environment:
    description: (Required) Environment (e.g., PRD, PPE, DEV)
    type: String
  OSVersion:
    description: (Required) OS Version (e.g., Windows Server 2022, Windows Server 2019)
    type: String
  ImageBuilderImageArn:
    description: (Required) ImageBuilder Image ARN to deploy from
    type: String
  InstanceType:
    description: (Required) EC2 Instance Type
    type: String
    default: 't3.medium'
  SubnetId:
    description: (Required) Subnet ID for instance deployment
    type: String
  SecurityGroupIds:
    description: (Required) Security Group IDs (comma-separated)
    type: String
  KeyName:
    description: (Required) EC2 Key Pair name
    type: String
  S3ConfigBucket:
    description: (Required) S3 bucket containing configuration files
    type: String
  SecretsManagerSecretArn:
    description: (Required) Secrets Manager secret ARN containing domain credentials
    type: String
  ComputerName:
    description: (Optional) Custom computer name. If not provided, will be auto-generated
    type: String
    default: ''
  IamInstanceProfile:
    description: (Optional) IAM Instance Profile for the EC2 instance
    type: String
    default: ''
  Region:
    description: (Optional) AWS Region
    type: String
    default: 'af-south-1'

mainSteps:
- name: loadConfiguration
  action: aws:executeScript
  description: Load and validate deployment configuration from S3
  inputs:
    Runtime: PowerShell 7.4
    Handler: loadConfig
    Script: |-
      function loadConfig {
        param($AssetOwner, $AppType, $Client, $Environment, $OSVersion, $S3ConfigBucket, $Region)
        
        try {
          # Load base configuration
          $baseConfigKey = "configs/base_config.json"
          $tempFile = [System.IO.Path]::GetTempFileName()
          
          aws s3 cp "s3://$S3ConfigBucket/$baseConfigKey" $tempFile --region $Region
          if ($LASTEXITCODE -ne 0) { throw "Failed to download base config" }
          
          $baseConfig = Get-Content $tempFile -Raw | ConvertFrom-Json
          Remove-Item $tempFile -Force
          
          # Validate inputs
          if ($AssetOwner -notin $baseConfig.ASSET_OWNER) {
            throw "Invalid Asset Owner: $AssetOwner"
          }
          
          $assetConfig = $baseConfig.$AssetOwner
          if ($AppType -notin $assetConfig.APP_TYPE) { throw "Invalid App Type: $AppType" }
          if ($Environment -notin $assetConfig.ENV) { throw "Invalid Environment: $Environment" }
          if ($OSVersion -notin $assetConfig.OS_VERSION) { throw "Invalid OS Version: $OSVersion" }
          if ($Client -notin $assetConfig.CLIENT) { throw "Invalid Client: $Client" }
          
          # Load specific configuration
          $specificConfigKey = "configs/${AssetOwner}_${AppType}_Config.json"
          $tempFile2 = [System.IO.Path]::GetTempFileName()
          
          aws s3 cp "s3://$S3ConfigBucket/$specificConfigKey" $tempFile2 --region $Region
          if ($LASTEXITCODE -ne 0) { throw "Failed to download specific config" }
          
          $specificConfig = Get-Content $tempFile2 -Raw | ConvertFrom-Json
          Remove-Item $tempFile2 -Force
          
          # Extract configuration
          $clientConfig = $specificConfig.$Client
          if (-not $clientConfig) { throw "Client $Client not found" }
          
          $envConfig = $clientConfig.$Environment
          if (-not $envConfig) { throw "Environment $Environment not found" }
          
          $osConfig = $envConfig.OS_VERSIONS.$OSVersion
          if (-not $osConfig) { throw "OS Version $OSVersion not found" }
          
          $finalConfig = @{
            AssetOwner = $AssetOwner
            AppType = $AppType
            Client = $Client
            Environment = $Environment
            OSVersion = $OSVersion
            Domain = $envConfig.DOMAIN
            BasePath = $envConfig.BASE_PATH
            LocalAdmins = $envConfig.LOCAL_ADM
            TargetOU = $osConfig.OU
          }
          
          return ($finalConfig | ConvertTo-Json -Depth 5)
          
        } catch {
          throw "Configuration loading failed: $($_.Exception.Message)"
        }
      }
    InputPayload:
      AssetOwner: '{{AssetOwner}}'
      AppType: '{{AppType}}'
      Client: '{{Client}}'
      Environment: '{{Environment}}'
      OSVersion: '{{OSVersion}}'
      S3ConfigBucket: '{{S3ConfigBucket}}'
      Region: '{{Region}}'
  timeoutSeconds: 300
  nextStep: createADObject
  outputs:
  - Name: ConfigurationResult
    Selector: $.Payload
    Type: String

- name: createADObject
  action: aws:executeScript
  description: Create AD computer object via on-premises webhook API
  inputs:
    Runtime: PowerShell 7.4
    Handler: createADObject
    Script: |-
      function createADObject {
        param($ConfigJson, $ComputerName, $Region, $SecretsManagerSecretArn)

        try {
          # Load System.Web for URL encoding
          Add-Type -AssemblyName System.Web
          $config = $ConfigJson | ConvertFrom-Json

          # Get webhook credentials from Secrets Manager
          $secretValue = Get-SECSecretValue -SecretId $SecretsManagerSecretArn -Region $Region
          $secretJson = $secretValue.SecretString | ConvertFrom-Json

          # Determine computer name
          $computerName = if ($ComputerName -and $ComputerName -ne "") {
            $ComputerName
          } else {
            "$($config.AssetOwner)-$($config.Client)-$($config.AppType)-$(Get-Date -Format 'yyyyMMdd-HHmm')"
          }

          # Generate job ID for tracking
          $jobId = "AWS-SSM-$(Get-Date -Format 'yyyyMMdd-HHmmss')"

          # Create description
          $description = "AWS EC2 - $($config.AssetOwner) $($config.Client) $($config.AppType) $($config.Environment)"

          # Get webhook configuration from secrets
          $apiKey = $secretJson.webhookApiKey
          $webhookBaseUrl = $secretJson.webhookApiUrl
          $scriptName = "Create-ADObject.ps1"

          # Validate webhook configuration
          if (-not $apiKey -or -not $webhookBaseUrl) {
            throw "Webhook API key or URL not found in Secrets Manager. Please ensure 'webhookApiKey' and 'webhookApiUrl' are configured."
          }

          # URL encode the parameters
          $encodedParams = [System.Web.HttpUtility]::UrlEncode("-jobId '$jobId' -objectName '$computerName' -objectDescription '$description' -vmOS 'Windows' -domain '$($config.Domain)' -ouPath '$($config.TargetOU)' -appType '$($config.AppType)'")

          # Build the webhook URL
          $webhookUrl = "$webhookBaseUrl?key=$apiKey&script=$scriptName&param=$encodedParams"

          Write-Output "Calling AD creation webhook for computer: $computerName"
          Write-Output "Target OU: $($config.TargetOU)"
          Write-Output "Job ID: $jobId"

          # Call the webhook API
          try {
            $response = Invoke-RestMethod -Uri $webhookUrl -Method GET -TimeoutSec 120
            Write-Output "Raw webhook response: $($response | ConvertTo-Json -Depth 10)"

            # Parse the response structure
            if ($response.output) {
              $apiResult = $response.output

              if ($apiResult.success -eq $true -and $apiResult.status -eq "COMPLETED") {
                Write-Output "AD Object created successfully"
                Write-Output "  Computer Name: $($apiResult.data.objectName)"
                Write-Output "  Domain: $($apiResult.data.Domain)"
                Write-Output "  OU: $($apiResult.data.ouCreatedIn)"
                Write-Output "  Timestamp: $($apiResult.data.timeStamp)"
                Write-Output "  Message: $($apiResult.message)"
                $action = "CREATED_VIA_API"
                $apiSuccess = $true
                $apiMessage = $apiResult.message
                $actualOU = $apiResult.data.ouCreatedIn
              } else {
                Write-Warning "AD Object creation failed or incomplete"
                Write-Warning "  Status: $($apiResult.status)"
                Write-Warning "  Message: $($apiResult.message)"
                Write-Output "Using default OU for domain join - AD object will be created during join process"

                # Get default OU from secrets manager or use fallback
                $defaultOU = if ($secretJson.defaultTargetOU) {
                  $secretJson.defaultTargetOU
                } else {
                  $config.TargetOU
                }

                Write-Output "Default OU for domain join: $defaultOU"
                $action = "API_FAILED_USING_DEFAULT"
                $apiSuccess = $false
                $apiMessage = $apiResult.message
                $actualOU = $defaultOU
              }
            } else {
              Write-Warning "Unexpected response format from webhook API"
              Write-Output "Using default OU for domain join - AD object will be created during join process"

              # Get default OU from secrets manager or use fallback
              $defaultOU = if ($secretJson.defaultTargetOU) {
                $secretJson.defaultTargetOU
              } else {
                $config.TargetOU
              }

              Write-Output "Default OU for domain join: $defaultOU"
              $action = "API_FAILED_USING_DEFAULT"
              $apiSuccess = $false
              $apiMessage = "Unexpected response format"
              $actualOU = $defaultOU
            }
          } catch {
            # If webhook fails, use default OU for domain join
            Write-Warning "Webhook API call failed: $($_.Exception.Message)"
            Write-Output "Using default OU for domain join - AD object will be created during join process"

            # Get default OU from secrets manager or use fallback
            $defaultOU = if ($secretJson.defaultTargetOU) {
              $secretJson.defaultTargetOU
            } else {
              $config.TargetOU
            }

            Write-Output "Default OU for domain join: $defaultOU"
            $action = "API_FAILED_USING_DEFAULT"
            $apiSuccess = $false
            $apiMessage = $_.Exception.Message
            $actualOU = $defaultOU
          }

          $result = @{
            ComputerName = $computerName
            Action = $action
            TargetOU = $config.TargetOU
            ActualOU = $actualOU
            Domain = $config.Domain
            JobId = $jobId
            WebhookUrl = $webhookUrl
            Description = $description
            ApiSuccess = $apiSuccess
            ApiMessage = $apiMessage
            Timestamp = if ($apiResult.data.timeStamp) { $apiResult.data.timeStamp } else { Get-Date -Format "yyyy-MM-dd HH:mm:ss" }
          }

          return ($result | ConvertTo-Json -Depth 5)

        } catch {
          throw "AD object creation failed: $($_.Exception.Message)"
        }
      }
    InputPayload:
      ConfigJson: '{{loadConfiguration.ConfigurationResult}}'
      ComputerName: '{{ComputerName}}'
      Region: '{{Region}}'
      SecretsManagerSecretArn: '{{SecretsManagerSecretArn}}'
  timeoutSeconds: 600
  nextStep: deployEC2Instance
  outputs:
  - Name: ADObjectResult
    Selector: $.Payload
    Type: String

- name: deployEC2Instance
  action: aws:executeScript
  description: Deploy EC2 instance from ImageBuilder recipe
  inputs:
    Runtime: PowerShell 7.4
    Handler: deployInstance
    Script: |-
      function deployInstance {
        param($ImageBuilderImageArn, $InstanceType, $SubnetId, $SecurityGroupIds, $KeyName, $ConfigJson, $ADObjectJson, $IamInstanceProfile, $Region)

        try {
          $config = $ConfigJson | ConvertFrom-Json
          $adResult = $ADObjectJson | ConvertFrom-Json

          # Get AMI from ImageBuilder
          $imageDetails = aws imagebuilder get-image --image-build-version-arn $ImageBuilderImageArn --region $Region --output json | ConvertFrom-Json
          if ($LASTEXITCODE -ne 0) { throw "Failed to get ImageBuilder image details" }

          $amiId = $null
          foreach ($resource in $imageDetails.image.outputResources.amis) {
            if ($resource.region -eq $Region) {
              $amiId = $resource.image
              break
            }
          }

          if (-not $amiId) { throw "No AMI found for region $Region" }

          # Create user data script for domain join
          $userDataScript = "<powershell>`n# Set computer name if different from AD object`n`$adComputerName = `"$($adResult.ComputerName)`"`n`$currentName = `$env:COMPUTERNAME`n`nif (`$currentName -ne `$adComputerName) {`n  Rename-Computer -NewName `$adComputerName -Force`n  Restart-Computer -Force`n}`n`n# Download and execute domain join script`n`$scriptPath = `"C:\Temp\ServerInstalls\Join-DomainWithLocalAdmins.ps1`"`nif (-not (Test-Path `"C:\Temp\ServerInstalls`")) { New-Item -Path `"C:\Temp\ServerInstalls`" -ItemType Directory -Force }`n`naws s3 cp `"s3://$S3ConfigBucket/scripts/Join-DomainWithLocalAdmins.ps1`" `$scriptPath --region $Region`n`nif (Test-Path `$scriptPath) {`n  & `$scriptPath -Domain `"$($config.Domain)`" -SecretsManagerSecretArn `"$SecretsManagerSecretArn`" -LocalAdminGroups @(`"$($config.LocalAdmins -join '","')`") -ComputerName `$adComputerName -TargetOU `"$($config.TargetOU)`" -Region `"$Region`" -RestartAfterJoin`n}`n</powershell>"

          # Encode user data
          $userDataBytes = [System.Text.Encoding]::UTF8.GetBytes($userDataScript)
          $userDataBase64 = [System.Convert]::ToBase64String($userDataBytes)

          # Create tags
          $tags = @(
            @{ Key = "Name"; Value = $adResult.ComputerName },
            @{ Key = "AssetOwner"; Value = $config.AssetOwner },
            @{ Key = "AppType"; Value = $config.AppType },
            @{ Key = "Client"; Value = $config.Client },
            @{ Key = "Environment"; Value = $config.Environment },
            @{ Key = "OSVersion"; Value = $config.OSVersion },
            @{ Key = "Domain"; Value = $config.Domain },
            @{ Key = "TargetOU"; Value = $config.TargetOU },
            @{ Key = "AutoDomainJoin"; Value = "true" },
            @{ Key = "CreatedBy"; Value = "SSM-Automation" },
            @{ Key = "ADObjectAction"; Value = $adResult.Action }
          )

          # Build launch parameters
          $launchParams = @(
            "--image-id", $amiId,
            "--count", "1",
            "--instance-type", $InstanceType,
            "--key-name", $KeyName,
            "--security-group-ids", $SecurityGroupIds,
            "--subnet-id", $SubnetId,
            "--user-data", $userDataBase64,
            "--region", $Region,
            "--output", "json"
          )

          if ($IamInstanceProfile -and $IamInstanceProfile -ne "") {
            $launchParams += @("--iam-instance-profile", "Name=$IamInstanceProfile")
          }

          $tagSpec = @{ ResourceType = "instance"; Tags = $tags }
          $tagSpecJson = ConvertTo-Json @($tagSpec) -Depth 10 -Compress
          $launchParams += @("--tag-specifications", $tagSpecJson)

          # Launch instance
          $launchResult = & aws ec2 run-instances @launchParams
          if ($LASTEXITCODE -ne 0) { throw "Failed to launch EC2 instance: $launchResult" }

          $instanceData = $launchResult | ConvertFrom-Json
          $instance = $instanceData.Instances[0]

          $result = @{
            InstanceId = $instance.InstanceId
            AMIId = $amiId
            ComputerName = $adResult.ComputerName
            State = $instance.State.Name
            PrivateIpAddress = $instance.PrivateIpAddress
            LaunchTime = $instance.LaunchTime
            UserDataApplied = $true
          }

          # Return result with InstanceId accessible for automation references
          return @{
            Payload = ($result | ConvertTo-Json -Depth 5)
            InstanceId = $instance.InstanceId
          }

        } catch {
          throw "EC2 deployment failed: $($_.Exception.Message)"
        }
      }
    InputPayload:
      ImageBuilderImageArn: '{{ImageBuilderImageArn}}'
      InstanceType: '{{InstanceType}}'
      SubnetId: '{{SubnetId}}'
      SecurityGroupIds: '{{SecurityGroupIds}}'
      KeyName: '{{KeyName}}'
      ConfigJson: '{{loadConfiguration.ConfigurationResult}}'
      ADObjectJson: '{{createADObject.ADObjectResult}}'
      IamInstanceProfile: '{{IamInstanceProfile}}'
      Region: '{{Region}}'
  timeoutSeconds: 1800
  nextStep: waitForInstanceRunning
  outputs:
  - Name: EC2DeploymentResult
    Selector: $.Payload
    Type: String
  - Name: InstanceId
    Selector: $.InstanceId
    Type: String

- name: waitForInstanceRunning
  action: aws:waitForAwsResourceProperty
  description: Wait for the new EC2 instance to be in running state
  inputs:
    Service: ec2
    Api: DescribeInstances
    InstanceIds:
    - '{{deployEC2Instance.InstanceId}}'
    PropertySelector: '$.Reservations[0].Instances[0].State.Name'
    DesiredValues:
    - running
  timeoutSeconds: 900
  nextStep: finalizeDeployment

- name: finalizeDeployment
  action: aws:createTags
  description: Tag the instance with deployment completion status
  inputs:
    ResourceIds:
    - '{{deployEC2Instance.InstanceId}}'
    ResourceType: EC2
    Tags:
    - Key: DeploymentStatus
      Value: Complete
    - Key: DeploymentDate
      Value: '{{global:DATE_TIME}}'
    - Key: AutomationExecutionId
      Value: '{{automation:EXECUTION_ID}}'
    - Key: ConfigurationLoaded
      Value: Success
    - Key: ADObjectCreated
      Value: Success
  isEnd: true
