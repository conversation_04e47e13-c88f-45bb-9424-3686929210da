from typing import Any
from typing import Annotated, Literal, Any
from fastapi import APIRouter, Request, Form, Query, Depends, HTTPException
from fastapi.responses import JSONResponse
from fastapi.encoders import jsonable_encoder
from datetime import datetime, date, time
import json
from app import logging
from pydantic import BaseModel, Field
# For HTML pages
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
# from starlette.exceptions import HTTPException as StarletteHTTPException
from wtforms import <PERSON><PERSON>ield, FieldList, FormField, SelectField, IntegerField
from wtforms.validators import DataRequired
from starlette.datastructures import FormData, UploadFile
# Import helpers
from app.core.helpers import key_exists, dictval2list, dct2df, recast_json
#import MariaDB connection
from app.core.database import dbQuery
from app.api.v1.uispecs import ProcessSpecs, UiSpecs
from app.api.v1.uioptions import UiInit, UiOptions
from app.api.v1.workers import getClient, getOStypes, getIpConfigs, getADConfigs, getHostingConfigs, getVcConfigs, getDeployment, getDcsRates, getVCompute, getVStorage, getVSpecs, getAnnotations, getResources

router = APIRouter()
templates = Jinja2Templates(directory="templates")

restart_time = datetime.now()
last_restart = str(restart_time).split(".")[0]

class stdResponse(BaseModel):
    status: str | None = None
    message: str | None = None
    data: str | None = None
    success: bool

class ClientRequest(BaseModel):
    client: str = Field(values=["SGT","Santam","Retail Mass","Glacier","Sanlam Corporate","MiWay Life","Safrican","Group Office","Centriq","CCC","SIG","BCXtest"])


class DeploymentRequest(BaseModel):
    aso_tag: str = Field(values=["SLM","STM","SRM","SKY"])
    app_type: str = Field(values=["Shared","Shell","Appliance","MSSQL"])
    deployment: str = Field(values=["Development","Load-Balanced","SQL-AlwaysOn","Active-Passive","SAN-Mirrored","Pre-Production","Proof-of-Concept","Standalone","Vmware-SRM","Vmware-VSAN","Prod-Simulation"])
         

class ADRequest(BaseModel):
    client_tag: str = Field(values=["SGT","STM","SRM","GLC","SC","MWL","SAF","SLM","CTQ","CCC","SIG"])
    app_type: str = Field(values=["Shared","Shell","Appliance","MSSQL"])
    os_version: str = Field(values=["Windows Server 2022","Linux","Other"])
    env_type: str = Field(values=["PRD","DEV","PPE"])

class IPAMRequest(BaseModel):
    aso_tag: str = Field(values=["SLM","STM","SRM","SKY"])
    app_type: str = Field(values=["Shared","Shell","Appliance","MSSQL"])
    os_type: str = Field(values=["Windows","Linux","Other"])
    sla_type: str = Field(values=["PROD","NPRD","POC","COMMON","VMWARE"])


class HostingRequest(BaseModel):
    aso_tag: str = Field(values=["SLM","STM","SRM","SKY"])
    app_type: str = Field(values=["Shared","Shell","Appliance","MSSQL"])
    os_type: str = Field(values=["Windows","Linux"])
    sla_type: str = Field(values=["PROD","NPRD","POC"])
    
class DcsRatesRequest(BaseModel):
    aso_tag: str = Field(values=["SLM","STM","SRM","SKY"])
    os_type: str = Field(values=["Windows","Linux"])
    fy_period: str = Field(values=["2025","2024","2023"])

class DcsVmSpecs(BaseModel):
    vm: str = Field(values=["srv007775","poc000123","sdv12345"])

# Values to populate drop downs
""" ui_options = {
    "clients": ["SGT","Santam","Retail Mass","Sanlam Corporate","Glacier","Group Office","MiWay Life","Safrican","CCC","Centriq","SanlamLife","Afrocentric"],
    "deployments": ["Load-Balanced","SQL-AlwaysOn","Active-Passive","Vmware-SRM","Veritas-Replication","SAN-Mirrored","VM-Replication","Standalone","Acceptance","Development","Integration","Maintenance","Pre-Production","Performance","Testing","Prod-Simulation","Proof-of-Concept","Virtual-Desktop"],
    "platform": ["VMware","AWS"],
    "zone": ["BDC","CDC","AZ1","AZ2","AZ3"],
    "app_type": ["Shared","MSSQL","Shell","Appliance","Common","OpenEdge","VDI"],
    "os_type": ["Windows","Linux"]
} """

ui_objects = {
    "object_types": ["SRV","POC","CLS","LST"],
    "app_types": ["Shared","MSSQL","Shell","Appliance","Common","OpenEdge","VDI"],
    "os_types": ["Windows","Linux"]
}

async def get_config_form(request: Request):
    content_type = request.headers.get('Content-Type')
    if content_type is None:
        raise HTTPException(status_code=400, detail='No Content-Type provided!')
    elif (content_type == 'application/x-www-form-urlencoded' or
          content_type.startswith('multipart/form-data')):
        try:
            return await request.form()
        except Exception:
            raise HTTPException(status_code=400, detail='Invalid Form data')
    else:
        raise HTTPException(status_code=400, detail='Content-Type not supported!')


# Display landing page
@router.get("/cpsops", response_class=HTMLResponse)
async def read_root(request: Request):    
    ui_options = UiInit()
    print(f"UI INIT: {ui_options}\n")
    server_data = {"messages": []}    
    return templates.TemplateResponse("cpsops.html", {"request": request, "ui_options": ui_options, "ui_objects": ui_objects, "server_data": server_data})

# GET CLIENT from worker tasks
@router.get("/clients",summary="Clients Info URL GET Params", tags=["configs"], response_model=stdResponse) # 
async def getClientRequest(request: ClientRequest): #Annotated[ClientRequest, Query()]):
    params = dict(request.query_params)
    print(f"URL PARAMS: {params}")
    logging.info(f"Client URL Params received: {params}")
    response = getClient(params) #ProcessFilters(params)
    # response = params
    return JSONResponse(content=jsonable_encoder(response))

# UI Options POST endpoint from worker tasks
@router.post("/clients/",summary="Test URL POST Body (Payload)", tags=["configs"], response_model=stdResponse) # 
async def postClientRequest(request: ClientRequest):
    """Return payload received in POST Body"""
    params = dict(request)
    logging.info(f"POST payload body received: {params}")
    response = getClient(params) #params #ProcessFilters(params)
    # response = params
    return JSONResponse(content=jsonable_encoder(response))


# GET CLIENT from worker tasks
@router.get("/deployments/",summary="Deployment Info URL GET Params", tags=["configs"], response_model=stdResponse) # 
async def getDeploymentRequest(request: Request): #  Annotated[IPAMRequest, Query()]):
    logging.info(f"DEPLOYMENT CONFIG API GET Request!")
    params = dict(request.query_params)
    print(f"URL PARAMS: {params}")
    logging.info(f"AD CONFIG URL Params received: {params}")
    response = getDeployment(params) #ProcessFilters(params)
    # response = params
    return JSONResponse(content=jsonable_encoder(response))

# UI Options POST endpoint from worker tasks
@router.post("/deployments/",summary="Deployments Info URL POST Body (Payload)", tags=["configs"], response_model=stdResponse) # 
async def postDeploymentRequest(request: DeploymentRequest):
    """Return payload received in POST Body"""
    params = dict(request)
    logging.info(f"POST payload body received: {params}")
    response = getDeployment(params) #params #ProcessFilters(params)
    # response = params
    return JSONResponse(content=jsonable_encoder(response))

# GET AD CONFIGs from worker tasks
@router.get("/adconfigs/",summary="AD Domain Info URL GET Params", tags=["configs"], response_model=stdResponse) # 
async def getADRequest(request: Request): #  Annotated[IPAMRequest, Query()]):
    logging.info(f"AD CONFIG API GET Request!")
    params = dict(request.query_params)
    print(f"URL PARAMS: {params}")
    logging.info(f"AD CONFIG URL Params received: {params}")
    response = getADConfigs(params) #ProcessFilters(params)
    # response = params
    return JSONResponse(content=jsonable_encoder(response))

# POST AD from worker tasks
@router.post("/adconfigs/",summary="AD Domain Info URL POST Params", tags=["configs"], response_model=stdResponse) # 
async def postADRequest(request: ADRequest):
    """Return payload received in POST Body"""
    logging.info(f"AD CONFIGs API POST Request!")
    params = dict(request)
    logging.info(f"POST payload body received: {params}")
    response = getADConfigs(params) #params #ProcessFilters(params)
    # response = params
    return JSONResponse(content=jsonable_encoder(response))


# GET IPAM from worker tasks
@router.get("/ipconfigs/",summary="IPAM Info URL GET Params", tags=["configs"], response_model=stdResponse) # 
async def getIpamRequest(request: Request): #  Annotated[IPAMRequest, Query()]):
    logging.info(f"IPAM CONFIGs API GET Request!")
    params = dict(request.query_params)
    print(f"URL PARAMS: {params}")
    logging.info(f"Client URL Params received: {params}")
    response = getIpConfigs(params) #ProcessFilters(params)
    # response = params
    return JSONResponse(content=jsonable_encoder(response))

# POST IPAM from worker tasks
@router.post("/ipconfigs/",summary="IPAM Info URL POST Body (Payload)", tags=["configs"], response_model=stdResponse) # 
async def postIpamRequest(request: IPAMRequest):
    """Return payload received in POST Body"""
    logging.info(f"IPAM CONFIGs API POST Request!")
    params = dict(request)
    logging.info(f"POST payload body received: {params}")
    response = getIpConfigs(params) #params #ProcessFilters(params)
    # response = params
    return JSONResponse(content=jsonable_encoder(response))


# GET AD CONFIGs from worker tasks
@router.get("/vcconfigs/",summary="VC Hosting Info URL GET Params", tags=["configs"], response_model=stdResponse) # 
async def getVCRequest(request: Request): #  Annotated[IPAMRequest, Query()]):
    logging.info(f"AD CONFIG API GET Request!")
    params = dict(request.query_params)
    print(f"URL PARAMS: {params}")
    logging.info(f"VC CONFIG URL Params received: {params}")
    response = getVcConfigs(params) #ProcessFilters(params)
    # response = params
    return JSONResponse(content=jsonable_encoder(response))


# POST VC CONFIGs from worker tasks
@router.post("/vcconfigs/",summary="VC Hosting Info URL POST Body (Payload)", tags=["configs"], response_model=stdResponse) # 
async def postIpamRequest(request: HostingRequest):
    """Return payload received in POST Body"""
    logging.info(f"VC CONFIGs API POST Request!")
    params = dict(request)
    logging.info(f"POST payload body received: {params}")
    response = getVcConfigs(params) #params #ProcessFilters(params)
    # response = params
    return JSONResponse(content=jsonable_encoder(response))


# GET IPAM from worker tasks
@router.get("/dcsrates/",summary="DCS Rates Info URL GET Params", tags=["configs"], response_model=stdResponse) # 
async def getDcsRatesRequest(request: Request): #  Annotated[IPAMRequest, Query()]):
    logging.info(f"DCS RATES API GET Request!")
    params = dict(request.query_params)
    print(f"URL PARAMS: {params}")
    logging.info(f"Client URL Params received: {params}")
    response = getDcsRates(params) #ProcessFilters(params)
    # response = params
    return JSONResponse(content=jsonable_encoder(response))

# POST DCSRates from worker tasks
@router.post("/dcsrates/",summary="DCS Rates URL POST Body (Payload)", tags=["configs"], response_model=stdResponse) # 
async def postDcsRatesRequest(request: DcsRatesRequest): # DcsRatesRequest
    """Return DCS Rates based on payload received in POST Body"""
    logging.info(f"DCS RATES API POST Request!")
    params = dict(request)
    logging.info(f"POST payload body received: {params}")
    response = getDcsRates(params)
    # response = params
    return JSONResponse(content=jsonable_encoder(response))


# GET VM Specs with GET
@router.get("/vms/",summary="VM Info URL GET Params", tags=["compute"], response_model=stdResponse) # 
async def getVmRequest(request: Request):
    params = dict(request.query_params)
    print(f"URL PARAMS: {params}")
    logging.info(f"VM Specs GET query Params received: {params}")
    response = getVSpecs(params) #params #ProcessFilters(params)
    # response = params
    return JSONResponse(content=jsonable_encoder(response))

# UI Options POST endpoint from worker tasks
@router.post("/vms/",summary="VM Info URL POST Body (Payload)", tags=["compute"], response_model=stdResponse) # 
async def postVmRequest(request: DcsVmSpecs):
    """Return payload received in POST Body"""
    params = dict(request)
    logging.info(f"POST payload body received: {params}")
    response = getVSpecs(params) #params #ProcessFilters(params)
    # response = params
    return JSONResponse(content=jsonable_encoder(response))

# GET VM Specs with GET
@router.get("/ebx/",summary="Server EBX Info URL GET Params", tags=["annotations"], response_model=stdResponse) # 
async def getEbxRequest(request: Request):
    params = dict(request.query_params)
    print(f"URL PARAMS: {params}")
    logging.info(f"Server Annotations query Params received: {params}")
    response = getAnnotations(params) #params #ProcessFilters(params)
    # response = params
    return JSONResponse(content=jsonable_encoder(response))


# GET VM Specs with GET
@router.get("/vmcompute/",summary="VM Compute URL GET Params", tags=["compute"], response_model=stdResponse) # 
async def getVmCopmute(request: Request):
    params = dict(request.query_params)
    print(f"URL PARAMS: {params}")
    logging.info(f"VM Compute query Params received: {params}")
    response = getVCompute(params) #params #ProcessFilters(params)
    # response = params
    return JSONResponse(content=jsonable_encoder(response))


# GET VM Specs with GET
@router.get("/vmdisks/",summary="VM Disks URL GET Params", tags=["compute"], response_model=stdResponse) # 
async def getVmCompute(request: Request):
    params = dict(request.query_params)
    print(f"URL PARAMS: {params}")
    logging.info(f"VM Disks query Params received: {params}")
    response = getVStorage(params) #params #ProcessFilters(params)
    # response = params
    return JSONResponse(content=jsonable_encoder(response))


# UI Selections POST endpoint from worker tasks
@router.post("/configs",summary="Retrieve Config Detail", tags=["configs"], response_model=stdResponse) # 
async def getConfigs(request: Request, body=Depends(get_config_form)):
    """Receive config request"""
    # params = await request.form() #
    params = jsonable_encoder(body)
    response = processConfigQuery(params)
    # if isinstance(body, FormData):  # if Form/File data received
    # date_requested = datetime.now()
    # specs = json.loads(body)
    logging.info(f"Config Request received:\n{params}\nConfig Query Response:\n{response}\n")
    # response = ProcessSpecs(specs['data']) #{"response":"User Selections Received!"}#
    # # response = {"status":"RECEIVED", "message": "Specifications Received", "data": params, "success": True}
    # logging.info(f"Config Request response: {response}")
    # response = params
    #return JSONResponse(content=jsonable_encoder(response))
    return templates.TemplateResponse("show_configs.html", 
            { "request": request, "response": response }) #JSONResponse(content=jsonable_encoder(params))})

# Prep Config Query
def prepClientQuery(dctTmp):
    qrytxt = f"select * from clients where lower(client) = lower('{dctTmp['client']}');"
    logging.info(f"QUERY - getClient: {qrytxt}")
    dfClientQry = recast_json(dbQuery(qrytxt))
    print(f"Client:\n{dfClientQry}\n")
    dctClient = dfClientQry.to_dict(orient='records')[0]
    dctOut = dctTmp.copy()
    dctOut.update({ "aso_tag": dctClient['aso_tag'], "client_tag": dctClient['client_tag']})
    print(f"Client:\n{dctOut}")
    return dctOut

# Process Config Query
def processConfigQuery(dctIn):
    # dctTmp = prepClientQuery(dctIn)
    # # Get Hosting Options
    # try:
    #     deployment = getDeployment(dctTmp) #[0] #getEnvirons(options) #
    #     dctTmp = {**dctTmp, **deployment['data']}
    #     # dctRes['data'] = dctHosting
    # except Exception as e:
    #     logging.warning(f"DEPLOYMENT EXCEPTION: {deployment['messages']}")
    #     logging.error(f"Exception: {deployment['messages']}")
    #     # resp = deployment #{'messages' : deployment['messages'], "data":deployment['data']}
    #     return deployment 
    
    resp = UiConfigs(dctIn)
    # if deployment['success']:
    #     resp['data'].update({"deployment": deployment})
    print(f"processConfigQuery -> RESP:\n{resp}")
    return resp

# Query DB for UI Options
def UiConfigs(options):
    print(f"UI Filters:\n{options}")
    messages = []
    dctRes = {}
    dctRes['success'] = True
    dctRes['status'] = "NORMAL"
    dctRes['messages'] = messages
    dctRes['data'] = {
        "client": {},
        "deployment": {},
        "hosting": {},
        "osconfig": {},
        "ipconfig": {}
    }

    if key_exists('client',options):
        try:
            dctClient = getClient(options)
            print(f"CLIENT:\n{dctClient}")
            if dctClient['success']:
                options = {**options, **dctClient['data']}
            # dctRes.update({'data':dctClient})
        except:
            dctRes['messages'] = dctClient['messages']
            # return {'message' : "Invalid client selected", "data":""}
            return dctRes
    else:
        messages.append("No client specified")
        dctRes['messages'] = messages
        dctRes['success'] = False
        # return {'message' : "No client specified", "data":""}
        return dctRes

    # deployment = {**options, **dctClient}    # merge Client query result with options
    print("UI DEPLOYMENT\n",options)

    if key_exists('deployment',options):
        try:
            dctDeployTypes = getDeployment(options) #[0] #getEnvirons(options) #
            # dctRes.append({'data':dctEnvs})
        except:
            return {'message' : "Invalid deployment or app/os type selection", "data":""}
    else:
        return {'message' : "No deployment specified", "data":""}
    
    options = {**options, **dctDeployTypes['data']}

    if key_exists('app_type',options):
        try:
            dctOStypes = getOStypes(options)
        except:
            return {'message' : "Invalid OS or App Type", "data":""}
    else:
        return {'message' : "No OS or App Type specified", "data":""}
    
    # dctRes['data'] = {**dctClient, **dctDeployTypes, **dctOStypes} #, **dctEnvs} #}
    # Get IP Options
    try:
        dctIpConfigs = getIpConfigs(options) #dctRes['data']) #[0] #getEnvirons(options) #
        # dctRes.append({'data':dctEnvs})
    except Exception as e:
        print(f"IP COnfig Exception:\n{e}")
        return {'message' : "Invalid selections or No IP configs defined!", "data":""}
    # Get Resource Options
    try:
        dctResources = getResources(options) #dctRes['data']) #[0] #getEnvirons(options) #
        # dctRes.append({'data':dctEnvs})
    except:
        return {'message' : "Invalid selections or No server resources defined!", "data":""}
    # Get Hosting Options
    try:
        # dctHosting = getVcConfigs(options) #dctRes['data']) #[0] #getEnvirons(options) #
        dctHosting = getHostingConfigs(options)
        # dctRes.append({'data':dctEnvs})
    except:
        return {'message' : "Invalid selections or No hosting defined!", "data":""}
    
    # dctOptions = {**dctClient, **dctDeployTypes, **dctOStypes, **dctResources, **dctIpConfigs, **dctHosting}
    # # print(json.dumps(dctOptions, indent=2),"\n")
    # dctRes['data'] = dictval2list(dctOptions,",") 
    dctResult= {}
    dctResult['data'] = {
        "client": dctClient,
        "deployment": dctDeployTypes,
        "hosting": dctHosting,
        "osconfig": dctOStypes,
        "ipconfig": dctIpConfigs,
        "resources": dctResources
        }
    # print(f"Results: {len(dctRes)}\n")
    # return dctRes
    return dctResult


# Display landing page
@router.get("/serverrequest", response_class=HTMLResponse)
async def read_root(request: Request):    
    ui_options = UiInit()
    print(f"UI INIT: {ui_options}\n")
    server_data = {"messages": []}    
    # Can use UI Option and UI Objects to pass display values into form
    #  "ui_options": ui_options, "ui_objects": ui_objects,
    return templates.TemplateResponse("server_request.html", {"request": request, "ui_options": ui_options, "server_data": server_data})

# Test POST endpoint from worker tasks
@router.post("/deploy",summary="Test URL POST Body (Payload)", tags=["test"], response_model=stdResponse) # 
# async def postTest(request: Request):
async def deployRequest(request: Request, body=Depends(get_config_form)):
    if isinstance(body, FormData):  # if Form/File data received
        print(f"Quote Request RAW:\n{body}")
        """Return payload received in POST Body"""
        params = body #await request.json() #json.loads(request.data)
        validation = validateDeployRequest(params)
        logging.info(f"POST TEST payload received:\n{params}")    
        response={"success": True, "messages": validation['messages'], 'validation': validation, "status": "OK", "data": params}
        resp_msg = (f"POST TEST payload Response:\n{response}")
        print(resp_msg)
        logging.warning(f"POST TEST payload Response:\n{resp_msg}")
        return JSONResponse(content=jsonable_encoder(response))


# Prep Deploy Request
def validateDeployRequest(dctIn):
    logging.info("Prepping Deployment Request")
    server_types = ['SRV','SDV','POC']
    nonserver_types = ['CLS','LST']
    messages = []
    validation = {
        "valid_request" : False,
        "messages" : messages,
        "deploy_server" : False,
        "create_object" : False,
        "update_annotations" : False
    }
    update_ebx = True if key_exists('update_ebx',dctIn) else False
    if dctIn['object_type'].upper() in server_types:
        validation.update({'deploy_server' : True })
        messages.append(f"Processing {dctIn['object_type'].upper()} request")
        if update_ebx:  
            messages.append("Cannot update existing annotations during new deployment")
        else:
            validation.update({'update_annotations' : True, "create_object" : True, "valid_request" : True })
            messages.append(f"Adding new annotations")
    elif dctIn['object_type'].upper() == "NONE" or int(dctIn['vm_count']) == 0:
        messages.append(f"{dctIn['vm_count']} {dctIn['object_type'].upper()} object(s) requested")
    if dctIn['object_type'].upper() in nonserver_types:        
        messages.append(f"Creating {dctIn['object_type'].upper()} object only, please also specify annotations")
        object_request = True
    
    validation.update({'messages' : messages })

    logging.warning(validation)

    return validation