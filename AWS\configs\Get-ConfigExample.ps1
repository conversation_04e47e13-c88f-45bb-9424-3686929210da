# Example PowerShell script for AWS SSM Runbook
# This demonstrates how to use the base_config.json to determine which specific config to load

param(
    [Parameter(Mandatory=$true)]
    [string]$AssetOwner,
    
    [Parameter(Mandatory=$true)]
    [string]$AppType,
    
    [Parameter(Mandatory=$true)]
    [string]$Client,
    
    [Parameter(Mandatory=$true)]
    [string]$Environment,
    
    [Parameter(Mandatory=$true)]
    [string]$OSVersion
)

# Function to get configuration from S3 or local path
function Get-ConfigFromSource {
    param(
        [string]$ConfigPath
    )
    
    try {
        if ($ConfigPath.StartsWith("s3://")) {
            # Download from S3
            $tempFile = [System.IO.Path]::GetTempFileName()
            aws s3 cp $ConfigPath $tempFile
            $content = Get-Content $tempFile -Raw | ConvertFrom-Json
            Remove-Item $tempFile -Force
            return $content
        } else {
            # Local file
            return Get-Content $ConfigPath -Raw | ConvertFrom-Json
        }
    }
    catch {
        Write-Error "Failed to load config from $ConfigPath : $_"
        return $null
    }
}

# Main logic
try {
    # Step 1: Load base configuration to validate inputs
    $baseConfigPath = "s3://your-bucket/configs/base_config.json"  # or local path
    $baseConfig = Get-ConfigFromSource -ConfigPath $baseConfigPath
    
    if (-not $baseConfig) {
        throw "Failed to load base configuration"
    }
    
    # Step 2: Validate Asset Owner
    if ($AssetOwner -notin $baseConfig.ASSET_OWNER) {
        throw "Invalid Asset Owner: $AssetOwner. Valid options: $($baseConfig.ASSET_OWNER -join ', ')"
    }
    
    # Step 3: Validate inputs against base config
    $assetConfig = $baseConfig.$AssetOwner
    
    if ($AppType -notin $assetConfig.APP_TYPE) {
        throw "Invalid App Type: $AppType for $AssetOwner. Valid options: $($assetConfig.APP_TYPE -join ', ')"
    }
    
    if ($Environment -notin $assetConfig.ENV) {
        throw "Invalid Environment: $Environment for $AssetOwner. Valid options: $($assetConfig.ENV -join ', ')"
    }
    
    if ($OSVersion -notin $assetConfig.OS_VERSION) {
        throw "Invalid OS Version: $OSVersion for $AssetOwner. Valid options: $($assetConfig.OS_VERSION -join ', ')"
    }
    
    if ($Client -notin $assetConfig.CLIENT) {
        throw "Invalid Client: $Client for $AssetOwner. Valid options: $($assetConfig.CLIENT -join ', ')"
    }
    
    # Step 4: Load specific configuration
    $specificConfigPath = "s3://your-bucket/configs/${AssetOwner}_${AppType}_Config.json"
    $specificConfig = Get-ConfigFromSource -ConfigPath $specificConfigPath
    
    if (-not $specificConfig) {
        throw "Failed to load specific configuration: $specificConfigPath"
    }
    
    # Step 5: Extract the required configuration
    $clientConfig = $specificConfig.$Client
    if (-not $clientConfig) {
        throw "Client $Client not found in configuration"
    }
    
    $envConfig = $clientConfig.$Environment
    if (-not $envConfig) {
        throw "Environment $Environment not found for client $Client"
    }
    
    $osConfig = $envConfig.OS_VERSIONS.$OSVersion
    if (-not $osConfig) {
        throw "OS Version $OSVersion not found for client $Client in environment $Environment"
    }
    
    # Step 6: Build final configuration object
    $finalConfig = @{
        AssetOwner = $AssetOwner
        AppType = $AppType
        Client = $Client
        Environment = $Environment
        OSVersion = $OSVersion
        Domain = $envConfig.DOMAIN
        BasePath = $envConfig.BASE_PATH
        LocalAdmins = $envConfig.LOCAL_ADM
        TargetOU = $osConfig.OU
    }
    
    # Output the configuration (this would be used by subsequent runbook steps)
    Write-Output "Configuration loaded successfully:"
    Write-Output ($finalConfig | ConvertTo-Json -Depth 3)
    
    # Return the config for use in runbook
    return $finalConfig
    
} catch {
    Write-Error "Configuration loading failed: $_"
    exit 1
}
