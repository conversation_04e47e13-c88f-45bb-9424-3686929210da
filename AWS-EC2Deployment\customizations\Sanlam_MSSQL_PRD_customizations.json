{"FirewallRules": [{"Name": "Allow SQL Server Default Instance", "Direction": "in", "Action": "allow", "Protocol": "tcp", "LocalPort": "1433", "Profile": "domain", "RemoteAddress": "10.0.0.0/8", "Description": "Allow SQL Server connections from internal networks"}, {"Name": "Allow SQL Server Browser", "Direction": "in", "Action": "allow", "Protocol": "udp", "LocalPort": "1434", "Profile": "domain", "RemoteAddress": "10.0.0.0/8", "Description": "Allow SQL Server Browser service"}, {"Name": "Allow RDP Inbound - Restricted", "Direction": "in", "Action": "allow", "Protocol": "tcp", "LocalPort": "3389", "Profile": "domain", "RemoteAddress": "**********/24", "Description": "Allow RDP from admin subnet only"}, {"Name": "Block All Other Inbound", "Direction": "in", "Action": "block", "Protocol": "any", "Profile": "any", "Description": "De<PERSON><PERSON> deny all other inbound traffic"}], "RegistrySettings": [{"Path": "HKLM:\\SOFTWARE\\Microsoft\\Microsoft SQL Server\\MSSQL15.MSSQLSERVER\\MSSQLServer", "Name": "LoginMode", "Value": 1, "Type": "DWord", "Description": "Set SQL Server to Windows Authentication only"}, {"Path": "HKLM:\\SOFTWARE\\Policies\\Microsoft\\Windows\\EventLog\\Security", "Name": "MaxSize", "Value": 1048576, "Type": "DWord", "Description": "Set Security log size to 1GB for production"}, {"Path": "HKLM:\\SOFTWARE\\Sanlam\\Deployment", "Name": "Environment", "Value": "PRD", "Type": "String", "Description": "Mark server as production environment"}, {"Path": "HKLM:\\SOFTWARE\\Sanlam\\Deployment", "Name": "BackupRequired", "Value": 1, "Type": "DWord", "Description": "Flag server for backup inclusion"}], "SoftwarePackages": [{"Name": "Sanlam Security Agent - Production", "S3Key": "security/sanlam-security-agent-prd-v2.1.msi", "FileName": "sanlam-security-agent-prd.msi", "TargetPath": "C:\\Temp\\ServerInstalls\\sanlam-security-agent-prd.msi", "InstallCommand": "msiexec.exe /i C:\\Temp\\ServerInstalls\\sanlam-security-agent-prd.msi /quiet /norestart ENVIRONMENT=PRD REPORTING_LEVEL=HIGH", "ValidateCommand": "Get-Service -Name 'SanlamSecurityAgent' -ErrorAction SilentlyContinue", "Description": "Production security monitoring with enhanced reporting"}, {"Name": "SQL Server Backup Encryption Certificate", "S3Key": "security/sql-backup-cert.pfx", "FileName": "sql-backup-cert.pfx", "TargetPath": "C:\\Temp\\ServerInstalls\\sql-backup-cert.pfx", "InstallCommand": "Import-PfxCertificate -FilePath C:\\Temp\\ServerInstalls\\sql-backup-cert.pfx -CertStoreLocation Cert:\\LocalMachine\\My -Password (ConvertTo-SecureString 'CertPassword' -AsPlainText -Force)", "ValidateCommand": "Get-ChildItem -Path Cert:\\LocalMachine\\My | Where-Object {$_.Subject -like '*SQL Backup*'}", "Description": "Certificate for SQL Server backup encryption"}], "Services": [{"Name": "<PERSON>pooler", "StartupType": "Disabled", "Status": "Stopped", "Description": "Disable print spooler - not needed on SQL servers"}, {"Name": "RemoteRegistry", "StartupType": "Disabled", "Status": "Stopped", "Description": "Disable remote registry for security"}, {"Name": "SQLSERVERAGENT", "StartupType": "Automatic", "Status": "Running", "Description": "Ensure SQL Server Agent is running"}, {"Name": "MSSQLSERVER", "StartupType": "Automatic", "Status": "Running", "Description": "Ensure SQL Server Database Engine is running"}], "AuditPolicies": [{"Category": "Logon/Logoff", "Subcategory": "Logon", "Setting": "Success,Failure", "Description": "Audit all logon attempts"}, {"Category": "Object Access", "Subcategory": "File System", "Setting": "Failure", "Description": "Audit failed file access attempts"}, {"Category": "Privilege Use", "Subcategory": "Sensitive Privilege Use", "Setting": "Success,Failure", "Description": "Audit sensitive privilege usage"}, {"Category": "System", "Subcategory": "Security System Extension", "Setting": "Success,Failure", "Description": "Audit security system changes"}], "LocalSecurityPolicies": [{"Policy": "SeRemoteInteractiveLogonRight", "Accounts": ["Domain Admins", "SQL Admins"], "Description": "Restrict RDP access to SQL and Domain admins only"}, {"Policy": "SeDenyNetworkLogonRight", "Accounts": ["Guest", "Everyone"], "Description": "Deny network logon for Guest and Everyone"}], "PowerShellExecutionPolicy": {"Scope": "LocalMachine", "ExecutionPolicy": "AllSigned", "Description": "Production requires all scripts to be signed"}}