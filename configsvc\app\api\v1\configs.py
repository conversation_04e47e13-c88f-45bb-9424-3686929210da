from typing import Any
from typing import Annotated, Literal, Any
from fastapi import APIRouter, Request, Query
from fastapi.responses import JSONResponse
from fastapi.encoders import jsonable_encoder
from datetime import datetime, date, time
import json
from app import logging
from pydantic import BaseModel, Field

# from app.api.v1.uioptions import UiInit, ProcessFilters
# from app.api.v1.uirequests import ValidateRequest #, ProcessRequest
# from app.api.v1.uispecs import ProcessSpecs, UiSpecs
from app.api.v1.workers import getClient, getIpConfigs, getADConfigs, getVcConfigs, getDeployment, getDcsRates, getVCompute, getVStorage, getVSpecs, getAnnotations

router = APIRouter()

restart_time = datetime.now()
last_restart = str(restart_time).split(".")[0]

class stdResponse(BaseModel):
    status: str | None = None
    message: str | None = None
    data: str | None = None
    success: bool

class ClientRequest(BaseModel):
    client: str = Field(values=["SGT","Santam","Retail Mass","Glacier","Sanlam Corporate","MiWay Life","Safrican","Group Office","Centriq","CCC","SIG","BCXtest"])


class DeploymentRequest(BaseModel):
    aso_tag: str = Field(values=["SLM","STM","SRM","SKY"])
    app_type: str = Field(values=["Shared","Shell","Appliance","MSSQL"])
    deployment: str = Field(values=["Development","Load-Balanced","SQL-AlwaysOn","Active-Passive","SAN-Mirrored","Pre-Production","Proof-of-Concept","Standalone","Vmware-SRM","Vmware-VSAN","Prod-Simulation"])
         

class ADRequest(BaseModel):
    client_tag: str = Field(values=["SGT","STM","SRM","GLC","SC","MWL","SAF","SLM","CTQ","CCC","SIG"])
    app_type: str = Field(values=["Shared","Shell","Appliance","MSSQL"])
    os_version: str = Field(values=["Windows Server 2022","Linux","Other"])
    env_type: str = Field(values=["PRD","DEV","PPE"])

class IPAMRequest(BaseModel):
    aso_tag: str = Field(values=["SLM","STM","SRM","SKY"])
    app_type: str = Field(values=["Shared","Shell","Appliance","MSSQL"])
    os_type: str = Field(values=["Windows","Linux","Other"])
    sla_type: str = Field(values=["PROD","NPRD","POC","COMMON","VMWARE"])


class HostingRequest(BaseModel):
    aso_tag: str = Field(values=["SLM","STM","SRM","SKY"])
    app_type: str = Field(values=["Shared","Shell","Appliance","MSSQL"])
    os_type: str = Field(values=["Windows","Linux"])
    sla_type: str = Field(values=["PROD","NPRD","POC"])
    
class DcsRatesRequest(BaseModel):
    aso_tag: str = Field(values=["SLM","STM","SRM","SKY"])
    os_type: str = Field(values=["Windows","Linux"])
    fy_period: str = Field(values=["2025","2024","2023"])
         
    
class DcsVmSpecs(BaseModel):
    vm: str = Field(values=["srv007775","poc000123","sdv12345"])

# Test GET endpoint from worker tasks
@router.get("/test/",summary="Test URL GET Params", tags=["test"], response_model=stdResponse) # 
async def getTest(request: Request):
    params = request.query_params
    logging.info(f"URL Params received: {params}")
    response = params #ProcessFilters(params)
    # response = params
    return JSONResponse(content=jsonable_encoder(response))

# Test POST endpoint from worker tasks
@router.post("/test/",summary="Test URL POST Body (Payload)", tags=["test"], response_model=stdResponse) # 
async def postTest(request: Request):
    """Return payload received in POST Body"""
    params = await request.json() #json.loads(request.data)
    logging.info(f"POST TEST payload received:\n{params}")    
    response={"success": True, "messages": "Test successful", "status": "OK", "data": params}
    resp_msg = (f"POST TEST payload Response:\n{response}")
    print(resp_msg)
    logging.warning(f"POST TEST payload Response:\n{resp_msg}")
    return JSONResponse(content=jsonable_encoder(response))

# GET CLIENT from worker tasks
@router.get("/clients",summary="Clients Info URL GET Params", tags=["configs"], response_model=stdResponse) # 
async def getClientRequest(request: ClientRequest): #Annotated[ClientRequest, Query()]):
    params = dict(request.query_params)
    print(f"URL PARAMS: {params}")
    logging.info(f"Client URL Params received: {params}")
    response = getClient(params) #ProcessFilters(params)
    # response = params
    return JSONResponse(content=jsonable_encoder(response))

# UI Options POST endpoint from worker tasks
@router.post("/clients/",summary="Test URL POST Body (Payload)", tags=["configs"], response_model=stdResponse) # 
async def postClientRequest(request: ClientRequest):
    """Return payload received in POST Body"""
    params = dict(request)
    logging.info(f"POST payload body received: {params}")
    response = getClient(params) #params #ProcessFilters(params)
    # response = params
    return JSONResponse(content=jsonable_encoder(response))


# GET CLIENT from worker tasks
@router.get("/deployments/",summary="Deployment Info URL GET Params", tags=["configs"], response_model=stdResponse) # 
async def getDeploymentRequest(request: Request): #  Annotated[IPAMRequest, Query()]):
    logging.info(f"DEPLOYMENT CONFIG API GET Request!")
    params = dict(request.query_params)
    print(f"URL PARAMS: {params}")
    logging.info(f"AD CONFIG URL Params received: {params}")
    response = getDeployment(params) #ProcessFilters(params)
    # response = params
    return JSONResponse(content=jsonable_encoder(response))

# UI Options POST endpoint from worker tasks
@router.post("/deployments/",summary="Deployments Info URL POST Body (Payload)", tags=["configs"], response_model=stdResponse) # 
async def postDeploymentRequest(request: DeploymentRequest):
    """Return payload received in POST Body"""
    params = dict(request)
    logging.info(f"POST payload body received: {params}")
    response = getDeployment(params) #params #ProcessFilters(params)
    # response = params
    return JSONResponse(content=jsonable_encoder(response))



# GET AD CONFIGs from worker tasks
@router.get("/adconfigs/",summary="AD Domain Info URL GET Params", tags=["configs"], response_model=stdResponse) # 
async def getADRequest(request: Request): #  Annotated[IPAMRequest, Query()]):
    logging.info(f"AD CONFIG API GET Request!")
    params = dict(request.query_params)
    print(f"URL PARAMS: {params}")
    logging.info(f"AD CONFIG URL Params received: {params}")
    response = getADConfigs(params) #ProcessFilters(params)
    # response = params
    return JSONResponse(content=jsonable_encoder(response))


# POST AD from worker tasks
@router.post("/adconfigs/",summary="AD Domain Info URL POST Params", tags=["configs"], response_model=stdResponse) # 
async def postADRequest(request: ADRequest):
    """Return payload received in POST Body"""
    logging.info(f"AD CONFIGs API POST Request!")
    params = dict(request)
    logging.info(f"POST payload body received: {params}")
    response = getADConfigs(params) #params #ProcessFilters(params)
    # response = params
    return JSONResponse(content=jsonable_encoder(response))


# GET IPAM from worker tasks
@router.get("/ipconfigs/",summary="IPAM Info URL GET Params", tags=["configs"], response_model=stdResponse) # 
async def getIpamRequest(request: Request): #  Annotated[IPAMRequest, Query()]):
    logging.info(f"IPAM CONFIGs API GET Request!")
    params = dict(request.query_params)
    print(f"URL PARAMS: {params}")
    logging.info(f"Client URL Params received: {params}")
    response = getIpConfigs(params) #ProcessFilters(params)
    # response = params
    return JSONResponse(content=jsonable_encoder(response))

# POST IPAM from worker tasks
@router.post("/ipconfigs/",summary="IPAM Info URL POST Body (Payload)", tags=["configs"], response_model=stdResponse) # 
async def postIpamRequest(request: IPAMRequest):
    """Return payload received in POST Body"""
    logging.info(f"IPAM CONFIGs API POST Request!")
    params = dict(request)
    logging.info(f"POST payload body received: {params}")
    response = getIpConfigs(params) #params #ProcessFilters(params)
    # response = params
    return JSONResponse(content=jsonable_encoder(response))


# GET AD CONFIGs from worker tasks
@router.get("/vcconfigs/",summary="VC Hosting Info URL GET Params", tags=["configs"], response_model=stdResponse) # 
async def getVCRequest(request: Request): #  Annotated[IPAMRequest, Query()]):
    logging.info(f"AD CONFIG API GET Request!")
    params = dict(request.query_params)
    print(f"URL PARAMS: {params}")
    logging.info(f"VC CONFIG URL Params received: {params}")
    response = getVcConfigs(params) #ProcessFilters(params)
    # response = params
    return JSONResponse(content=jsonable_encoder(response))


# POST VC CONFIGs from worker tasks
@router.post("/vcconfigs/",summary="VC Hosting Info URL POST Body (Payload)", tags=["configs"], response_model=stdResponse) # 
async def postIpamRequest(request: HostingRequest):
    """Return payload received in POST Body"""
    logging.info(f"VC CONFIGs API POST Request!")
    params = dict(request)
    logging.info(f"POST payload body received: {params}")
    response = getVcConfigs(params) #params #ProcessFilters(params)
    # response = params
    return JSONResponse(content=jsonable_encoder(response))


# GET IPAM from worker tasks
@router.get("/dcsrates/",summary="DCS Rates Info URL GET Params", tags=["configs"], response_model=stdResponse) # 
async def getDcsRatesRequest(request: Request): #  Annotated[IPAMRequest, Query()]):
    logging.info(f"DCS RATES API GET Request!")
    params = dict(request.query_params)
    print(f"URL PARAMS: {params}")
    logging.info(f"Client URL Params received: {params}")
    response = getDcsRates(params) #ProcessFilters(params)
    # response = params
    return JSONResponse(content=jsonable_encoder(response))

# POST DCSRates from worker tasks
@router.post("/dcsrates/",summary="DCS Rates URL POST Body (Payload)", tags=["configs"], response_model=stdResponse) # 
async def postDcsRatesRequest(request: DcsRatesRequest): # DcsRatesRequest
    """Return DCS Rates based on payload received in POST Body"""
    logging.info(f"DCS RATES API POST Request!")
    params = dict(request)
    logging.info(f"POST payload body received: {params}")
    response = getDcsRates(params)
    # response = params
    return JSONResponse(content=jsonable_encoder(response))


# GET VM Specs with GET
@router.get("/vms/",summary="VM Info URL GET Params", tags=["compute"], response_model=stdResponse) # 
async def getVmRequest(request: Request):
    params = dict(request.query_params)
    print(f"URL PARAMS: {params}")
    logging.info(f"VM Specs GET query Params received: {params}")
    response = getVSpecs(params) #params #ProcessFilters(params)
    # response = params
    return JSONResponse(content=jsonable_encoder(response))

# UI Options POST endpoint from worker tasks
@router.post("/vms/",summary="VM Info URL POST Body (Payload)", tags=["compute"], response_model=stdResponse) # 
async def postVmRequest(request: DcsVmSpecs):
    """Return payload received in POST Body"""
    params = dict(request)
    logging.info(f"POST payload body received: {params}")
    response = getVSpecs(params) #params #ProcessFilters(params)
    # response = params
    return JSONResponse(content=jsonable_encoder(response))

# GET VM Specs with GET
@router.get("/ebx/",summary="Server EBX Info URL GET Params", tags=["annotations"], response_model=stdResponse) # 
async def getEbxRequest(request: Request):
    params = dict(request.query_params)
    print(f"URL PARAMS: {params}")
    logging.info(f"Server Annotations query Params received: {params}")
    response = getAnnotations(params) #params #ProcessFilters(params)
    # response = params
    return JSONResponse(content=jsonable_encoder(response))


# GET VM Specs with GET
@router.get("/vmcompute/",summary="VM Compute URL GET Params", tags=["compute"], response_model=stdResponse) # 
async def getVmCopmute(request: Request):
    params = dict(request.query_params)
    print(f"URL PARAMS: {params}")
    logging.info(f"VM Compute query Params received: {params}")
    response = getVCompute(params) #params #ProcessFilters(params)
    # response = params
    return JSONResponse(content=jsonable_encoder(response))


# GET VM Specs with GET
@router.get("/vmdisks/",summary="VM Disks URL GET Params", tags=["compute"], response_model=stdResponse) # 
async def getVmCompute(request: Request):
    params = dict(request.query_params)
    print(f"URL PARAMS: {params}")
    logging.info(f"VM Disks query Params received: {params}")
    response = getVStorage(params) #params #ProcessFilters(params)
    # response = params
    return JSONResponse(content=jsonable_encoder(response))
