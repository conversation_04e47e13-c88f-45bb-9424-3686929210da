# AWS Image Builder Component: Install PowerShell 7
# This component installs the latest PowerShell 7 on Windows Server

name: win-server-powershell7
description: Install PowerShell 7 (PowerShell Core) on Windows Server
schemaVersion: 1.0
phases:
  - name: build
    steps:
      - name: CheckExistingPowerShell7
        action: ExecutePowerShell
        onFailure: Continue
        inputs:
            commands:
              - |
                Write-Host "Checking for existing PowerShell 7 installation..."

                # Check if PowerShell 7 is already installed
                $pwsh7Path = "${env:ProgramFiles}\PowerShell\7\pwsh.exe"
                if (Test-Path $pwsh7Path) {
                    Write-Host "PowerShell 7 is already installed at: $pwsh7Path"
                    
                    # Get version information
                    try {
                        $version = & $pwsh7Path -Command '$PSVersionTable.PSVersion.ToString()'
                        Write-Host "Installed PowerShell 7 version: $version"
                        exit 0
                    } catch {
                        Write-Warning "Could not determine PowerShell 7 version: $($_.Exception.Message)"
                    }
                } else {
                    Write-Host "PowerShell 7 is not installed. Proceeding with installation..."
                }

      - name: DownloadPowerShell7
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "Downloading PowerShell 7..."

                # Create temp directory
                $tempDir = "C:\temp\ServerInstalls\pwsh7"
                if (!(Test-Path $tempDir)) {
                    New-Item -ItemType Directory -Path $tempDir -Force
                    Write-Host "Created temp directory: $tempDir"
                }

                # Get the latest PowerShell 7 release information from GitHub API
                try {
                    Write-Host "Fetching latest PowerShell 7 release information..."
                    $releaseInfo = Invoke-RestMethod -Uri "https://api.github.com/repos/PowerShell/PowerShell/releases/latest" -UseBasicParsing
                    
                    # Find the Windows x64 MSI installer
                    $asset = $releaseInfo.assets | Where-Object { $_.name -like "*win-x64.msi" } | Select-Object -First 1
                    
                    if ($asset) {
                        $downloadUrl = $asset.browser_download_url
                        $fileName = $asset.name
                        $installerPath = "$tempDir\$fileName"
                        
                        Write-Host "Downloading PowerShell 7 installer: $fileName"
                        Write-Host "Download URL: $downloadUrl"
                        
                        Invoke-WebRequest -Uri $downloadUrl -OutFile $installerPath -UseBasicParsing -TimeoutSec 600
                        
                        # Verify download
                        if ((Test-Path $installerPath) -and ((Get-Item $installerPath).Length -gt 50MB)) {
                            Write-Host "Download completed successfully"
                            Write-Host "Installer size: $([math]::Round((Get-Item $installerPath).Length / 1MB, 2)) MB"
                        } else {
                            throw "Downloaded file is missing or too small"
                        }
                    } else {
                        throw "Could not find Windows x64 MSI installer in release assets"
                    }
                } catch {
                    Write-Error "Failed to download PowerShell 7: $($_.Exception.Message)"
                    
                    # Fallback to direct download URL (may need updating)
                    Write-Host "Attempting fallback download..."
                    $fallbackUrl = "https://github.com/PowerShell/PowerShell/releases/download/v7.4.0/PowerShell-7.4.0-win-x64.msi"
                    $installerPath = "$tempDir\PowerShell-7.4.0-win-x64.msi"
                    
                    try {
                        Invoke-WebRequest -Uri $fallbackUrl -OutFile $installerPath -UseBasicParsing -TimeoutSec 600
                        Write-Host "Fallback download completed"
                    } catch {
                        Write-Error "Both download attempts failed: $($_.Exception.Message)"
                        exit 1
                    }
                }

      - name: InstallPowerShell7
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "Installing PowerShell 7..."

                $tempDir = "C:\temp\ServerInstalls\pwsh7"
                $installerPath = Get-ChildItem "$tempDir\*.msi" | Select-Object -First 1 -ExpandProperty FullName

                if (!(Test-Path $installerPath)) {
                    Write-Error "PowerShell 7 installer not found in temp directory"
                    exit 1
                }

                Write-Host "Installing from: $installerPath"
                Write-Host "This may take a few minutes..."

                # Install PowerShell 7 using msiexec
                $arguments = @(
                    "/i", "`"$installerPath`"",
                    "/quiet",
                    "/norestart",
                    "ADD_EXPLORER_CONTEXT_MENU_OPENPOWERSHELL=1",
                    "ADD_FILE_CONTEXT_MENU_RUNPOWERSHELL=1",
                    "ENABLE_PSREMOTING=1",
                    "REGISTER_MANIFEST=1",
                    "USE_MU=1",
                    "ENABLE_MU=1"
                )

                $process = Start-Process -FilePath "msiexec.exe" -ArgumentList $arguments -Wait -PassThru

                Write-Host "Installation process completed with exit code: $($process.ExitCode)"

                # MSI exit codes:
                # 0 = Success
                # 1641 = Success, reboot initiated
                # 3010 = Success, reboot required
                # Other = Error

                switch ($process.ExitCode) {
                    0 { 
                        Write-Host "PowerShell 7 installed successfully"
                    }
                    1641 { 
                        Write-Host "PowerShell 7 installed successfully (reboot initiated)"
                    }
                    3010 { 
                        Write-Host "PowerShell 7 installed successfully (reboot required)"
                    }
                    default { 
                        Write-Error "Installation failed with exit code: $($process.ExitCode)"
                        exit 1
                    }
                }

      - name: ConfigurePowerShell7
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "Configuring PowerShell 7..."

                # Verify installation
                $pwsh7Path = "${env:ProgramFiles}\PowerShell\7\pwsh.exe"
                if (Test-Path $pwsh7Path) {
                    Write-Host "PowerShell 7 executable found at: $pwsh7Path"
                    
                    # Get version information
                    try {
                        $version = & $pwsh7Path -Command '$PSVersionTable.PSVersion.ToString()'
                        Write-Host "Installed PowerShell 7 version: $version"
                    } catch {
                        Write-Warning "Could not get version information: $($_.Exception.Message)"
                    }
                    
                    # Add PowerShell 7 to system PATH if not already there
                    $currentPath = [Environment]::GetEnvironmentVariable("PATH", "Machine")
                    $pwshDir = "${env:ProgramFiles}\PowerShell\7"
                    
                    if ($currentPath -notlike "*$pwshDir*") {
                        Write-Host "Adding PowerShell 7 to system PATH..."
                        $newPath = "$currentPath;$pwshDir"
                        [Environment]::SetEnvironmentVariable("PATH", $newPath, "Machine")
                        Write-Host "PowerShell 7 added to system PATH"
                    } else {
                        Write-Host "PowerShell 7 is already in system PATH"
                    }
                    
                    # Create PowerShell 7 profile directory
                    try {
                        $profileDir = & $pwsh7Path -Command '$PROFILE | Split-Path'
                        if (!(Test-Path $profileDir)) {
                            New-Item -ItemType Directory -Path $profileDir -Force
                            Write-Host "Created PowerShell 7 profile directory: $profileDir"
                        }
                    } catch {
                        Write-Warning "Could not create profile directory: $($_.Exception.Message)"
                    }
                    
                } else {
                    Write-Error "PowerShell 7 executable not found after installation"
                    exit 1
                }

      - name: InstallCommonModules
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "Installing common PowerShell modules..."

                $pwsh7Path = "${env:ProgramFiles}\PowerShell\7\pwsh.exe"

                # List of useful modules to install
                $modules = @(
                    "Az",
                    "Microsoft.PowerShell.SecretManagement",
                    "Microsoft.PowerShell.SecretStore",
                    "PSReadLine",
                    "Terminal-Icons",
                    "oh-my-posh"
                )

                foreach ($module in $modules) {
                    Write-Host "Installing module: $module"
                    try {
                        & $pwsh7Path -Command "Install-Module -Name '$module' -Force -AllowClobber -Scope AllUsers -Repository PSGallery"
                        Write-Host "Successfully installed: $module"
                    } catch {
                        Write-Warning "Failed to install $module : $($_.Exception.Message)"
                    }
                }

      - name: Cleanup
        action: ExecutePowerShell
        onFailure: Continue
        inputs:
            commands:
              - |
                Write-Host "Cleaning up installation files..."

                $tempDir = "C:\temp\ServerInstalls\pwsh7"
                if (Test-Path $tempDir) {
                    try {
                        Remove-Item $tempDir -Recurse -Force
                        Write-Host "Cleanup completed successfully"
                    } catch {
                        Write-Warning "Failed to clean up temp directory: $($_.Exception.Message)"
                    }
                }

  - name: validate
    steps:
      - name: ValidatePowerShell7
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "Validating PowerShell 7 installation..."

                # Check if PowerShell 7 executable exists
                $pwsh7Path = "${env:ProgramFiles}\PowerShell\7\pwsh.exe"
                if (!(Test-Path $pwsh7Path)) {
                    Write-Error "VALIDATION FAILED: PowerShell 7 executable not found"
                    exit 1
                }

                # Test PowerShell 7 execution
                try {
                    $output = & $pwsh7Path -Command "Write-Output 'PowerShell 7 is working'; \$PSVersionTable"
                    Write-Host "PowerShell 7 execution test successful"
                    Write-Host "Output: $output"
                } catch {
                    Write-Error "VALIDATION FAILED: PowerShell 7 execution test failed: $($_.Exception.Message)"
                    exit 1
                }

                # Check if PowerShell 7 is in PATH
                $pathCheck = & where.exe pwsh 2>$null
                if ($pathCheck) {
                    Write-Host "SUCCESS: PowerShell 7 is accessible via PATH"
                } else {
                    Write-Warning "PowerShell 7 may not be in system PATH"
                }

                Write-Host "VALIDATION SUCCESS: PowerShell 7 is properly installed and functional"
