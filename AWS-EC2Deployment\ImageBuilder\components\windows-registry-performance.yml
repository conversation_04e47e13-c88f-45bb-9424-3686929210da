# AWS Image Builder Component: Configure Windows Performance Registry Settings
# This component configures Windows performance optimization registry settings for business environments

name: windows-registry-performance
description: Configure Windows performance optimization registry settings for server environments
schemaVersion: 1.0
phases:
  - name: build
    steps:
      - name: ConfigurePerformanceSettings
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "Configuring Windows performance optimization registry settings..."

                # Configure system performance settings
                Write-Host "Configuring system performance settings..."
                $perfPath = "HKLM:\SYSTEM\CurrentControlSet\Control\PriorityControl"

                # Optimize for background services (typical for servers)
                Set-ItemProperty -Path $perfPath -Name "Win32PrioritySeparation" -Value 24 -Type DWord

                # Configure memory management
                Write-Host "Configuring memory management settings..."
                $memPath = "HKLM:\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management"

                # Disable paging executive (if sufficient RAM available)
                Set-ItemProperty -Path $memPath -Name "DisablePagingExecutive" -Value 1 -Type DWord

                # Configure large system cache for file server workloads
                Set-ItemProperty -Path $memPath -Name "LargeSystemCache" -Value 1 -Type DWord

                # Configure I/O settings
                Write-Host "Configuring I/O performance settings..."
                $ioPath = "HKLM:\SYSTEM\CurrentControlSet\Services\LanmanServer\Parameters"

                # Optimize for throughput for file sharing
                Set-ItemProperty -Path $ioPath -Name "Size" -Value 3 -Type DWord

                # Configure network adapter settings
                Write-Host "Configuring network performance settings..."
                $netPath = "HKLM:\SYSTEM\CurrentControlSet\Services\Tcpip\Parameters"

                # Enable TCP window scaling
                Set-ItemProperty -Path $netPath -Name "Tcp1323Opts" -Value 3 -Type DWord

                # Configure TCP chimney offload (disable for compatibility)
                Set-ItemProperty -Path $netPath -Name "EnableTCPChimney" -Value 0 -Type DWord

                # Configure power management for performance
                Write-Host "Configuring power management for performance..."
                $powerPath = "HKLM:\SYSTEM\CurrentControlSet\Control\Power"

                # Set power scheme to high performance
                Set-ItemProperty -Path $powerPath -Name "CsEnabled" -Value 0 -Type DWord

                # Disable hibernation to save disk space
                Write-Host "Disabling hibernation to save disk space..."
                $hibernationPath = "HKLM:\SYSTEM\CurrentControlSet\Control\Power"
                Set-ItemProperty -Path $hibernationPath -Name "HibernateEnabled" -Value 0 -Type DWord

                # Configure visual effects for performance (disable unnecessary animations)
                Write-Host "Configuring visual effects for performance..."
                $visualPath = "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects"
                if (-not (Test-Path $visualPath)) {
                    New-Item -Path $visualPath -Force | Out-Null
                }
                Set-ItemProperty -Path $visualPath -Name "VisualFXSetting" -Value 2 -Type DWord  # Adjust for best performance

                # Configure Windows Search for minimal impact
                Write-Host "Configuring Windows Search for minimal performance impact..."
                $searchPath = "HKLM:\SOFTWARE\Microsoft\Windows Search"
                if (-not (Test-Path $searchPath)) {
                    New-Item -Path $searchPath -Force | Out-Null
                }
                Set-ItemProperty -Path $searchPath -Name "SetupCompletedSuccessfully" -Value 0 -Type DWord

                # Configure automatic maintenance
                Write-Host "Configuring automatic maintenance settings..."
                $maintPath = "HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Schedule\Maintenance"
                if (-not (Test-Path $maintPath)) {
                    New-Item -Path $maintPath -Force | Out-Null
                }
                # Schedule maintenance during off-hours (3 AM)
                Set-ItemProperty -Path $maintPath -Name "MaintenanceDisabled" -Value 0 -Type DWord

                # Configure disk defragmentation
                Write-Host "Configuring disk optimization settings..."
                $defragPath = "HKLM:\SOFTWARE\Microsoft\Dfrg\BootOptimizeFunction"
                if (-not (Test-Path $defragPath)) {
                    New-Item -Path $defragPath -Force | Out-Null
                }
                # Enable boot optimization
                Set-ItemProperty -Path $defragPath -Name "Enable" -Value "Y" -Type String

                Write-Host "Performance optimization registry settings configured successfully"

  - name: validate
    steps:
      - name: ValidatePerformanceConfiguration
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "Validating Windows performance registry configuration..."

                $validationErrors = @()

                # Validate system performance settings
                try {
                    $perfPath = "HKLM:\SYSTEM\CurrentControlSet\Control\PriorityControl"
                    $win32Priority = Get-ItemProperty -Path $perfPath -Name "Win32PrioritySeparation" -ErrorAction SilentlyContinue | Select-Object -ExpandProperty Win32PrioritySeparation
                    if ($win32Priority -ne 24) {
                        $validationErrors += "Win32PrioritySeparation should be 24 (background services), found: $win32Priority"
                    }
                }
                catch {
                    $validationErrors += "Failed to validate system performance settings: $($_.Exception.Message)"
                }

                # Validate memory management settings
                try {
                    $memPath = "HKLM:\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management"
                    $disablePaging = Get-ItemProperty -Path $memPath -Name "DisablePagingExecutive" -ErrorAction SilentlyContinue | Select-Object -ExpandProperty DisablePagingExecutive
                    if ($disablePaging -ne 1) {
                        $validationErrors += "DisablePagingExecutive should be 1 (disabled), found: $disablePaging"
                    }

                    $largeCache = Get-ItemProperty -Path $memPath -Name "LargeSystemCache" -ErrorAction SilentlyContinue | Select-Object -ExpandProperty LargeSystemCache
                    if ($largeCache -ne 1) {
                        $validationErrors += "LargeSystemCache should be 1 (enabled), found: $largeCache"
                    }
                }
                catch {
                    $validationErrors += "Failed to validate memory management settings: $($_.Exception.Message)"
                }

                # Validate I/O settings
                try {
                    $ioPath = "HKLM:\SYSTEM\CurrentControlSet\Services\LanmanServer\Parameters"
                    $size = Get-ItemProperty -Path $ioPath -Name "Size" -ErrorAction SilentlyContinue | Select-Object -ExpandProperty Size
                    if ($size -ne 3) {
                        $validationErrors += "LanmanServer Size should be 3 (maximize throughput), found: $size"
                    }
                }
                catch {
                    $validationErrors += "Failed to validate I/O settings: $($_.Exception.Message)"
                }

                # Validate network settings
                try {
                    $netPath = "HKLM:\SYSTEM\CurrentControlSet\Services\Tcpip\Parameters"
                    $tcp1323 = Get-ItemProperty -Path $netPath -Name "Tcp1323Opts" -ErrorAction SilentlyContinue | Select-Object -ExpandProperty Tcp1323Opts
                    if ($tcp1323 -ne 3) {
                        $validationErrors += "Tcp1323Opts should be 3 (window scaling enabled), found: $tcp1323"
                    }
                }
                catch {
                    $validationErrors += "Failed to validate network settings: $($_.Exception.Message)"
                }

                # Validate hibernation is disabled
                try {
                    $hibernationPath = "HKLM:\SYSTEM\CurrentControlSet\Control\Power"
                    $hibernation = Get-ItemProperty -Path $hibernationPath -Name "HibernateEnabled" -ErrorAction SilentlyContinue | Select-Object -ExpandProperty HibernateEnabled
                    if ($hibernation -ne 0) {
                        $validationErrors += "HibernateEnabled should be 0 (disabled), found: $hibernation"
                    }
                }
                catch {
                    $validationErrors += "Failed to validate hibernation settings: $($_.Exception.Message)"
                }

                # Report validation results
                if ($validationErrors.Count -eq 0) {
                    Write-Host "VALIDATION SUCCESS: Performance registry settings configured correctly"
                    Write-Host "- System: Optimized for background services"
                    Write-Host "- Memory: Paging executive disabled, large system cache enabled"
                    Write-Host "- I/O: Optimized for throughput"
                    Write-Host "- Network: TCP window scaling enabled"
                    Write-Host "- Power: Hibernation disabled for disk space savings"
                    exit 0
                } else {
                    Write-Error "VALIDATION FAILED: Performance registry configuration errors found:"
                    foreach ($error in $validationErrors) {
                        Write-Error "  - $error"
                    }
                    exit 1
                }
