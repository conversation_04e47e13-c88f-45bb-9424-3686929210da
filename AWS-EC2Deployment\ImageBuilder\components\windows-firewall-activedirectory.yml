# AWS Image Builder Component: Active Directory Windows Firewall Configuration
# This component configures Windows Firewall rules specifically for Active Directory Domain Services

name: windows-firewall-activedirectory
description: Configure Windows Firewall rules for Active Directory Domain Services
schemaVersion: 1.0
phases:
  - name: build
    steps:
      - name: ConfigureActiveDirectoryFirewallRules
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "=== CONFIGURING ACTIVE DIRECTORY FIREWALL RULES ==="

                # Create custom rule group for AD rules
                $adGroup = "Windows Server Active Directory"

                # LDAP (Port 389)
                Write-Host "Allowing LDAP (port 389)..."
                New-NetFirewallRule -DisplayName "LDAP" -Group $adGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 389 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow LDAP from internal networks"

                # LDAPS/Secure LDAP (Port 636)
                Write-Host "Allowing LDAPS (port 636)..."
                New-NetFirewallRule -DisplayName "LDAPS" -Group $adGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 636 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow secure LDAP from internal networks"

                # Global Catalog LDAP (Port 3268)
                Write-Host "Allowing Global Catalog LDAP (port 3268)..."
                New-NetFirewallRule -DisplayName "Global Catalog LDAP" -Group $adGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 3268 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow Global Catalog LDAP from internal networks"

                # Global Catalog LDAPS (Port 3269)
                Write-Host "Allowing Global Catalog LDAPS (port 3269)..."
                New-NetFirewallRule -DisplayName "Global Catalog LDAPS" -Group $adGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 3269 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow secure Global Catalog LDAP from internal networks"

                # Kerberos (Port 88)
                Write-Host "Allowing Kerberos (port 88)..."
                New-NetFirewallRule -DisplayName "Kerberos TCP" -Group $adGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 88 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow Kerberos TCP from internal networks"
                New-NetFirewallRule -DisplayName "Kerberos UDP" -Group $adGroup -Direction Inbound -Action Allow -Protocol UDP -LocalPort 88 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow Kerberos UDP from internal networks"

                # DNS (Port 53) - Required for AD domain services
                Write-Host "Allowing DNS (port 53)..."
                New-NetFirewallRule -DisplayName "DNS TCP" -Group $adGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 53 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow DNS TCP from internal networks"
                New-NetFirewallRule -DisplayName "DNS UDP" -Group $adGroup -Direction Inbound -Action Allow -Protocol UDP -LocalPort 53 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow DNS UDP from internal networks"

                # RPC Endpoint Mapper (Port 135)
                Write-Host "Allowing RPC Endpoint Mapper (port 135)..."
                New-NetFirewallRule -DisplayName "RPC Endpoint Mapper" -Group $adGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 135 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow RPC Endpoint Mapper from internal networks"

                # RPC Dynamic Ports (for AD replication and management)
                Write-Host "Allowing RPC Dynamic Ports for AD replication..."
                New-NetFirewallRule -DisplayName "RPC Dynamic Ports AD" -Group $adGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 1024-65535 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow RPC dynamic ports for AD replication from internal networks"

                # SMB/CIFS (Ports 445, 139)
                Write-Host "Allowing SMB/CIFS (ports 445, 139)..."
                New-NetFirewallRule -DisplayName "SMB" -Group $adGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 445 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow SMB from internal networks"
                New-NetFirewallRule -DisplayName "NetBIOS Session" -Group $adGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 139 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow NetBIOS Session from internal networks"

                # NetBIOS Name Service (Port 137 UDP)
                Write-Host "Allowing NetBIOS Name Service (port 137 UDP)..."
                New-NetFirewallRule -DisplayName "NetBIOS Name Service" -Group $adGroup -Direction Inbound -Action Allow -Protocol UDP -LocalPort 137 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow NetBIOS Name Service from internal networks"

                # NetBIOS Datagram Service (Port 138 UDP)
                Write-Host "Allowing NetBIOS Datagram Service (port 138 UDP)..."
                New-NetFirewallRule -DisplayName "NetBIOS Datagram Service" -Group $adGroup -Direction Inbound -Action Allow -Protocol UDP -LocalPort 138 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow NetBIOS Datagram Service from internal networks"

                # NTP (Port 123) - For time synchronization
                Write-Host "Allowing NTP (port 123)..."
                New-NetFirewallRule -DisplayName "NTP" -Group $adGroup -Direction Inbound -Action Allow -Protocol UDP -LocalPort 123 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow NTP from internal networks"

                # Enable built-in Active Directory firewall rules
                Write-Host "Enabling built-in Active Directory firewall rules..."
                $adRuleGroups = @(
                    "Active Directory Domain Services",
                    "Active Directory Domain Services (NP-In)",
                    "Active Directory Web Services (ADWS)",
                    "DFS Management",
                    "DFS Replication",
                    "DNS Service",
                    "Kerberos Key Distribution Center"
                )

                foreach ($ruleGroup in $adRuleGroups) {
                    try {
                        Enable-NetFirewallRule -DisplayGroup $ruleGroup -ErrorAction SilentlyContinue
                        Write-Host "Enabled firewall rule group: $ruleGroup"
                    }
                    catch {
                        Write-Host "Could not enable firewall rule group $ruleGroup`: $($_.Exception.Message)" -ForegroundColor Yellow
                    }
                }

                Write-Host "Active Directory firewall rules configured successfully"

  - name: validate
    steps:
      - name: ValidateActiveDirectoryFirewallConfiguration
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "=== VALIDATING ACTIVE DIRECTORY FIREWALL CONFIGURATION ==="

                $validationErrors = @()

                # Check if Active Directory custom rule group exists
                $existingRules = Get-NetFirewallRule | Where-Object { $_.Enabled -eq "True" }

                $adGroup = "Windows Server Active Directory"
                $adGroupRules = $existingRules | Where-Object { $_.Group -eq $adGroup }

                if ($adGroupRules.Count -eq 0) {
                    $validationErrors += "No enabled rules found for Active Directory group: $adGroup"
                } else {
                    Write-Host "Found $($adGroupRules.Count) enabled rules in Active Directory group"
                }

                # Check key Active Directory service rules
                $requiredADRules = @("LDAP", "LDAPS", "Kerberos TCP", "DNS TCP", "SMB", "RPC Endpoint Mapper")
                foreach ($rule in $requiredADRules) {
                    $foundRule = $existingRules | Where-Object { $_.DisplayName -eq $rule }
                    if (-not $foundRule) {
                        $validationErrors += "Missing Active Directory service rule: $rule"
                    }
                }

                # Validate specific AD port configurations
                try {
                    $ldapRule = Get-NetFirewallRule -DisplayName "LDAP" -ErrorAction SilentlyContinue
                    if ($ldapRule) {
                        $ldapPortFilter = Get-NetFirewallPortFilter -AssociatedNetFirewallRule $ldapRule
                        if ($ldapPortFilter.LocalPort -ne "389") {
                            $validationErrors += "LDAP rule should be on port 389, found: $($ldapPortFilter.LocalPort)"
                        }
                    }

                    $kerberosRule = Get-NetFirewallRule -DisplayName "Kerberos TCP" -ErrorAction SilentlyContinue
                    if ($kerberosRule) {
                        $kerberosPortFilter = Get-NetFirewallPortFilter -AssociatedNetFirewallRule $kerberosRule
                        if ($kerberosPortFilter.LocalPort -ne "88") {
                            $validationErrors += "Kerberos TCP rule should be on port 88, found: $($kerberosPortFilter.LocalPort)"
                        }
                    }

                    $dnsRule = Get-NetFirewallRule -DisplayName "DNS TCP" -ErrorAction SilentlyContinue
                    if ($dnsRule) {
                        $dnsPortFilter = Get-NetFirewallPortFilter -AssociatedNetFirewallRule $dnsRule
                        if ($dnsPortFilter.LocalPort -ne "53") {
                            $validationErrors += "DNS TCP rule should be on port 53, found: $($dnsPortFilter.LocalPort)"
                        }
                    }
                }
                catch {
                    $validationErrors += "Failed to validate Active Directory port configurations: $($_.Exception.Message)"
                }

                # Report validation results
                if ($validationErrors.Count -eq 0) {
                    Write-Host "VALIDATION SUCCESS: Active Directory firewall configuration completed successfully"
                    Write-Host "=== ACTIVE DIRECTORY SERVICES CONFIGURED ==="
                    Write-Host "LDAP/LDAPS: Ports 389, 636, 3268, 3269 (internal networks only)"
                    Write-Host "Kerberos: Port 88 TCP/UDP (internal networks only)"
                    Write-Host "DNS: Port 53 TCP/UDP (internal networks only)"
                    Write-Host "RPC: Port 135 + dynamic ports 1024-65535 (internal networks only)"
                    Write-Host "SMB/NetBIOS: Ports 445, 139, 137, 138 (internal networks only)"
                    Write-Host "NTP: Port 123 UDP (internal networks only)"
                    Write-Host "Built-in AD rule groups enabled"
                    Write-Host "Total Active Directory rules: $($adGroupRules.Count)"
                    exit 0
                } else {
                    Write-Error "VALIDATION FAILED: Active Directory firewall configuration errors found:"
                    foreach ($error in $validationErrors) {
                        Write-Error "  - $error"
                    }
                    exit 1
                }
