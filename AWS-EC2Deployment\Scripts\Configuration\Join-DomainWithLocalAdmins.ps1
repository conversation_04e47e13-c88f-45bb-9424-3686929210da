<#
    .NOTES
    File Name: Join-DomainWithLocalAdmins.ps1
    Author: <PERSON><PERSON>
    Version: 1.0.0 - 03/09/2025
    Description: Joins EC2 instance to domain and configures local administrators
    Note: This script is designed to be called from SSM Automation runbooks

    .NOTES
    Version 1.0.0     - Base Script
#>

param(
    [Parameter(Mandatory=$true)]
    [string]$Domain,
    
    [Parameter(Mandatory=$true)]
    [string]$SecretsManagerSecretArn,
    
    [Parameter(Mandatory=$true)]
    [array]$LocalAdminGroups,
    
    [Parameter(Mandatory=$false)]
    [string]$ComputerName = "",
    
    [Parameter(Mandatory=$false)]
    [string]$TargetOU = "",
    
    [Parameter(Mandatory=$false)]
    [string]$Region = "af-south-1",
    
    [Parameter(Mandatory=$false)]
    [switch]$RestartAfterJoin = $true,
    
    [Parameter(Mandatory=$false)]
    [switch]$OutputJson = $false
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Function to write structured log messages
function Write-LogMessage {
    param(
        [string]$Message,
        [ValidateSet("INFO", "WARN", "ERROR", "SUCCESS")]
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    switch ($Level) {
        "INFO" { Write-Host $logMessage -ForegroundColor Cyan }
        "WARN" { Write-Host $logMessage -ForegroundColor Yellow }
        "ERROR" { Write-Host $logMessage -ForegroundColor Red }
        "SUCCESS" { Write-Host $logMessage -ForegroundColor Green }
    }
}

# Function to get domain credentials from Secrets Manager
function Get-DomainCredentials {
    param(
        [string]$SecretArn,
        [string]$Region
    )
    
    try {
        Write-LogMessage "Retrieving domain credentials from Secrets Manager" "INFO"
        
        # Get secret value using AWS PowerShell module
        $secretValue = Get-SECSecretValue -SecretId $SecretArn -Region $Region
        $secretJson = $secretValue.SecretString | ConvertFrom-Json
        
        # Validate required fields
        if (-not $secretJson.domainJoinUserName) {
            throw "domainJoinUserName not found in secret"
        }
        
        if (-not $secretJson.domainJoinPassword) {
            throw "domainJoinPassword not found in secret"
        }
        
        # Create credential object
        $securePassword = $secretJson.domainJoinPassword | ConvertTo-SecureString -AsPlainText -Force
        $credential = New-Object System.Management.Automation.PSCredential($secretJson.domainJoinUserName, $securePassword)
        
        Write-LogMessage "Successfully retrieved domain credentials" "SUCCESS"
        return @{
            Credential = $credential
            Username = $secretJson.domainJoinUserName
            DomainName = if ($secretJson.domainName) { $secretJson.domainName } else { $Domain }
        }
        
    } catch {
        Write-LogMessage "Failed to retrieve domain credentials: $($_.Exception.Message)" "ERROR"
        throw
    }
}

# Function to check current domain membership
function Test-DomainMembership {
    try {
        Write-LogMessage "Checking current domain membership" "INFO"
        
        $computerSystem = Get-WmiObject -Class Win32_ComputerSystem
        
        if ($computerSystem.PartOfDomain) {
            Write-LogMessage "Computer is already domain-joined to: $($computerSystem.Domain)" "INFO"
            return @{
                IsDomainJoined = $true
                CurrentDomain = $computerSystem.Domain
                ComputerName = $computerSystem.Name
            }
        } else {
            Write-LogMessage "Computer is not domain-joined (workgroup: $($computerSystem.Workgroup))" "INFO"
            return @{
                IsDomainJoined = $false
                CurrentWorkgroup = $computerSystem.Workgroup
                ComputerName = $computerSystem.Name
            }
        }
        
    } catch {
        Write-LogMessage "Failed to check domain membership: $($_.Exception.Message)" "ERROR"
        throw
    }
}

# Function to join domain
function Join-DomainSafe {
    param(
        [string]$DomainName,
        [System.Management.Automation.PSCredential]$Credential,
        [string]$ComputerName,
        [string]$TargetOU
    )
    
    try {
        Write-LogMessage "Joining domain: $DomainName" "INFO"
        
        # Build join parameters
        $joinParams = @{
            DomainName = $DomainName
            Credential = $Credential
            Force = $true
        }
        
        # Add computer name if specified
        if ($ComputerName) {
            $joinParams.NewName = $ComputerName
            Write-LogMessage "Setting computer name to: $ComputerName" "INFO"
        }
        
        # Add OU if specified
        if ($TargetOU) {
            $joinParams.OUPath = $TargetOU
            Write-LogMessage "Joining to OU: $TargetOU" "INFO"
        }
        
        # Perform domain join
        Add-Computer @joinParams -ErrorAction Stop
        
        Write-LogMessage "Successfully joined domain: $DomainName" "SUCCESS"
        return $true
        
    } catch {
        Write-LogMessage "Failed to join domain: $($_.Exception.Message)" "ERROR"
        throw
    }
}

# Function to add local administrator groups
function Add-LocalAdministrators {
    param(
        [array]$AdminGroups
    )
    
    try {
        Write-LogMessage "Adding local administrator groups" "INFO"
        
        $addedGroups = @()
        $skippedGroups = @()
        $failedGroups = @()
        
        foreach ($group in $AdminGroups) {
            try {
                Write-LogMessage "Processing admin group: $group" "INFO"
                
                # Check if group already exists in local administrators
                $localAdmins = Get-LocalGroupMember -Group "Administrators" -ErrorAction SilentlyContinue
                $existingMember = $localAdmins | Where-Object { $_.Name -eq $group -or $_.Name -like "*\$group" }
                
                if ($existingMember) {
                    Write-LogMessage "Group '$group' is already a local administrator" "INFO"
                    $skippedGroups += $group
                } else {
                    # Add group to local administrators
                    Add-LocalGroupMember -Group "Administrators" -Member $group -ErrorAction Stop
                    Write-LogMessage "Successfully added '$group' to local administrators" "SUCCESS"
                    $addedGroups += $group
                }
                
            } catch {
                Write-LogMessage "Failed to add group '$group' to local administrators: $($_.Exception.Message)" "WARN"
                $failedGroups += @{
                    Group = $group
                    Error = $_.Exception.Message
                }
            }
        }
        
        return @{
            AddedGroups = $addedGroups
            SkippedGroups = $skippedGroups
            FailedGroups = $failedGroups
            TotalProcessed = $AdminGroups.Count
        }
        
    } catch {
        Write-LogMessage "Failed to process local administrator groups: $($_.Exception.Message)" "ERROR"
        throw
    }
}

# Main execution logic
try {
    Write-LogMessage "Starting Domain Join and Local Administrator Configuration" "INFO"
    Write-LogMessage "Domain: $Domain, Local Admin Groups: $($LocalAdminGroups -join ', ')" "INFO"

    # Step 1: Check current domain membership
    $currentStatus = Test-DomainMembership

    # Step 2: Get domain credentials from Secrets Manager
    $domainCreds = Get-DomainCredentials -SecretArn $SecretsManagerSecretArn -Region $Region

    # Use domain from secret if available
    $effectiveDomain = if ($domainCreds.DomainName) { $domainCreds.DomainName } else { $Domain }

    # Step 3: Determine if domain join is needed
    $needsDomainJoin = $false
    if (-not $currentStatus.IsDomainJoined) {
        $needsDomainJoin = $true
        Write-LogMessage "Computer is not domain-joined - domain join required" "INFO"
    } elseif ($currentStatus.CurrentDomain -ne $effectiveDomain) {
        $needsDomainJoin = $true
        Write-LogMessage "Computer is joined to different domain ($($currentStatus.CurrentDomain)) - rejoin required" "WARN"
    } else {
        Write-LogMessage "Computer is already joined to correct domain: $effectiveDomain" "SUCCESS"
    }

    # Step 4: Perform domain join if needed
    $domainJoinResult = @{
        WasRequired = $needsDomainJoin
        Success = $false
        PreviousDomain = $currentStatus.CurrentDomain
        NewDomain = $effectiveDomain
    }

    if ($needsDomainJoin) {
        try {
            Join-DomainSafe -DomainName $effectiveDomain -Credential $domainCreds.Credential -ComputerName $ComputerName -TargetOU $TargetOU
            $domainJoinResult.Success = $true
        } catch {
            $domainJoinResult.Error = $_.Exception.Message
            throw "Domain join failed: $($_.Exception.Message)"
        }
    } else {
        $domainJoinResult.Success = $true
    }

    # Step 5: Configure local administrators
    $adminResult = Add-LocalAdministrators -AdminGroups $LocalAdminGroups

    # Step 6: Build final result object
    $finalResult = @{
        Success = $true
        Domain = $effectiveDomain
        ComputerName = if ($ComputerName) { $ComputerName } else { $env:COMPUTERNAME }
        TargetOU = $TargetOU

        # Domain join details
        DomainJoin = $domainJoinResult

        # Local administrator details
        LocalAdministrators = $adminResult

        # Metadata
        SecretArn = $SecretsManagerSecretArn
        DomainUser = $domainCreds.Username
        RestartScheduled = $RestartAfterJoin -and $needsDomainJoin
        Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss UTC"
    }

    # Step 7: Schedule restart if domain join occurred and restart is requested
    if ($needsDomainJoin -and $RestartAfterJoin) {
        Write-LogMessage "Scheduling system restart in 60 seconds due to domain join" "WARN"
        shutdown /r /t 60 /c "Restarting after domain join - AWS SSM Automation"
        $finalResult.RestartScheduled = $true
    }

    # Output results
    if ($OutputJson) {
        Write-Output ($finalResult | ConvertTo-Json -Depth 10)
    } else {
        Write-LogMessage "=== DOMAIN JOIN AND LOCAL ADMIN CONFIGURATION COMPLETED ===" "SUCCESS"
        Write-LogMessage "Domain: $effectiveDomain" "INFO"
        Write-LogMessage "Domain Join Required: $needsDomainJoin" "INFO"
        Write-LogMessage "Domain Join Success: $($domainJoinResult.Success)" "INFO"
        Write-LogMessage "Local Admins Added: $($adminResult.AddedGroups.Count)" "INFO"
        Write-LogMessage "Local Admins Skipped: $($adminResult.SkippedGroups.Count)" "INFO"
        Write-LogMessage "Local Admins Failed: $($adminResult.FailedGroups.Count)" "INFO"
        if ($finalResult.RestartScheduled) {
            Write-LogMessage "System restart scheduled in 60 seconds" "WARN"
        }
    }

    # Return result for use in runbook
    return $finalResult

} catch {
    Write-LogMessage "Domain join and local admin configuration failed: $($_.Exception.Message)" "ERROR"

    $errorResult = @{
        Success = $false
        Error = $true
        Message = $_.Exception.Message
        Domain = $Domain
        ComputerName = if ($ComputerName) { $ComputerName } else { $env:COMPUTERNAME }
        LocalAdminGroups = $LocalAdminGroups
        ScriptName = $MyInvocation.MyCommand.Name
        LineNumber = $_.InvocationInfo.ScriptLineNumber
        Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss UTC"
    }

    if ($OutputJson) {
        Write-Output ($errorResult | ConvertTo-Json -Depth 5)
    }

    exit 1
}
