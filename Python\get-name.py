import json
import requests

api_url = "http://localhost:8030/getname"

headers = {'content-type': 'application/json'}
payload = {"object_type": "SRV", "reference": "AWS-TEST"}
r = requests.post(api_url,headers=headers,data=json.dumps(payload) )
#print(f'\nRESPONSE\nStatus: {r.status_code}')
content = json.loads(r.content)
print(f'getData >> RESPONSE: {r.status_code}\n{json.dumps(content, indent=2)}\n')