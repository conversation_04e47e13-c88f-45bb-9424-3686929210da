# Deployment Guide

This guide provides step-by-step instructions for deploying the AWS EC2 Deployment Automation system.

## Prerequisites Checklist

Before starting the deployment, ensure you have:

- [ ] AWS CLI configured with appropriate permissions
- [ ] PowerShell 5.1 or later
- [ ] Access to target AWS account
- [ ] Domain service account credentials
- [ ] ImageBuilder images available
- [ ] S3 bucket for configurations
- [ ] VPC and subnets configured

## Step 1: Prepare Infrastructure

### 1.1 Create S3 Bucket

```bash
# Create S3 bucket for configurations
aws s3 mb s3://your-ec2-deployment-config --region af-south-1

# Enable versioning
aws s3api put-bucket-versioning \
    --bucket your-ec2-deployment-config \
    --versioning-configuration Status=Enabled

# Enable encryption
aws s3api put-bucket-encryption \
    --bucket your-ec2-deployment-config \
    --server-side-encryption-configuration '{
        "Rules": [{
            "ApplyServerSideEncryptionByDefault": {
                "SSEAlgorithm": "AES256"
            }
        }]
    }'
```

### 1.2 Create Secrets Manager Secret

```bash
# Create domain join secret
aws secretsmanager create-secret \
    --name "ec2-deployment-domain-credentials" \
    --description "Domain join credentials for EC2 deployment automation" \
    --secret-string '{
        "domainJoinUserName": "DOMAIN\\ServiceAccount",
        "domainJoinPassword": "YourSecurePassword",
        "domainName": "yourdomain.com",
        "defaultTargetOU": "OU=Servers,OU=Default,DC=yourdomain,DC=com"
    }' \
    --region af-south-1
```

## Step 2: Deploy CloudFormation Stacks

### 2.1 Deploy IAM Roles Stack

```bash
aws cloudformation deploy \
    --template-file CloudFormation/EC2-Deployment-IAM-Roles.yaml \
    --stack-name ec2-deployment-iam \
    --parameter-overrides \
        S3ConfigBucketName=your-ec2-deployment-config \
        SecretsManagerSecretArn=arn:aws:secretsmanager:af-south-1:************:secret:ec2-deployment-domain-credentials-AbCdEf \
        Region=af-south-1 \
    --capabilities CAPABILITY_NAMED_IAM \
    --region af-south-1
```

### 2.2 Deploy SSM Document Stack

```bash
aws cloudformation deploy \
    --template-file CloudFormation/EC2-Deployment-SSM-Document.yaml \
    --stack-name ec2-deployment-ssm \
    --parameter-overrides \
        DocumentName=EC2-Deployment-Automation \
        AutomationRoleArn=arn:aws:iam::************:role/EC2-Deployment-Automation-Role-af-south-1 \
        S3ConfigBucket=your-ec2-deployment-config \
    --region af-south-1
```

## Step 3: Upload Configuration Files

### 3.1 Upload Base Configuration

```bash
# Upload base configuration
aws s3 cp configs/base_config.json \
    s3://your-ec2-deployment-config/configs/ \
    --region af-south-1
```

### 3.2 Upload Specific Configurations

```bash
# Upload all specific configurations
aws s3 sync configs/ \
    s3://your-ec2-deployment-config/configs/ \
    --exclude "base_config.json" \
    --region af-south-1
```

### 3.3 Upload Scripts

```bash
# Upload all PowerShell scripts
aws s3 sync Scripts/ \
    s3://your-ec2-deployment-config/scripts/ \
    --region af-south-1
```

### 3.4 Upload Applications (Optional)

```bash
# Upload application installers
aws s3 sync applications/ \
    s3://your-ec2-deployment-config/applications/ \
    --region af-south-1
```

## Step 4: Validate Configuration

### 4.1 AWS Systems Manager Validation Runbook (Recommended)

Use the comprehensive validation runbook to test configuration and AD integration:

```bash
# Execute Configuration and AD Validation
aws ssm start-automation-execution \
    --document-name "Config-and-AD-Validation" \
    --parameters \
        AssetOwner=Sanlam \
        AppType=Shared \
        Client=SGT \
        Environment=DEV \
        OSVersion="Windows Server 2022" \
        S3ConfigBucket=your-ec2-deployment-config \
        SecretsManagerSecretArn=arn:aws:secretsmanager:af-south-1:account:secret:domain-creds \
        TestMode=true \
        ComputerName="" \
        Region=af-south-1 \
    --region af-south-1
```

**What this validates:**
- ✅ Configuration file loading and validation
- ✅ Parameter validation against base configuration
- ✅ Secrets Manager access
- ✅ Webhook API connectivity
- ✅ AD object creation simulation (test mode)
- ✅ Overall system readiness

**Test Mode Benefits:**
- Safe testing without creating actual AD objects
- Validates all connectivity and configuration
- Provides detailed logging and error reporting

### 4.2 PowerShell Configuration Testing

For local testing and development:

```powershell
# Test configuration validation
.\Scripts\Configuration\Test-DeploymentConfiguration.ps1 `
    -S3BucketName "your-ec2-deployment-config" `
    -ValidateAllConfigs `
    -OutputReport
```

### 4.3 Test Individual Configuration

```powershell
# Test specific configuration
.\Scripts\Configuration\Get-DeploymentConfiguration.ps1 `
    -AssetOwner "Sanlam" `
    -AppType "Shared" `
    -Client "SGT" `
    -Environment "DEV" `
    -OSVersion "Windows Server 2022" `
    -S3BucketName "your-ec2-deployment-config" `
    -ValidateOnly
```

## Step 5: Execute Test Deployment

### 5.1 Start Automation Execution

```bash
aws ssm start-automation-execution \
    --document-name "EC2-Deployment-Automation" \
    --parameters \
        AssetOwner=Sanlam \
        AppType=Shared \
        Client=SGT \
        Environment=DEV \
        OSVersion="Windows Server 2022" \
        ImageBuilderImageArn=arn:aws:imagebuilder:af-south-1:************:image/windows-server-2022-base/1.0.0/1 \
        InstanceType=t3.medium \
        SubnetId=subnet-12345678 \
        SecurityGroupIds=sg-12345678 \
        KeyName=your-key-pair \
        S3ConfigBucket=your-ec2-deployment-config \
        SecretsManagerSecretArn=arn:aws:secretsmanager:af-south-1:************:secret:ec2-deployment-domain-credentials-AbCdEf \
        IamInstanceProfile=EC2-Deployment-Instance-Profile-af-south-1 \
    --region af-south-1
```

### 5.2 Monitor Execution

```bash
# Get execution ID from previous command output
EXECUTION_ID="12345678-1234-1234-1234-************"

# Monitor execution status
aws ssm describe-automation-executions \
    --filters "Key=ExecutionId,Values=$EXECUTION_ID" \
    --region af-south-1

# Get detailed step information
aws ssm get-automation-execution \
    --automation-execution-id $EXECUTION_ID \
    --region af-south-1
```

## Step 6: Verify Deployment

### 6.1 Check Instance Status

```bash
# Get instance ID from automation output
INSTANCE_ID="i-1234567890abcdef0"

# Check instance status
aws ec2 describe-instances \
    --instance-ids $INSTANCE_ID \
    --region af-south-1
```

### 6.2 Verify Domain Join

```powershell
# Connect to instance and verify domain membership
Invoke-Command -ComputerName $InstanceIP -Credential $DomainCreds -ScriptBlock {
    Get-WmiObject -Class Win32_ComputerSystem | Select-Object Domain, PartOfDomain
}
```

### 6.3 Check Active Directory

```powershell
# Verify computer object in AD
Get-ADComputer -Identity "ComputerName" -Server "domain.com"
```

## Step 7: Production Deployment

### 7.1 Environment-Specific Parameters

For production deployments, adjust parameters accordingly:

```bash
# Production deployment example
aws ssm start-automation-execution \
    --document-name "EC2-Deployment-Automation" \
    --parameters \
        AssetOwner=Sanlam \
        AppType=MSSQL \
        Client=SPF \
        Environment=PRD \
        OSVersion="Windows Server 2022" \
        ImageBuilderImageArn=arn:aws:imagebuilder:af-south-1:************:image/windows-server-2022-sql/1.0.0/1 \
        InstanceType=m5.xlarge \
        SubnetId=subnet-prod-12345678 \
        SecurityGroupIds=sg-prod-12345678,sg-sql-12345678 \
        KeyName=prod-key-pair \
        S3ConfigBucket=your-ec2-deployment-config \
        SecretsManagerSecretArn=arn:aws:secretsmanager:af-south-1:************:secret:ec2-deployment-domain-credentials-AbCdEf \
        IamInstanceProfile=EC2-Deployment-Instance-Profile-af-south-1 \
        ComputerName=SQLPRD001 \
    --region af-south-1
```

## Step 8: Automation and Scheduling

### 8.1 Create EventBridge Rule (Optional)

```bash
# Create rule for scheduled deployments
aws events put-rule \
    --name "ec2-deployment-schedule" \
    --schedule-expression "cron(0 2 * * ? *)" \
    --description "Daily EC2 deployment automation" \
    --region af-south-1

# Add SSM target
aws events put-targets \
    --rule "ec2-deployment-schedule" \
    --targets "Id"="1","Arn"="arn:aws:ssm:af-south-1:************:automation-definition/EC2-Deployment-Automation","RoleArn"="arn:aws:iam::************:role/EC2-Deployment-Automation-Role-af-south-1" \
    --region af-south-1
```

### 8.2 Lambda Integration (Optional)

Create Lambda function to trigger deployments based on custom logic:

```python
import boto3
import json

def lambda_handler(event, context):
    ssm = boto3.client('ssm', region_name='af-south-1')
    
    response = ssm.start_automation_execution(
        DocumentName='EC2-Deployment-Automation',
        Parameters={
            'AssetOwner': event['AssetOwner'],
            'AppType': event['AppType'],
            'Client': event['Client'],
            'Environment': event['Environment'],
            'OSVersion': event['OSVersion'],
            # ... other parameters
        }
    )
    
    return {
        'statusCode': 200,
        'body': json.dumps({
            'ExecutionId': response['AutomationExecutionId']
        })
    }
```

## Rollback Procedures

### 8.1 Stop Running Automation

```bash
# Stop automation execution
aws ssm stop-automation-execution \
    --automation-execution-id $EXECUTION_ID \
    --type "Cancel" \
    --region af-south-1
```

### 8.2 Cleanup Resources

```bash
# Terminate instance
aws ec2 terminate-instances \
    --instance-ids $INSTANCE_ID \
    --region af-south-1

# Remove AD computer object (manual step)
# Remove-ADComputer -Identity "ComputerName" -Confirm:$false
```

## Monitoring and Maintenance

### 8.1 CloudWatch Dashboards

Create dashboards to monitor:
- Automation execution success rates
- Instance deployment times
- Error patterns
- Resource utilization

### 8.2 Regular Maintenance Tasks

1. **Weekly**: Review failed executions and resolve issues
2. **Monthly**: Update configurations and test changes
3. **Quarterly**: Review and update IAM permissions
4. **Annually**: Rotate service account credentials

## Next Steps

After successful deployment:

1. Set up monitoring and alerting
2. Create operational runbooks
3. Train team members on the system
4. Implement change management processes
5. Plan for disaster recovery scenarios

For troubleshooting guidance, see [Troubleshooting-Guide.md](Troubleshooting-Guide.md).
